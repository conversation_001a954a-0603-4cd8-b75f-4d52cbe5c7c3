# 工程代码结构设计

## 项目架构概述

allin-silas-backend 项目采用了阿里巴巴 MVC+Manager 分层混合架构，结合了传统三层架构、DDD
领域驱动设计的部分思想，形成了一套适合业务特点的分层结构。项目主要分为以下几层：

```python
# 以知识百科为例
com.allin.silas.kn # 模块/限界上下文
├── client         # 对外客户端层
│   ├── event      # 对外发布的消息事件
│   ├── dto        # 数据传输对象
├── adapter        # 适配层/请求处理层
│   ├── controller # 控制器
│   ├── dto        # 数据传输对象
│   └── vo         # 视图对象
├── app            # 应用层/业务处理层
│   ├── entity     # 领域实体
│   ├── manager    # 通用逻辑层
│   │   └── impl   # 通用逻辑实现
│   └── service    # 业务服务层
│       └── impl   # 业务服务实现
└── infra          # 基础设施层
    └── repository # 数据访问层
```

## 旧鸟情系统痛点 - 过度耦合

下述问题，归根到底在于系统架构不清晰，划分出来的模块内聚度低、高耦合。

### 当代码沦为“数据库操作说明书”

在旧鸟情系统的传统三层架构实践中，做项目为了追求短平快，而忽略了代码结构设计，例如都习惯使用代码生成器一次性创建
controller、service、mapper 等。

这导致大多数 Service 类变成了数据库操作的简单封装，这种按数据表划分 Service 的方式，使得 Service
层沦为数据访问层的简单代理，失去了业务逻辑聚合的作用，代码更像是 "如何操作数据库" 的说明书，而非业务规则的体现。

### 业务逻辑被 "撕碎" 成零散片段

从旧鸟情系统中来看业务逻辑被分散在不同的模块和服务中。例如：

- 鸟情监测相关的业务逻辑可能分散在 patrol 、 warning 、 report 等多个包中
- 目标信息、目标统计、目标风险统计等功能都混杂在一起，导致修改一处可能影响多个功能模块。

这种分散导致完整的业务流程难以追踪，我们常常需要在多个 Service 类之间跳转才能理解一个完整的业务流程。

## 模块划分/限界上下文

我们可以将限界上下文拆解为两个词：限界和上下文。限界就是领域的边界，而上下文则是语义环境。

狭义上它是为了避免同样的概念或语义在不同的上下文环境中产生歧义，广义上限界上下文是用来保证模块内高内聚低耦合。

通常来说，一个限界上下文之内只有一个领域，可以是一个子域或者多个子域的集合。

在微服务架构中限界上下文是微服务拆分的依据，即每个限界上下文对应一个微服务。

如何进行划分，一个有效的方法是一个限界上下文必须支持一个完整的业务流程，保证这个业务流程所涉及的领域都在一个限界上下文中。

在代码中的体现如下：

- 同一限界上下文中，尽量使用函数依赖或者实体依赖，这样方便梳理逻辑和追踪代码。
- 不同限界上下文中，尽量只依赖数据或者消息，消灭实体依赖，防止改动的蔓延和放大。

### 通用模块（common）
跨业务领域的，比如通用的配置、工具类、共享的领域事件或外部服务

## 模块分层设计详解

在模块分层上参考 COLA 架构，因为没有采用 DDD 所以移除 domain 模块，保留其他四个模块
![img_4.png](img_4.png)

### 对外客户端层 （client）

负责服务对外的 API 和 DTO，例如 client 接口、 event 事件、提供的序列化 annotation 注解等

### 适配层/请求处理层 （adapter）

借鉴了 DDD 中的适配器概念，负责对前端展示（web，wireless，wap）的路由和适配，对于传统 B/S 系统而言，adapter 就相当于 MVC 中的
controller 层。

实现特点：

- 控制器类命名采用 XXXController 形式，如 KnBirdInfoController
- 使用 DTO （Data Transfer Object） 接收前端数据，如 EditBirdInfoDto
- 使用 VO （View Object） 返回前端数据，如 KnBirdInfoVo
- 控制器方法简洁，只负责参数校验和结果包装，不包含业务逻辑

```java

@RestController
@RequestMapping("/kn/bird")
class KnBirdInfoController {
  private final KnBirdInfoQueryService birdInfoQueryService;

  private final KnBirdInfoCommandService birdInfoCommandService;

  // 查询方法
  @GetMapping("/info/{chineseName}")
  Result<KnBirdInfoVo> info(@PathVariable String chineseName) {
    return Result.ok(birdInfoQueryService.info(chineseName));
  }

  // 命令方法
  @PutMapping("/info")
  Result<Void> editInfo(@Validated @RequestBody EditBirdInfoDto editInfoDto) {
    birdInfoCommandService.editInfo(editInfoDto);
    return Result.ok();
  }
}
```

进一步分包设计可以将 controller 按发起请求的客户端分为 app、web 等

### 应用层 （app）

这一层结合了传统 MVC 的 Service 层和 DDD 的应用层概念，但进一步细分为 service 和 manager 两个子层。

#### Service 业务处理层

应用层包含了应用的业务逻辑、规则和策略, 它依赖基础设施层的资源

实现特点：
- 采用 CQRS 模式，将查询和命令分离，职责分离能带给我们更多的架构属性选择。
  ![img_3.png](img_3.png)
  - 查询服务： KnBirdInfoQueryService 、 KnInsectInfoQueryService
  - 命令服务： KnBirdInfoCommandService 、 KnInsectInfoCommandService
- 每个 Controller 方法对应一个 Service 方法，保持业务流程的清晰
- Service 实现类负责业务流程编排，调用 Manager 层的原子服务

```java

@Service
public class KnInsectInfoCommandServiceImpl implements KnInsectInfoCommandService {
  private final KnInsectInfoManager insectInfoManager;

  @Transactional(rollbackFor = Exception.class)
  @Override
  public synchronized void editInfo(EditInsectInfoDto editInfoDto) {
    // 业务流程编排，调用多个 Manager 方法完成一个业务操作
    insectInfoManager.editAlias(editInfoDto.getChineseName(), editInfoDto.getAliases());
    insectInfoManager.editActiveMonths(editInfoDto.getChineseName(), editInfoDto.getActiveMonths());
    // ... 其他操作
  }
}
```

#### Manager 逻辑复用层

这是阿里巴巴分层架构特有的一层，提供可复用的原子服务。

实现特点：

- 提供领域对象的原子操作，如 KnBirdInfoManager 、 KnInsectInfoManager
- 封装通用业务逻辑，避免 Service 层代码重复
- 使用泛型和函数式接口提高代码复用性
- 解决 Service 之间的循环依赖问题

示例代码：

```java

@Service
public class KnBirdInfoManagerImpl implements KnBirdInfoManager {
  // 通用的填充扩展信息方法，使用泛型和函数式接口提高复用性
  @Override
  public <T> void fillExt(T birdInfo, Function<T, String> nameGetter, BiConsumer<T, KnBirdInfoExt> setter) {
    if (birdInfo == null) {
      return;
    }
    final String name = nameGetter.apply(birdInfo);
    // 查询扩展信息
    KnBirdInfoExt infoExt = birdInfoExtMapper.selectOne(/* ... */);
    // 填充属性
    setter.accept(birdInfo, infoExt);
  }

  // 原子操作方法
  @Override
  public synchronized void setLocal(String chineseName) {
    // 实现纳入本场的逻辑
  }
}
```

#### 领域实体

目前的设想是在可允许的情况下使用贫血模型，举个最简单的例子，假设一个实体类中有字段创建时间，我们的业务需要计算注册时长，这种非常简单的依靠实体自身属性就能得到的值，那么其实直接在实体中添加一个计算方法即可，完全不需要在
Service 或者 manager 中在声明一个方法。

实现特点：

- 实体类与数据库表结构对应，如 KnBirdInfoBase 、 KnInsectInfoBase
- 使用 MyBatis Plus 注解映射数据库表
- 实体类放在 app 包下而非 infra 包下，体现领域实体模型的重要性

补充概念：

- 失血模型：模型仅包含数据的定义和 getter/setter 方法，业务逻辑和应用逻辑都放到服务层中。这种类在 Java 中叫 POJO。
- 贫血模型：贫血模型中包含了一些业务逻辑，但不包含依赖持久层的业务逻辑。这部分依赖于持久层的业务逻辑将会放到服务层中。

### 基础设施层 （infra）

基础设施层主要职责是实现应用层所需要的技术服务，它提供各种「技术手段」来支撑业务，比如通信、数据库访问、文件系统、消息队列等等。
例如实现领域层中定义的仓储接口对应传统 MVC 的 DAO 层，使核心业务逻辑和技术细节分离，区分业务部分和非业务部分。

实现特点：

- 使用 Repository 模式，接口命名为 XXXMapper
- 基于 MyBatis Plus 实现数据访问
- 在接口中定义默认方法，简化简单查询操作

```java

@Mapper
public interface KnBirdInfoExtMapper extends BaseMapper<KnBirdInfoExt> {
  /**
   * 查询扩展信息
   */
  default KnBirdInfoExt getByName(String chineseName, String projectId) {
    return selectOne(Wrappers.lambdaQuery(KnBirdInfoExt.class)
            .eq(KnBirdInfoExt::getChineseName, chineseName)
            .eq(KnBirdInfoExt::getProjectId, projectId));
  }
}
```

在这里我们减少了仓储层的结构设计，还是采用的传统三层架构的方式由 Service 或者 Manager 直接调用具体的 ORM 框架，感兴趣的可以参考
COLA 架构中的设计，它通过在业务处理层定义仓储接口的方式实现高层模块不依赖于低层模块，体现了面向接口编程的重要性

## 架构优势与解决的问题

### 解决传统三层架构的问题

1. 解决代码重复问题 ：

- 传统 MVC 中，Service 层代码往往重复度高
- Manager 层提取通用逻辑，如 fillExt 、 batchFillAlias 等方法，大幅减少重复代码

2. 解决循环依赖问题 ：

- 传统 MVC 中，Service 之间相互调用容易形成循环依赖
- Manager 层作为中间层，避免了 Service 之间的直接调用

3. 提高代码可维护性 ：

- 传统 MVC 中，Controller 和 Service 职责不清晰
- 明确的分层和职责划分，使代码结构更清晰，易于维护

4. 提高代码复用性 ：

- 传统 MVC 中，复用性较低
- Manager 层提供的通用方法和泛型设计，大大提高了代码复用性

### 借鉴 DDD 的优势

1. 领域模型驱动 ：

- 实体类设计反映业务领域概念，如鸟种、虫种等
- 实体类包含业务属性，而不仅仅是数据库表映射

2. 关注点分离 ：

- 采用 CQRS 模式，将查询和命令分离
- 查询服务和命令服务各司其职，代码更加内聚

3. 适配器模式 ：

- adapter 层作为外部接口适配层，隔离核心业务逻辑
- DTO 和 VO 对象明确区分输入和输出

### 借鉴阿里巴巴分层架构的优势

1. Manager 层的引入 ：

- 提供原子服务，增强代码复用性
- 解决 Service 层代码膨胀问题

2. 分包命名规则的优化 ：

- 使用 adapter、app、infra 等命名，更符合领域驱动设计思想
- 比传统的 controller、service、dao 命名更能体现层次职责

## 总结

allin-silas-backend 项目的代码结构设计巧妙地结合了传统三层架构、DDD 领域驱动设计和阿里巴巴分层架构的优点，形成了一套适合业务特点的分层结构。

目前而言，我们通过引入 Manager 层和采用 CQRS
模式，解决了传统三层架构中的代码重复、循环依赖等问题，提高了代码的可维护性和复用性。后续我们会继续根据需求变化不断演化代码结构设计，以实现低成本响应需求变更。