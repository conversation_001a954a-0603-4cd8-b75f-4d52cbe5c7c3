package com.allin.silas.visual.adapter.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.SecureUtil;
import com.allin.silas.visual.adapter.dto.DonwloadZipDto;
import com.allin.silas.visual.app.service.VisualTargetInfoQueryService;
import com.allin.silas.visual.constant.FileStorageConstants;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import com.allin.view.base.utils.file.FileNameUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.FileCopyUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 探测识别/可视目标图片
 *
 * <AUTHOR>
 * @since 2025/7/2
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/visual/img")
public class VisualImageController {

    private final VisualTargetInfoQueryService infoQueryService;

    public VisualImageController(VisualTargetInfoQueryService infoQueryService) {
        this.infoQueryService = infoQueryService;
    }

    /**
     * 查看切片
     *
     * @param date        日期 yyyy-MM-dd
     * @param batchNumber 批次号
     * @param path        相对路径
     */
    @GetMapping
    public ResponseEntity<?> imgDownload(@RequestParam LocalDate date,
                                         @RequestParam String batchNumber,
                                         @RequestParam String path) {
        // 规范化路径并防止路径穿越
        Path baseDir = Paths.get(FileStorageConstants.visualImageFileDirPath).toAbsolutePath().normalize();
        Path filePath = Paths.get(FileStorageConstants.visualImageFileDirPath, path).toAbsolutePath().normalize();

        if (!filePath.startsWith(baseDir)) {
            log.error("非法路径{}", path);
            return ResponseEntity.notFound().build();
        }

        // 包含非法字符
        if (path.matches("(^|/|\\\\)(\\.\\.|~|\\*|\\$)(/|\\\\|$)")) {
            log.error("非法路径{}", path);
            return ResponseEntity.notFound().build();
        }

        // 白名单校验
        String fileExtension = FileUtil.extName(filePath.toString()).toLowerCase();
        if (!List.of("jpg", "jpeg", "png", "bmp", "gif").contains(fileExtension)) {
            log.error("不允许的文件类型:{}", fileExtension);
            return ResponseEntity.notFound().build();
        }

        // 配置缓存，7天
        CacheControl cacheControl = CacheControl.maxAge(7, TimeUnit.DAYS)
                .cachePrivate()
                .noTransform()
                .mustRevalidate();

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlAllowOrigin("*");
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDisposition(ContentDisposition.inline().build());

        FileSystemResource resource = new FileSystemResource(filePath.toString());
        if (resource.exists()) {
            return ResponseEntity.ok()
                    .headers(headers)
                    .cacheControl(cacheControl)
                    .eTag(SecureUtil.md5(date + batchNumber + path))
                    .body(resource);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 切片打包下载
     */
    @PostMapping("/download/zip")
    public ResponseEntity<StreamingResponseBody> exportImages(@RequestBody @Validated DonwloadZipDto dto) {
        log.info("开始处理图片打包下载请求 - 日期: {}, 批次数量: {}", dto.getDate(), dto.getBatchNumbers().size());

        // 获取图片路径列表
        List<String> imgPaths = infoQueryService.listImgPath(dto.getDate(), dto.getBatchNumbers());
        if (imgPaths.isEmpty()) {
            throw new ValidationFailureException(I18nUtil.getMessage("visual.no.pictures.found"));
        }

        List<File> files = new ArrayList<>();
        for (String fileName : imgPaths) {
            files.add(new File(FileStorageConstants.visualImageFileDirPath, fileName));
        }

        // 生成唯一的临时文件名
        String zipFileName = FileNameUtils.buildTempAbsoluteFilePath("img.zip", true);

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentDisposition(ContentDisposition.attachment().filename(zipFileName).build());
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        // 使用 StreamingResponseBody 进行流式响应
        StreamingResponseBody responseBody = outputStream -> {
            File tempZipFile = ZipUtil.zip(new File(zipFileName), false, files.toArray(new File[0]));
            try {
                log.info("压缩包创建完成 - 文件: {}, 大小: {} bytes", tempZipFile.getName(), tempZipFile.length());
                // 将文件内容写入输出流
                FileCopyUtils.copy(new FileInputStream(tempZipFile), outputStream);
            } finally {
                // 清理临时文件
                FileUtil.del(tempZipFile);
            }
        };

        return ResponseEntity.ok()
                .headers(headers)
                .body(responseBody);
    }
}
