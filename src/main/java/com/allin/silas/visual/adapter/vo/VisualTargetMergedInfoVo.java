package com.allin.silas.visual.adapter.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 合并的可视目标信息表
 */
@Data
public class VisualTargetMergedInfoVo {

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 后端生成的批次号
     */
    private String backendBatchNumber;

    /**
     * 开始经度
     */
    private Double startLongitude;

    /**
     * 开始纬度
     */
    private Double startLatitude;

    /**
     * 结束经度
     */
    private Double endLongitude;

    /**
     * 结束纬度
     */
    private Double endLatitude;

    /**
     * 最大只数
     */
    private Integer maxTargetCount;

    /**
     * 离跑道最小距离
     */
    private Double minRunwayDistance;

    /**
     * 离跑道最大距离
     */
    private Double maxRunwayDistance;

    /**
     * 离跑道中心最小距离
     */
    private Double minRunwayCenterDistance;

    /**
     * 离跑道中心最大距离
     */
    private Double maxRunwayCenterDistance;

    /**
     * 开始飞行方向
     */
    private Float startFlightDirection;

    /**
     * 结束飞行方向
     */
    private Float endFlightDirection;

    /**
     * 最大高度
     */
    private Float maxHeight;

    /**
     * 细分识别类型,子类
     */
    private String detectSubType;

    /**
     * 主要识别类型,一级分类
     * @see com.allin.silas.visual.app.enums.DetectTypeEnums#code
     */
    private String detectType;

    /**
     * 人工标记类型
     * @see com.allin.silas.visual.app.enums.MarkTypeEnums#code
     */
    private String markType;

    /**
     * 人工标记子类型
     */
    private String markSubType;

    /**
     * 第一帧的切片地址
     */
    private String startImgUrl;

    /**
     * 最后一帧的切片地址
     */
    private String endImgUrl;

    /**
     * 危险等级
     *
     * @see com.allin.silas.kn.app.entity.KnBirdInfoDangerLevel
     */
    private Integer dangerLevel;

    /**
     * 危险分类, 1-3低中高
     */
    private Integer dangerClass;

    /**
     * 推送状态,0未推送,1已推送
     */
    private Integer pushStatus;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 默认情况下 dangerLevel=1时为1低危, dangerLevel=2,3时为2中危, dangerLevel=4,5时为3高危
     */
    public Integer getDangerClass() {
        if (dangerClass == null) {
            if (dangerLevel == 1) {
                return 1;
            }
            if (dangerLevel == 2 || dangerLevel == 3) {
                return 2;
            }
            if (dangerLevel == 4 || dangerLevel == 5) {
                return 3;
            }
        }
        return dangerClass;
    }
}