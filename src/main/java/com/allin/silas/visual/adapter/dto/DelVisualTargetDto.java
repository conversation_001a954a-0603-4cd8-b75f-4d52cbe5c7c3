package com.allin.silas.visual.adapter.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 删除可视目标信息
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
@Data
public class DelVisualTargetDto {

    /**
     * 日期, yyyy-MM-dd
     */
    @NotNull
    private LocalDate date;

    /**
     * 批次号
     */
    @NotEmpty
    private List<String> batchNumbers;
}
