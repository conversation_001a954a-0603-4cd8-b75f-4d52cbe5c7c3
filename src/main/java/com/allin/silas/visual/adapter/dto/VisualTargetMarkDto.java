package com.allin.silas.visual.adapter.dto;

import com.allin.silas.visual.app.enums.MarkTypeEnums;
import com.allin.view.base.enums.validator.IEnumValid;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 标记可视目标信息
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
@Data
public class VisualTargetMarkDto {

    /**
     * 日期, yyyy-MM-dd
     */
    @NotNull
    private LocalDate date;

    /**
     * 目标批次号
     */
    @NotBlank
    private String backendBatchNumber;

    /**
     * 人工标记类型,无人机/空飘物/鸟类/鸟群/飞机/违建/虚景
     *
     * @see MarkTypeEnums#code
     */
    @NotNull
    @IEnumValid(target = MarkTypeEnums.class)
    private String markType;

    /**
     * 手动标记子类型
     */
    @NotBlank
    private String markSubType;

    /**
     * 危险等级
     */
    @JsonIgnore
    private Integer dangerLevel;
}
