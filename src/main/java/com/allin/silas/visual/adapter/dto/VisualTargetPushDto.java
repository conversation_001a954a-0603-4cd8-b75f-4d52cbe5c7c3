package com.allin.silas.visual.adapter.dto;

import com.allin.silas.event.client.dto.TargetManualPushEventInvasionDto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 推送可视目标信息
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
@Data
public class VisualTargetPushDto {

    /**
     * 日期, yyyy-MM-dd
     */
    @NotNull
    private LocalDate date;

    /**
     * 目标批次号
     */
    @NotBlank
    private String backendBatchNumber;

    /**
     * 处置详情
     */
    @Valid
    @NotNull
    private TargetManualPushEventInvasionDto.ReportHandleEventInvasionDto handleEventInvasionDto;
}
