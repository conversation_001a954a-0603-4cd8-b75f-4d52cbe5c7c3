package com.allin.silas.visual.adapter.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.allin.silas.dev.detect.app.enums.DevDetectRunModeEnums;
import com.allin.silas.visual.app.enums.CollectTypeEnums;
import com.allin.view.base.enums.serializer.IEnumsChangeDesc;
import com.allin.view.base.execl.DefaultExportStyle;
import com.allin.view.base.execl.converter.IEnumsConverter;
import com.allin.view.base.execl.converter.IEnumsToStrConverter;
import com.allin.view.base.execl.converter.ListToStrConverter;
import com.allin.view.config.serialize.annotation.ApiFoxNoIgnore;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 合并的可视目标信息表
 */
@Data
public class VisualTargetInfoListVo extends DefaultExportStyle {

    /**
     * 设备编号
     */
    @ExcelProperty("设备编号")
    private String devNum;

    /**
     * 设备类型
     */
    @ExcelIgnore
    private String devType;

    /**
     * 采集类型
     *
     * @see CollectTypeEnums#code
     */
    @ExcelProperty(value = "采集类型", converter = IEnumsToStrConverter.class)
    @IEnumsConverter(enums = CollectTypeEnums.class)
    private Integer collectType;

    /**
     * 第一帧的切片地址
     */
    @ExcelProperty
    private String startImgUrl;

    /**
     * 后端生成的批次号
     */
    @ExcelProperty("批次号")
    private String backendBatchNumber;

    /**
     * 最大只数
     */
    @ExcelProperty("最大只数")
    private Integer maxTargetCount;

    /**
     * 离跑道最小距离, 单位米
     */
    @ExcelProperty("离跑道最小距离")
    private Double minRunwayDistance;

    /**
     * 离跑道最大距离, 单位米
     */
    @ExcelProperty("离跑道最大距离")
    private Double maxRunwayDistance;

    /**
     * 开始飞行方向
     */
    @ExcelProperty("开始飞行方向")
    private Float startFlightDirection;

    /**
     * 结束飞行方向
     */
    @ExcelProperty("结束飞行方向")
    private Float endFlightDirection;

    /**
     * 最小翼展
     */
    @ExcelProperty("最小翼展")
    private Float minWingSpan;

    /**
     * 最大翼展
     */
    @ExcelProperty("最大翼展")
    private Float maxWingSpan;

    /**
     * 离设备最小距离
     */
    @ExcelProperty("离设备最小距离")
    private Float minDistance;

    /**
     * 离设备最大距离
     */
    @ExcelProperty("离设备最大距离")
    private Float maxDistance;

    /**
     * 最小高度
     */
    @ExcelProperty("最小高度")
    private Float minHeight;

    /**
     * 最大高度
     */
    @ExcelProperty("最大高度")
    private Float maxHeight;

    /**
     * 最大置信度
     */
    @ExcelProperty("最大置信度")
    private Float maxConfidenceLevel;

    /**
     * 目标大小,0点目标,1面目标
     */
    @ExcelProperty("目标大小")
    private Integer targetSize;

    /**
     * 细分识别类型,子类
     */
    @ExcelProperty("细分识别类型")
    private String detectSubType;

    /**
     * 主要识别类型,一级分类
     */
    @ExcelProperty("主识别类型")
    private String detectType;

    /**
     * 人工标记类型,无人机/空飘物/鸟/鸟群/飞机/违建/虚景
     */
    @ExcelProperty(value = "人工标记主类型")
    private String markType;

    /**
     * 人工标记子类型
     */
    @ExcelProperty("人工标记子类型")
    private String markSubType;

    /**
     * 最后一帧的切片地址
     */
    @ExcelProperty
    private String endImgUrl;

    /**
     * 危险等级
     *
     * @see com.allin.silas.kn.app.entity.KnBirdInfoDangerLevel
     */
    @ExcelProperty("危险等级")
    private Integer dangerLevel;

    /**
     * 危险分类, 1-3低中高
     */
    @ExcelProperty(value = "危险分类", converter = IEnumsToStrConverter.class)
    @IEnumsConverter(replace = {"1_低危", "2_中危", "3_高危"})
    private Integer dangerClass;

    /**
     * 推送状态,0未推送,1已推送
     */
    @IEnumsConverter(replace = {"0_未推送", "1_已推送"})
    @ExcelProperty(value = "推送状态", converter = IEnumsToStrConverter.class)
    private Integer pushStatus;

    /**
     * 开始时间
     */
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    /**
     * 设备运行状态
     *
     * @see DevDetectRunModeEnums#code
     */
    @IEnumsChangeDesc(key = "devRunModelDesc", enums = DevDetectRunModeEnums.class)
    @ExcelProperty(value = "设备运行状态", converter = IEnumsToStrConverter.class)
    private Integer devRunModel;

    /**
     * 设备运行状态描述
     */
    @ApiFoxNoIgnore
    @ExcelIgnore
    private String devRunModelDesc;

    /**
     * 区域列表
     */
    @ExcelProperty(value = "区域列表", converter = ListToStrConverter.class)
    private List<String> traceRegionNames;

    public Integer getCollectType() {
        return CollectTypeEnums.getByDevType(getDevType()).getCode();
    }

    /**
     * 默认情况下 dangerLevel=1时为1低危, dangerLevel=2,3时为2中危, dangerLevel=4,5时为3高危
     */
    public Integer getDangerClass() {
        if (dangerClass == null) {
            if (dangerLevel == 1) {
                return 1;
            }
            if (dangerLevel == 2 || dangerLevel == 3) {
                return 2;
            }
            if (dangerLevel == 4 || dangerLevel == 5) {
                return 3;
            }
        }
        return dangerClass;
    }
}