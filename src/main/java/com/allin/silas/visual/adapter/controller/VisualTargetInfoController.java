package com.allin.silas.visual.adapter.controller;

import com.allin.silas.visual.adapter.dto.DelVisualTargetDto;
import com.allin.silas.visual.adapter.dto.VisualTargetMarkDto;
import com.allin.silas.visual.adapter.dto.VisualTargetPushDto;
import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoListQuery;
import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoPageQuery;
import com.allin.silas.visual.adapter.vo.VisualTargetInfoListVo;
import com.allin.silas.visual.adapter.vo.VisualTargetMergedInfoVo;
import com.allin.silas.visual.app.service.VisualTargetInfoCommandService;
import com.allin.silas.visual.app.service.VisualTargetInfoQueryService;
import com.allin.silas.visual.utils.VisualDetectUtils;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * 探测识别/可视目标
 *
 * <AUTHOR>
 * @since 2025/6/24
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/visual/target")
public class VisualTargetInfoController {

    private final VisualTargetInfoQueryService infoQueryService;

    private final VisualTargetInfoCommandService infoCommandService;

    public VisualTargetInfoController(VisualTargetInfoQueryService infoQueryService,
                                      VisualTargetInfoCommandService infoCommandService) {
        this.infoQueryService = infoQueryService;
        this.infoCommandService = infoCommandService;
    }

    /**
     * 可视目标可识别的细分类型列表
     */
    @GetMapping("/detect_sub_type")
    public Result<Collection<String>> detectSubType() {
        return Result.ok(VisualDetectUtils.TARGET_DETECT_TYPE_MAP.values());
    }

    /**
     * 分页查询可视目标
     */
    @GetMapping("/page")
    public Result<PageData<VisualTargetInfoListVo>> page(PageParam pageParam,
                                                         @Validated VisualTargetMergedInfoPageQuery query) {
        query.validateAndInit();
        return Result.ok(PageData.getInstance(infoQueryService.page(pageParam, query)));
    }

    /**
     * 导出可视目标
     */
    @PostMapping("/export/excel")
    public void exportExcel(@Validated @RequestBody VisualTargetMergedInfoListQuery query,
                            HttpServletResponse response) {
        query.validateAndInit();
        final List<VisualTargetInfoListVo> exportList = infoQueryService.listExport(query);
        FastExcelOperUtils.exportXlsx(response, exportList, VisualTargetInfoListVo.class, "可视目标信息");
    }

    /**
     * 查询可视目标详情
     *
     * @param date yyyy-MM-dd
     */
    @GetMapping
    public Result<VisualTargetMergedInfoVo> info(@RequestParam LocalDate date,
                                                 @RequestParam String batchNumber) {
        return Result.ok(infoQueryService.info(date, batchNumber));
    }

    /**
     * 标记可视目标类型
     */
    @PutMapping("/mark")
    public Result<Void> mark(@RequestBody @Validated VisualTargetMarkDto markDto) {
        return infoCommandService.mark(markDto) ? Result.ok() : Result.fail();
    }

    /**
     * 推送事件
     */
    @PutMapping("/push")
    public Result<Void> push(@RequestBody @Validated VisualTargetPushDto pushDto) {
        return infoCommandService.push(pushDto) ? Result.ok() : Result.fail();
    }

    /**
     * 删除可视目标
     */
    @DeleteMapping
    public Result<Void> del(@Validated @RequestBody DelVisualTargetDto delVisualTargetDto) {
        infoCommandService.del(delVisualTargetDto);
        return Result.ok();
    }

}
