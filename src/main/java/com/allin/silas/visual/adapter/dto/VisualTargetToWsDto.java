package com.allin.silas.visual.adapter.dto;

import com.allin.silas.visual.app.entity.VisualTargetOriginalInfo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 可视目标信息推送到前端界面
 */
@Data
public class VisualTargetToWsDto {

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备类型
     */
    private String devType;

    /**
     * 后端生成的批次号
     */
    private String backendBatchNumber;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 方位值
     */
    private Float azimuth;

    /**
     * 俯仰值
     */
    private Float pitch;

    /**
     * 距离
     */
    private Float distance;

    /**
     * 高度
     */
    private Float height;

    /**
     * 速度
     */
    private Float speed;

    /**
     * 翼展
     */
    private Float wingSpan;

    /**
     * 飞行方向
     */
    private Float flightDirection;

    /**
     * 目标大小,0点目标,1面目标
     */
    private Integer targetSize;

    /**
     * 细分识别类型,子类
     */
    private String detectSubType;

    /**
     * 主要识别类型,一级分类
     */
    private String detectType;

    /**
     * 面积
     */
    private Float area;

    /**
     * 切片地址
     */
    private String imgUrl;

    /**
     * 目标数量
     */
    private Integer targetCount;

    /**
     * 设备运行状态
     *
     */
    private Integer devRunModel;

    /**
     * 离跑道的距离
     */
    private Double runwayDistance;

    public VisualTargetToWsDto() {
    }

    public VisualTargetToWsDto(VisualTargetOriginalInfo originalInfo) {
        BeanUtils.copyProperties(originalInfo, this);
    }
}