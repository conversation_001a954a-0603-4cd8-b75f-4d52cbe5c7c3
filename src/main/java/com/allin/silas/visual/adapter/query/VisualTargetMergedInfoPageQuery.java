package com.allin.silas.visual.adapter.query;

import cn.hutool.core.collection.CollUtil;
import com.allin.silas.visual.app.enums.CollectTypeEnums;
import com.allin.silas.visual.app.enums.DetectSubTypeEnums;
import com.allin.silas.visual.app.enums.DetectTypeEnums;
import com.allin.silas.visual.app.enums.MarkTypeEnums;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.enums.validator.IEnumValid;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.execl.DefaultExportStyle;
import com.allin.view.base.i18n.I18nUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 合并的可视目标信息表
 */
@Data
public class VisualTargetMergedInfoPageQuery extends DefaultExportStyle {

    /**
     * 设备编号
     */
    private List<String> devNums;

    /**
     * 开始时间, yyyy-MM-dd HH:mm:ss
     */
    @NotNull
    private LocalDateTime startTime;

    /**
     * 结束时间, yyyy-MM-dd HH:mm:ss
     */
    @NotNull
    private LocalDateTime endTime;

    /**
     * 细分识别类型,子类
     */
    private List<String> detectSubTypes;

    /**
     * 主要识别类型,鸟科/鸟群/无人机/空飘物/违建
     */
    private List<@IEnumValid(target = DetectTypeEnums.class) String> detectTypes;

    /**
     * 是否需要查询识别类型为空的目标, 0-否,1-是
     */
    private Integer isQueryNullDetectType;

    /**
     * 人工标记类型,无人机/空飘物/鸟/鸟群/飞机/违建/虚景
     */
    private List<@IEnumValid(target = MarkTypeEnums.class) String> markTypes;

    /**
     * 人工手动标记名称，只有选鸟的时候才传值
     */
    private List<String> markSubTypes;

    /**
     * 最小高度
     */
    private Double minHeight;

    /**
     * 最大高度
     */
    private Double maxHeight;

    /**
     * 离跑道最小距离
     */
    private Double minRunwayDistance;

    /**
     * 离跑道最大距离
     */
    private Double maxRunwayDistance;

    /**
     * 目标大小,0点目标,1面目标
     */
    @Range(min = 0, max = 1)
    private Integer targetSize;

    /**
     * 推送状态,0未推送,1已推送
     */
    @Range(min = 0, max = 1)
    private Integer pushStatus;

    /**
     * 危险等级
     */
    private List<@Range(min = 1, max = 5) Integer> dangerLevels;

    /**
     * 区域id列表
     */
    private List<String> regionIds;

    /**
     * 采集类型
     *
     * @see CollectTypeEnums#code
     */
    @IEnumValid(target = CollectTypeEnums.class)
    private Integer collectType;

    /**
     * 设备类型
     */
    @JsonIgnore
    private List<String> devTypes;

    /**
     * 项目编号
     */
    @JsonIgnore
    private String projectId;


    /**
     * 前端如果发送的标记分类中不包含鸟则直接重置为空
     */
    public List<String> getMarkSubTypes() {
        if (CollUtil.isNotEmpty(getMarkTypes()) && CollUtil.isNotEmpty(markSubTypes)
            && !getMarkTypes().contains(MarkTypeEnums.BIRD.getCode())) {
            setMarkSubTypes(Collections.emptyList());
        }
        return markSubTypes;
    }

    /**
     * 校验参数
     */
    public void validateAndInit() {
        setProjectId(SecurityContextHolder.getProjectId());

        // 结束时间不能超过今天
        if (endTime.toLocalDate().isAfter(LocalDate.now())) {
            throw new IllegalArgumentException(I18nUtil.getMessage("visual.time.cannot.exceed.today"));
        }

        // 根据采集类型获取设备类型
        if (collectType != null) {
            setDevTypes(CollectTypeEnums.getByCode(collectType));
        }

        if (CollUtil.isNotEmpty(detectSubTypes)) {
            for (String type : detectSubTypes) {
                if (!DetectSubTypeEnums.listDetectSubType().contains(type)) {
                    throw new ValidationFailureException(I18nUtil.isParamException("detectSubTypes"));
                }
            }
        }
    }
}