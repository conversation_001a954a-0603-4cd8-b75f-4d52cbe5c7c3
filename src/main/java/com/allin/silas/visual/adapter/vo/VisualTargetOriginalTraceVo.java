package com.allin.silas.visual.adapter.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 原始的可视目标信息表
 */
@Data
public class VisualTargetOriginalTraceVo {

    /**
     * 轨迹id
     */
    private String id;

    /**
     * 后端生成的批次号
     */
    private String backendBatchNumber;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 方位值
     */
    private Float azimuth;

    /**
     * 俯仰值
     */
    private Float pitch;

    /**
     * 距离跑道
     */
    private Float runwayDistance;

    /**
     * 距离跑道中心
     */
    private Float runwayCenterDistance;

    /**
     * 高度
     */
    private Float height;

    /**
     * 速度
     */
    private Float speed;

    /**
     * 翼展
     */
    private Float wingSpan;

    /**
     * 飞行方向
     */
    private Float flightDirection;

    /**
     * 面积
     */
    private Float area;

    /**
     * 切片地址
     */
    private String imgUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
}