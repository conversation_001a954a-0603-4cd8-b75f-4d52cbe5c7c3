package com.allin.silas.visual.infra.repository;

import com.allin.silas.visual.adapter.vo.VisualTargetOriginalMergedVo;
import com.allin.silas.visual.adapter.vo.VisualTargetOriginalTraceVo;
import com.allin.silas.visual.app.entity.VisualTargetOriginalInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VisualTargetOriginalInfoMapper extends BaseMapper<VisualTargetOriginalInfo> {

    /**
     * 目标轨迹列表
     */
    List<VisualTargetOriginalTraceVo> trace(@Param("tableName") String tableName,
                                            @Param("batchNumber") String batchNumber);

    /**
     * 查询还没有合并完全的数据
     * 按创建时间逆序排序
     */
    List<VisualTargetOriginalMergedVo> listNotMergedAndSortCreatedTimeDesc(@Param("tableName") String tableName,
                                                                           @Param("batchNumbers") List<String> batchNumbers);

    /**
     * 查询目标的切片地址列表
     */
    List<String> listImgPath(@Param("tableName") String tableName,
                             @Param("batchNumbers") List<String> batchNumbers);
}