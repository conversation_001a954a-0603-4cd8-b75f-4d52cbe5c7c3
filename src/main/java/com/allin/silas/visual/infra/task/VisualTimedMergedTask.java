package com.allin.silas.visual.infra.task;

import cn.hutool.core.collection.CollUtil;
import com.allin.silas.common.mybatis.config.MybatisDynamicTableNameHandler;
import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.visual.adapter.vo.VisualTargetOriginalMergedVo;
import com.allin.silas.visual.app.entity.VisualTargetMergedInfo;
import com.allin.silas.visual.infra.repository.VisualTargetMergedInfoMapper;
import com.allin.silas.visual.infra.repository.VisualTargetOriginalInfoMapper;
import com.allin.silas.visual.utils.VisualDetectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 定时合并数据
 *
 * <AUTHOR>
 * @since 2025/7/31
 */
@Slf4j
@Component
public class VisualTimedMergedTask {

    private final VisualTargetOriginalInfoMapper visualTargetOriginalInfoMapper;

    private final VisualTargetMergedInfoMapper visualTargetMergedInfoMapper;

    private final VisualDetectUtils visualDetectUtils;

    public VisualTimedMergedTask(VisualTargetOriginalInfoMapper visualTargetOriginalInfoMapper,
                                 VisualTargetMergedInfoMapper visualTargetMergedInfoMapper,
                                 VisualDetectUtils visualDetectUtils) {
        this.visualTargetOriginalInfoMapper = visualTargetOriginalInfoMapper;
        this.visualTargetMergedInfoMapper = visualTargetMergedInfoMapper;
        this.visualDetectUtils = visualDetectUtils;
    }


    /**
     * 每30秒合并一次数据
     */
    @Scheduled(cron = "*/30 * * * * *")
    @Transactional(rollbackFor = Exception.class)
    public void merged() {
        LocalDate localDate = LocalDate.now();
        String mergedTableName = TableNameUtils.getVisualTargetMergedInfo(localDate);
        String originalTableName = TableNameUtils.getVisualTargetOriginalInfo(localDate);
        // 查询需要合并的数据
        final List<VisualTargetMergedInfo> allMergedInfos = visualTargetMergedInfoMapper.listNotMerged(mergedTableName);
        final List<String> batchNumbers = allMergedInfos.stream().map(VisualTargetMergedInfo::getBackendBatchNumber).toList();
        // 查询所有原始数据
        final List<VisualTargetOriginalMergedVo> allOriginalInfos = visualTargetOriginalInfoMapper.listNotMergedAndSortCreatedTimeDesc(originalTableName, batchNumbers);
        // 按目标编号分组
        final Map<String, List<VisualTargetOriginalMergedVo>> batchNumberMap = allOriginalInfos.stream()
                .collect(Collectors.groupingBy(VisualTargetOriginalMergedVo::getBackendBatchNumber));

        for (VisualTargetMergedInfo mergedInfo : allMergedInfos) {
            if (!batchNumberMap.containsKey(mergedInfo.getBackendBatchNumber())) {
                continue;
            }
            List<VisualTargetOriginalMergedVo> originalInfos = batchNumberMap.get(mergedInfo.getBackendBatchNumber());
            if (CollUtil.isEmpty(originalInfos)) {
                continue;
            }

            mergedInfo.setMergedTime(LocalDateTime.now());

            // 判断合并数量，相等则表示没有新轨迹，只需要更新合并状态和时间，而无需重新计算统计数据
            if (mergedInfo.getMergedCount() != null && mergedInfo.getMergedCount().equals(originalInfos.size())) {
                mergedInfo.setMergedStatus(1);
                continue;
            }

            // 更新合并数量
            mergedInfo.setMergedCount(originalInfos.size());

            // 按时间排序，第一个是最新的，最后一个是最早的
            originalInfos.sort(Comparator.comparing(VisualTargetOriginalMergedVo::getCreatedTime).reversed());

            // 更新开始和结束时间
            mergedInfo.setStartTime(originalInfos.get(originalInfos.size() - 1).getCreatedTime());
            mergedInfo.setEndTime(originalInfos.get(0).getCreatedTime());

            // 投票更新识别类型
            final Optional<String> detectSubTypeOpt = VisualDetectUtils.vote(originalInfos.stream()
                    .map(VisualTargetOriginalMergedVo::getDetectSubType)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
            detectSubTypeOpt.ifPresent(detectSubType -> {
                mergedInfo.setDetectSubType(detectSubType);
                mergedInfo.setDetectType(visualDetectUtils.getDetectType(detectSubType, originalInfos.size()));
            });

            // 更新经纬度范围
            updateLocationRange(mergedInfo, originalInfos);

            // 更新各种统计值
            updateStatisticalValues(mergedInfo, originalInfos);

            // 更新图片URL
            mergedInfo.setStartImgUrl(originalInfos.get(originalInfos.size() - 1).getImgUrl());
            mergedInfo.setEndImgUrl(originalInfos.get(0).getImgUrl());

        }
        // 更新合并数据
        try {
            MybatisDynamicTableNameHandler.setSuffix(localDate);
            // 批量更新每个合并信息
            visualTargetMergedInfoMapper.updateById(allMergedInfos);
        } finally {
            MybatisDynamicTableNameHandler.clear();
        }
    }

    /**
     * 更新经纬度范围
     */
    private void updateLocationRange(VisualTargetMergedInfo mergedInfo, List<VisualTargetOriginalMergedVo> originalInfos) {
        // 开始位置（最早时间）
        VisualTargetOriginalMergedVo startInfo = originalInfos.get(originalInfos.size() - 1);
        mergedInfo.setStartLongitude(startInfo.getLongitude());
        mergedInfo.setStartLatitude(startInfo.getLatitude());

        // 结束位置（最新时间）
        VisualTargetOriginalMergedVo endInfo = originalInfos.get(0);
        mergedInfo.setEndLongitude(endInfo.getLongitude());
        mergedInfo.setEndLatitude(endInfo.getLatitude());
    }

    /**
     * 更新各种统计值
     */
    private void updateStatisticalValues(VisualTargetMergedInfo mergedInfo, List<VisualTargetOriginalMergedVo> originalInfos) {
        // 过滤掉null值的数据流
        List<Float> azimuths = originalInfos.stream().map(VisualTargetOriginalMergedVo::getAzimuth).filter(Objects::nonNull).toList();
        List<Float> pitches = originalInfos.stream().map(VisualTargetOriginalMergedVo::getPitch).filter(Objects::nonNull).toList();
        List<Float> distances = originalInfos.stream().map(VisualTargetOriginalMergedVo::getDistance).filter(Objects::nonNull).toList();
        List<Float> heights = originalInfos.stream().map(VisualTargetOriginalMergedVo::getHeight).filter(Objects::nonNull).toList();
        List<Float> speeds = originalInfos.stream().map(VisualTargetOriginalMergedVo::getSpeed).filter(Objects::nonNull).toList();
        List<Float> wingSpans = originalInfos.stream().map(VisualTargetOriginalMergedVo::getWingSpan).filter(Objects::nonNull).toList();
        List<Float> areas = originalInfos.stream().map(VisualTargetOriginalMergedVo::getArea).filter(Objects::nonNull).toList();
        List<Float> confidenceLevels = originalInfos.stream().map(VisualTargetOriginalMergedVo::getConfidenceLevel).filter(Objects::nonNull).toList();
        List<Integer> targetCounts = originalInfos.stream().map(VisualTargetOriginalMergedVo::getTargetCount).filter(Objects::nonNull).toList();
        List<Float> runwayDistances = originalInfos.stream().map(VisualTargetOriginalMergedVo::getRunwayDistance).filter(Objects::nonNull).toList();
        List<Float> runwayCenterDistances = originalInfos.stream().map(VisualTargetOriginalMergedVo::getRunwayCenterDistance).filter(Objects::nonNull).toList();
        List<Float> flightDirections = originalInfos.stream().map(VisualTargetOriginalMergedVo::getFlightDirection).filter(Objects::nonNull).toList();

        // 更新方位值范围
        if (!azimuths.isEmpty()) {
            mergedInfo.setMinAzimuth(azimuths.stream().min(Float::compareTo).orElse(null));
            mergedInfo.setMaxAzimuth(azimuths.stream().max(Float::compareTo).orElse(null));
        }

        // 更新俯仰值范围
        if (!pitches.isEmpty()) {
            mergedInfo.setMinPitch(pitches.stream().min(Float::compareTo).orElse(null));
            mergedInfo.setMaxPitch(pitches.stream().max(Float::compareTo).orElse(null));
        }

        // 更新距离范围
        if (!distances.isEmpty()) {
            mergedInfo.setMinDistance(distances.stream().min(Float::compareTo).orElse(null));
            mergedInfo.setMaxDistance(distances.stream().max(Float::compareTo).orElse(null));
        }

        // 更新高度范围
        if (!heights.isEmpty()) {
            mergedInfo.setMinHeight(heights.stream().min(Float::compareTo).orElse(null));
            mergedInfo.setMaxHeight(heights.stream().max(Float::compareTo).orElse(null));
        }

        // 更新速度范围
        if (!speeds.isEmpty()) {
            mergedInfo.setMinSpeed(speeds.stream().min(Float::compareTo).orElse(null));
            mergedInfo.setMaxSpeed(speeds.stream().max(Float::compareTo).orElse(null));
        }

        // 更新翼展范围
        if (!wingSpans.isEmpty()) {
            mergedInfo.setMinWingSpan(wingSpans.stream().min(Float::compareTo).orElse(null));
            mergedInfo.setMaxWingSpan(wingSpans.stream().max(Float::compareTo).orElse(null));
        }

        // 更新最大面积
        if (!areas.isEmpty()) {
            mergedInfo.setMaxArea(areas.stream().max(Float::compareTo).orElse(null));
        }

        // 更新最大置信度
        if (!confidenceLevels.isEmpty()) {
            mergedInfo.setMaxConfidenceLevel(confidenceLevels.stream().max(Float::compareTo).orElse(null));
        }

        // 更新最大目标数量
        if (!targetCounts.isEmpty()) {
            mergedInfo.setMaxTargetCount(targetCounts.stream().max(Integer::compareTo).orElse(null));
        }

        // 更新跑道距离范围
        if (!runwayDistances.isEmpty()) {
            mergedInfo.setMinRunwayDistance(runwayDistances.stream().min(Float::compareTo).map(Float::doubleValue).orElse(null));
            mergedInfo.setMaxRunwayDistance(runwayDistances.stream().max(Float::compareTo).map(Float::doubleValue).orElse(null));
        }

        // 更新跑道中心距离范围
        if (!runwayCenterDistances.isEmpty()) {
            mergedInfo.setMinRunwayCenterDistance(runwayCenterDistances.stream().min(Float::compareTo).map(Float::doubleValue).orElse(null));
            mergedInfo.setMaxRunwayCenterDistance(runwayCenterDistances.stream().max(Float::compareTo).map(Float::doubleValue).orElse(null));
        }

        // 更新飞行方向（开始和结束）
        mergedInfo.setEndFlightDirection(originalInfos.get(0).getFlightDirection());
        mergedInfo.setStartFlightDirection(originalInfos.get(originalInfos.size() - 1).getFlightDirection());
    }
}
