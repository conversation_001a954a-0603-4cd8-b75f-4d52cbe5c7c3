package com.allin.silas.visual.infra.task;

import com.allin.silas.common.mybatis.config.MybatisDynamicTableNameHandler;
import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.visual.adapter.vo.VisualTargetOriginalMergedVo;
import com.allin.silas.visual.app.entity.VisualTargetMergedInfo;
import com.allin.silas.visual.infra.repository.VisualTargetMergedInfoMapper;
import com.allin.silas.visual.infra.repository.VisualTargetOriginalInfoMapper;
import com.allin.silas.visual.utils.VisualDetectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 定时合并数据
 *
 * <AUTHOR>
 * @since 2025/7/31
 */
@Slf4j
@Component
public class VisualTimedMergedTask {

    private final VisualTargetOriginalInfoMapper visualTargetOriginalInfoMapper;

    private final VisualTargetMergedInfoMapper visualTargetMergedInfoMapper;

    private final VisualDetectUtils visualDetectUtils;

    public VisualTimedMergedTask(VisualTargetOriginalInfoMapper visualTargetOriginalInfoMapper,
                                 VisualTargetMergedInfoMapper visualTargetMergedInfoMapper,
                                 VisualDetectUtils visualDetectUtils) {
        this.visualTargetOriginalInfoMapper = visualTargetOriginalInfoMapper;
        this.visualTargetMergedInfoMapper = visualTargetMergedInfoMapper;
        this.visualDetectUtils = visualDetectUtils;
    }


    /**
     * 每30秒合并一次数据
     */
    @Scheduled(cron = "*/30 * * * * *")
    @Transactional(rollbackFor = Exception.class)
    public void merged() {
        LocalDate localDate = LocalDate.now();
        String mergedTableName = TableNameUtils.getVisualTargetMergedInfo(localDate);
        String originalTableName = TableNameUtils.getVisualTargetOriginalInfo(localDate);
        // 查询需要合并的数据
        final List<VisualTargetMergedInfo> allMergedInfos = visualTargetMergedInfoMapper.listNotMerged(mergedTableName);
        final List<String> batchNumbers = allMergedInfos.stream().map(VisualTargetMergedInfo::getBackendBatchNumber).toList();
        // 查询所有原始数据
        final List<VisualTargetOriginalMergedVo> allOriginalInfos = visualTargetOriginalInfoMapper.listNotMergedAndSortCreatedTimeDesc(originalTableName, batchNumbers);
        // 按目标编号分组并按创建时间逆序排序
        final Map<String, List<VisualTargetOriginalMergedVo>> batchNumberMap = allOriginalInfos.stream()
                .collect(Collectors.groupingBy(
                        VisualTargetOriginalMergedVo::getBackendBatchNumber,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(VisualTargetOriginalMergedVo::getCreatedTime).reversed())
                                        .collect(Collectors.toList())
                        )
                ));

        for (VisualTargetMergedInfo mergedInfo : allMergedInfos) {
            if (!batchNumberMap.containsKey(mergedInfo.getBackendBatchNumber())) {
                continue;
            }
            List<VisualTargetOriginalMergedVo> originalInfos = batchNumberMap.get(mergedInfo.getBackendBatchNumber());
            mergedInfo.setMergedTime(LocalDateTime.now());
            mergedInfo.setMergedCount(originalInfos.size());
            // 判断合并数量，相等则表示没有新轨迹，只需要更新合并状态为已合并，而无需更新数据
            if (mergedInfo.getMergedCount() == originalInfos.size()) {
                mergedInfo.setMergedStatus(1);
                continue;
            }
            // 更新结束时间
            mergedInfo.setEndTime(originalInfos.get(0).getCreatedTime());
            // 投票更新识别类型
            final Optional<String> detectSubTypeOpt = VisualDetectUtils.vote(originalInfos.stream()
                    .map(VisualTargetOriginalMergedVo::getDetectSubType)
                    .collect(Collectors.toList()));
            detectSubTypeOpt.ifPresent(detectSubType -> {
                mergedInfo.setDetectSubType(detectSubType);
                mergedInfo.setDetectType(visualDetectUtils.getDetectType(detectSubType, originalInfos.size()));
            });
            // TODO 更新其他数据

        }
        // 更新合并数据
        try {
            MybatisDynamicTableNameHandler.setSuffix(localDate);
            visualTargetMergedInfoMapper.updateById(allMergedInfos);
        } finally {
            MybatisDynamicTableNameHandler.clear();
        }
    }
}
