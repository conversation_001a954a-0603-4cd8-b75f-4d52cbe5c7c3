package com.allin.silas.visual.infra.repository;

import com.allin.silas.visual.adapter.vo.VisualTargetTraceRegionVo;
import com.allin.silas.visual.app.entity.VisualTargetTraceRegion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface VisualTargetTraceRegionMapper extends BaseMapper<VisualTargetTraceRegion> {

    /**
     * 根据批次号查询
     */
    List<VisualTargetTraceRegionVo> listByBatchNumbers(List<String> batchNumbers);

    /**
     * 获取最后出现的区域
     */
    @Nullable
    VisualTargetTraceRegionVo getLastRegion(String batchNumber);
}