package com.allin.silas.visual.client.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备发送的可视目标信息解析传输类
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VisualTargetReceiveDto {

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备类型
     */
    private String devType;

    /**
     * 后端生成的批次号
     */
    private String backendBatchNumber;

    /**
     * 前端设备发送的批次号
     */
    private String frontendBatchNumber;

    /**
     * 全景图编号
     */
    private String panoramaId;

    /**
     * 全景图x坐标
     */
    private Double panoX;

    /**
     * 全景图y坐标
     */
    private Double panoY;

    /**
     * 轨迹x坐标
     */
    private String trajectoryX;

    /**
     * 轨迹y坐标
     */
    private String trajectoryY;

    /**
     * 方位轨迹
     */
    private String azimuthTrajectory;

    /**
     * 俯仰轨迹
     */
    private String pitchTrajectory;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 方位值
     */
    private Float azimuth;

    /**
     * 俯仰值
     */
    private Float pitch;

    /**
     * 距离
     */
    private Float distance;

    /**
     * 高度
     */
    private Float height;

    /**
     * 当前分割位
     */
    private Integer splitBit;

    /**
     * 速度
     */
    private Float speed;

    /**
     * 翼展
     */
    private Float wingSpan;

    /**
     * 飞行方向
     */
    private Float flightDirection;

    /**
     * 目标大小,0点目标,1面目标
     */
    private Integer targetSize;

    /**
     * 识别类型
     */
    private Long detectType;

    /**
     * 置信度
     */
    private Float confidenceLevel;

    /**
     * 面积
     */
    private Float area;

    /**
     * 切片宽度
     */
    private Integer imgWidth;

    /**
     * 切片高度
     */
    private Integer imgHeight;

    /**
     * 切片地址
     */
    private String imgUrl;

    /**
     * 目标数量
     */
    private Integer targetCount;

    /**
     * 设备运行状态
     */
    private Integer devRunModel;

    /**
     * 离跑道距离
     */
    private Double runwayDistance;

    /**
     * 离跑道中心点距离
     */
    private Double runwayCenterDistance;

    /**
     * 图片数据
     */
    private byte[] imgData;
}