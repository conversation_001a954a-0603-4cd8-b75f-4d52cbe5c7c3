package com.allin.silas.visual.client.vo;

import com.allin.silas.dev.detect.app.enums.DevDetectRunModeEnums;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 合并的可视目标信息表
 */
@Data
public class VisualTargetMergedInfoClientVo {
    /**
     * 主键id
     */
    private String id;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备类型
     */
    private String devType;

    /**
     * 后端生成的批次号
     */
    private String backendBatchNumber;

    /**
     * 开始经度
     */
    private Double startLongitude;

    /**
     * 开始纬度
     */
    private Double startLatitude;

    /**
     * 结束经度
     */
    private Double endLongitude;

    /**
     * 结束纬度
     */
    private Double endLatitude;

    /**
     * 最小方位值
     */
    private Float minAzimuth;

    /**
     * 最大方位值
     */
    private Float maxAzimuth;

    /**
     * 最小俯仰值
     */
    private Float minPitch;

    /**
     * 最大俯仰值
     */
    private Float maxPitch;

    /**
     * 最小距离
     */
    private Float minDistance;

    /**
     * 最大距离
     */
    private Float maxDistance;

    /**
     * 最小高度
     */
    private Float minHeight;

    /**
     * 最大高度
     */
    private Float maxHeight;

    /**
     * 最小速度
     */
    private Float minSpeed;

    /**
     * 最大速度
     */
    private Float maxSpeed;

    /**
     * 最小翼展
     */
    private Float minWingSpan;

    /**
     * 最大翼展
     */
    private Float maxWingSpan;

    /**
     * 开始飞行方向
     */
    private Float startFlightDirection;

    /**
     * 结束飞行方向
     */
    private Float endFlightDirection;

    /**
     * 最大面积
     */
    private Float maxArea;

    /**
     * 最大置信度
     */
    private Float maxConfidenceLevel;

    /**
     * 第一帧的切片地址
     */
    private String startImgUrl;

    /**
     * 最后一帧的切片地址
     */
    private String endImgUrl;

    /**
     * 最大只数
     */
    private Integer maxTargetCount;

    /**
     * 离跑道最小距离, 单位米
     */
    private Double minRunwayDistance;

    /**
     * 离跑道最大距离，单位米
     */
    private Double maxRunwayDistance;

    /**
     * 离跑道中心点最小距离, 单位米
     */
    private Double minRunwayCenterDistance;

    /**
     * 离跑道中心点最大距离，单位米
     */
    private Double maxRunwayCenterDistance;

    /**
     * 目标大小,0点目标,1面目标
     */
    private Integer targetSize;

    /**
     * 细分识别类型,子类
     */
    private String detectSubType;

    /**
     * 主要识别类型,一级分类
     */
    private String detectType;

    /**
     * 手动标记类型
     *
     * @see com.allin.silas.visual.app.enums.MarkTypeEnums#code
     */
    private String markType;

    /**
     * 手动标记子类型
     */
    private String markSubType;

    /**
     * 危险等级
     *
     * @see com.allin.silas.kn.app.entity.KnBirdInfoDangerLevel
     */
    private Integer dangerLevel;

    /**
     * 碰撞可能性
     */
    private Integer collisionPossibility;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 设备运行状态
     *
     * @see DevDetectRunModeEnums#code
     */
    private Integer devRunModel;
}