package com.allin.silas.visual.client;

import com.allin.silas.visual.client.dto.VisualTargetReceiveDto;
import com.allin.silas.visual.client.vo.VisualTargetMergedInfoClientVo;
import jakarta.annotation.Nullable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 可视目标门面层
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
public interface VisualTargetFacade {

    /**
     * 保存可视目标数据和切片信息
     */
    void saveVisualTarget(List<VisualTargetReceiveDto> sendDataList);

    /**
     * 目标信息
     */
    @Nullable
    VisualTargetMergedInfoClientVo getByBatchNumber(LocalDateTime localDateTime, String batchNumber);

    /**
     * 目标信息
     */
    List<VisualTargetMergedInfoClientVo> getByBatchNumber(LocalDateTime localDateTime, List<String> batchNumbers);
}
