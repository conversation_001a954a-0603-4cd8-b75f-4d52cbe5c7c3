package com.allin.silas.visual.client.event;

import com.allin.silas.visual.app.entity.VisualTargetOriginalInfo;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 可视目标信息已入库事件
 *
 * <AUTHOR>
 * @since 2025/7/17
 */
public class VisualTargetOriginalSavedEvent extends ApplicationEvent {

    @Getter
    private final VisualTargetOriginalInfo info;

    public VisualTargetOriginalSavedEvent(VisualTargetOriginalInfo info) {
        super(info);
        this.info = info;
    }

}
