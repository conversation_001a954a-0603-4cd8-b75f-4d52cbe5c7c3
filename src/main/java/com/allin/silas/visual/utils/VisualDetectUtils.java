package com.allin.silas.visual.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.visual.app.enums.DetectSubTypeEnums;
import com.allin.silas.visual.app.enums.DetectTypeEnums;
import com.allin.view.base.enums.base.IEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 目标识别类型工具类
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
@Slf4j
@Component
public class VisualDetectUtils {

    public static final Map<Long, String> TARGET_DETECT_TYPE_MAP = new HashMap<>();

    /**
     * 主类型和子类型映射表
     */
    public static final Map<String, String> TRANSFORM_MAP = new HashMap<>();

    static {
        TRANSFORM_MAP.put("漂浮物", "空飘物");
        TRANSFORM_MAP.put("气球", "空飘物");
        TRANSFORM_MAP.put("空飘物", "空飘物");
        TRANSFORM_MAP.put("无人机", "无人机");
        TRANSFORM_MAP.put("违建", "违建");
        TRANSFORM_MAP.put("鸟群", "鸟群");

        // 读取类型映射配置文件
        ClassPathResource detectTypeFile = new ClassPathResource("/detect/detect_type.txt");
        // 读取每一行，从0开始，在下标基础上+10000做为key，value为内容
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                detectTypeFile.getInputStream(), StandardCharsets.UTF_8))) {
            int index = 0;
            for (String line; (line = reader.readLine()) != null; index++) {
                TARGET_DETECT_TYPE_MAP.put(index + 10000L, line.trim());
            }
        } catch (Exception e) {
            log.error("加载 detect_type.txt 失败: {}", e.getMessage());
        }
        log.info("成功从 detect_type.txt 加载 {} 条识别类型", TARGET_DETECT_TYPE_MAP.size());
    }

    /**
     * 将前端设备发送的code转换成Name
     *
     * @param code 前端设备发送的code
     */
    public static Optional<String> codeToType(Long code) {
        if (code == null) {
            return Optional.empty();
        }
        // 小于10000走枚举
        if (code < 10000) {
            return IEnums.tryGetDesc(DetectSubTypeEnums.class, code);
        }
        // 大于20000需要减10000
        if (code > 20000) {
            code -= 10000;
        }
        // 将代码转换成中文
        String typeName = TARGET_DETECT_TYPE_MAP.get(code);
        if (typeName == null) {
            return Optional.empty();
        } else {
            // 将部分类型合并到大类
            typeName = TRANSFORM_MAP.getOrDefault(typeName, typeName);
        }
        // 在转换一次
        return Optional.ofNullable(typeName);
    }

    /**
     * 投票算法，返回出现类型最多的类型
     *
     * @param types 前端识别的类型序列
     */
    public static Optional<String> vote(Collection<String> types) {
        if (CollUtil.isEmpty(types)) {
            return Optional.empty();
        }
        Map<String, Integer> typeCount = new HashMap<>();

        // 统计每个类型的出现次数
        for (String type : types) {
            typeCount.put(type, typeCount.getOrDefault(type, 0) + 1);
        }

        String mostVotedType = null;
        int maxCount = 0;

        // 遍历统计结果，找出票数最多的类型
        for (Map.Entry<String, Integer> entry : typeCount.entrySet()) {
            if (entry.getValue() > maxCount) {
                mostVotedType = entry.getKey();
                maxCount = entry.getValue();
            }
        }

        return Optional.ofNullable(mostVotedType);
    }

    /**
     * 将子类型转换为大类
     *
     * @param detectSubType 前端识别的类型
     */
    private String transform(String detectSubType) {
        if (StrUtil.isBlank(detectSubType)) {
            return null;
        }

        return TRANSFORM_MAP.getOrDefault(detectSubType, detectSubType);
    }


    /**
     * 根据子类型以及其他参数推断主类型
     */
    @Cacheable(cacheNames = "detectType#60", key = "#detectSubType + #targetCount", unless = "#result == null")
    public String getDetectType(String detectSubType, Integer targetCount) {
        if (StrUtil.isBlank(detectSubType)) {
            return null;
        }
        // 名字中带科认为识别类型为鸟
        if (detectSubType.contains("科") || detectSubType.contains("鸟")) {
            // TODO 判断是否为鸟群
            if (targetCount > 3) {
                return DetectTypeEnums.BIRDS.getCode();
            }
            return DetectTypeEnums.BIRD.getCode();
        }
        return transform(detectSubType);
    }
}
