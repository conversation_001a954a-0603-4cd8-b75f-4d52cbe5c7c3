package com.allin.silas.visual.app.service;

import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoListQuery;
import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoPageQuery;
import com.allin.silas.visual.adapter.vo.VisualTargetInfoListVo;
import com.allin.silas.visual.adapter.vo.VisualTargetMergedInfoVo;
import com.allin.silas.visual.adapter.vo.VisualTargetOriginalTraceVo;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.time.LocalDate;
import java.util.List;

/**
 * 可视目标信息查询服务接口
 *
 * <AUTHOR>
 * @since 2025/6/25
 */
public interface VisualTargetInfoQueryService {

    /**
     * 分页查询
     */
    Page<VisualTargetInfoListVo> page(PageParam pageParam, VisualTargetMergedInfoPageQuery query);

    /**
     * 查询导出列表
     */
    List<VisualTargetInfoListVo> listExport(VisualTargetMergedInfoListQuery query);

    /**
     * 查询详情
     */
    VisualTargetMergedInfoVo info(LocalDate date, String batchNumber);

    /**
     * 轨迹查询
     */
    List<VisualTargetOriginalTraceVo> trace(LocalDate date, String batchNumber);

    /**
     * 查询切片列表
     */
    List<String> listImgPath(LocalDate date, List<String> batchNumbers);
}
