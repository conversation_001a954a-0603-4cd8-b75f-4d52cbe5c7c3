package com.allin.silas.visual.app.manager.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoListQuery;
import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoPageQuery;
import com.allin.silas.visual.adapter.vo.VisualTargetInfoListVo;
import com.allin.silas.visual.adapter.vo.VisualTargetMergedInfoVo;
import com.allin.silas.visual.adapter.vo.VisualTargetTraceRegionVo;
import com.allin.silas.visual.app.entity.VisualTargetMergedInfo;
import com.allin.silas.visual.app.manager.VisualTargetMergedInfoManager;
import com.allin.silas.visual.infra.repository.VisualTargetMergedInfoMapper;
import com.allin.silas.visual.infra.repository.VisualTargetTraceRegionMapper;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 可视目标合并信息通用逻辑层接口实现
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
@Slf4j
@Service
public class VisualTargetMergedInfoManagerImpl implements VisualTargetMergedInfoManager {

    private final VisualTargetMergedInfoMapper infoMapper;

    private final VisualTargetTraceRegionMapper traceRegionMapper;

    public VisualTargetMergedInfoManagerImpl(VisualTargetMergedInfoMapper infoMapper,
                                             VisualTargetTraceRegionMapper traceRegionMapper) {
        this.infoMapper = infoMapper;
        this.traceRegionMapper = traceRegionMapper;
    }

    @Override
    public Page<VisualTargetInfoListVo> page(PageParam pageParam, VisualTargetMergedInfoPageQuery query) {
        final String tableName = TableNameUtils.getVisualTargetMergedInfo(query.getStartTime());
        final Page<VisualTargetInfoListVo> page = pageParam.toPage();
        infoMapper.page(page, tableName, query);
        if (page.getTotal() < 1) {
            return page;
        }
        fillOtherData(page.getRecords());
        return page;
    }

    @Override
    public List<VisualTargetInfoListVo> list(String tableName, VisualTargetMergedInfoListQuery query) {
        final List<VisualTargetInfoListVo> visualTargets = infoMapper.list(tableName, query);
        fillOtherData(visualTargets);
        return visualTargets;
    }

    private void fillOtherData(List<VisualTargetInfoListVo> visualTargets) {
        // TODO 填充危险等级分类
        final List<String> batchNumbers = visualTargets.stream()
                .map(VisualTargetInfoListVo::getBackendBatchNumber)
                .toList();
        final List<VisualTargetTraceRegionVo> traceRegions = traceRegionMapper.listByBatchNumbers(batchNumbers);
        if (CollectionUtils.isEmpty(traceRegions)) {
            return;
        }
        final Map<String, List<VisualTargetTraceRegionVo>> traceRegionMap = traceRegions.stream()
                .collect(Collectors.groupingBy(VisualTargetTraceRegionVo::getBackendBatchNumber));
        visualTargets.forEach(vo -> {
            if (traceRegionMap.containsKey(vo.getBackendBatchNumber())) {
                final List<VisualTargetTraceRegionVo> regions = traceRegionMap.get(vo.getBackendBatchNumber());
                vo.setTraceRegionNames(regions.stream().map(VisualTargetTraceRegionVo::getRegionName).toList());
            }
        });
    }

    @Override
    public VisualTargetMergedInfoVo info(LocalDate date, String backendBatchNumber) {
        String tableName = TableNameUtils.getVisualTargetMergedInfo(date);
        return infoMapper.info(tableName, backendBatchNumber);
    }

    @Cacheable(cacheNames = "visualTarget#60", key = "#devNum + #backendBatchNumber", unless = "#result == 0")
    @Override
    public Long getVisualTargetByCacheTargetNum(String devNum, String backendBatchNumber, LocalDateTime createTime) {
        LambdaQueryWrapper<VisualTargetMergedInfo> query = new LambdaQueryWrapper<>();
        query.eq(VisualTargetMergedInfo::getDevNum, devNum);
        query.eq(VisualTargetMergedInfo::getBackendBatchNumber, backendBatchNumber);
        LocalDateTime beginOfDay = LocalDateTimeUtil.beginOfDay(createTime);
        query.ge(VisualTargetMergedInfo::getStartTime, beginOfDay);
        return infoMapper.selectCount(query);
    }


}