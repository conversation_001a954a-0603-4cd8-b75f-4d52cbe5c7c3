package com.allin.silas.visual.app.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.common.mybatis.config.MybatisDynamicTableNameHandler;
import com.allin.silas.kn.client.KnBirdInfoDangerLevelFacade;
import com.allin.silas.visual.app.entity.VisualTargetMergedInfo;
import com.allin.silas.visual.app.entity.VisualTargetOriginalInfo;
import com.allin.silas.visual.app.manager.VisualTargetMergedInfoManager;
import com.allin.silas.visual.client.VisualTargetFacade;
import com.allin.silas.visual.client.dto.VisualTargetReceiveDto;
import com.allin.silas.visual.client.event.VisualTargetOriginalSavedEvent;
import com.allin.silas.visual.client.vo.VisualTargetMergedInfoClientVo;
import com.allin.silas.visual.constant.FileStorageConstants;
import com.allin.silas.visual.infra.repository.VisualTargetMergedInfoMapper;
import com.allin.silas.visual.infra.repository.VisualTargetOriginalInfoMapper;
import com.allin.silas.visual.utils.VisualDetectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 可视目标信息服务实现类
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Slf4j
@Service
public class VisualTargetFacadeImpl implements VisualTargetFacade {

    private final VisualTargetOriginalInfoMapper visualTargetOriginalInfoMapper;

    private final VisualTargetMergedInfoMapper visualTargetMergedInfoMapper;

    private final VisualTargetMergedInfoManager visualTargetMergedInfoManager;

    private final ApplicationContext applicationContext;

    private final VisualDetectUtils visualDetectUtils;

    private final KnBirdInfoDangerLevelFacade dangerLevelFacade;

    public VisualTargetFacadeImpl(VisualTargetOriginalInfoMapper visualTargetOriginalInfoMapper,
                                  VisualTargetMergedInfoMapper visualTargetMergedInfoMapper,
                                  VisualTargetMergedInfoManager visualTargetMergedInfoManager,
                                  ApplicationContext applicationContext,
                                  VisualDetectUtils visualDetectUtils,
                                  KnBirdInfoDangerLevelFacade dangerLevelFacade) {
        this.visualTargetOriginalInfoMapper = visualTargetOriginalInfoMapper;
        this.visualTargetMergedInfoMapper = visualTargetMergedInfoMapper;
        this.visualTargetMergedInfoManager = visualTargetMergedInfoManager;
        this.applicationContext = applicationContext;
        this.visualDetectUtils = visualDetectUtils;
        this.dangerLevelFacade = dangerLevelFacade;
    }

    /**
     * 保存可视目标数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveVisualTarget(List<VisualTargetReceiveDto> receiveTargets) {
        LocalDateTime now = LocalDateTime.now();
        String today = LocalDateTimeUtil.format(now, DatePattern.PURE_DATE_PATTERN);
        String timestamp = LocalDateTimeUtil.format(now, DatePattern.PURE_DATETIME_MS_FORMATTER);
        for (VisualTargetReceiveDto target : receiveTargets) {
            VisualTargetOriginalInfo originalInfo = new VisualTargetOriginalInfo();
            BeanUtil.copyProperties(target, originalInfo);

            // 构建文件名与保存路径
            String fileName = String.format("%s_%s_%s_%s.jpg",
                    target.getBackendBatchNumber(), timestamp, Math.round(target.getDistance()), target.getTargetSize());
            String devTimeImgDirPath = File.separator + String.join(File.separator,
                    "track",
                    target.getDevNum(),
                    today,
                    String.valueOf(now.getHour()));
            String imgUrl = Paths.get(devTimeImgDirPath, fileName).toString();
            try {
                FileUtil.writeBytes(target.getImgData(), FileStorageConstants.visualImageFileDirPath + imgUrl);
            } catch (Exception e) {
                log.error("保存图片失败", e);
                continue;
            }
            originalInfo.setImgUrl(imgUrl);
            originalInfo.setDetectSubType(VisualDetectUtils.codeToType(target.getDetectType()).orElse(null));
            if (StrUtil.isNotBlank(originalInfo.getDetectSubType())) {
                originalInfo.setDetectType(visualDetectUtils.getDetectType(originalInfo.getDetectSubType(), originalInfo.getTargetCount()));
            }
            // 插入原始表
            visualTargetOriginalInfoMapper.insert(originalInfo);
            // 插入合并表
            initMergedInfo(originalInfo, imgUrl);
            // 推送入库事件
            applicationContext.publishEvent(new VisualTargetOriginalSavedEvent(originalInfo));
        }
    }

    /**
     * 通过唯一约束防止并发初始化合并表信息，这里将异常catch不抛出
     */
    private void initMergedInfo(VisualTargetOriginalInfo originalInfo, String imgUrl) {
        try {
            // 如果不存在，则插入合并表
            boolean notExists = visualTargetMergedInfoManager.getVisualTargetByCacheTargetNum(
                    originalInfo.getDevNum(),
                    originalInfo.getBackendBatchNumber(),
                    originalInfo.getCreatedTime()) == 0;
            if (notExists) {
                VisualTargetMergedInfo mergedInfo = getMergedInfoFromOriginal(originalInfo, imgUrl, originalInfo.getCreatedTime());
                visualTargetMergedInfoMapper.insert(mergedInfo);
            }
        } catch (DataIntegrityViolationException e) {
            // 数据已存在，忽略异常
            log.warn("批次号: {}, 错误信息: {}, 若为唯一约束则忽略", originalInfo.getBackendBatchNumber(), e.getMessage());
        }
    }

    @Override
    public VisualTargetMergedInfoClientVo getByBatchNumber(LocalDateTime localDateTime, String batchNumber) {
        MybatisDynamicTableNameHandler.setSuffix(localDateTime.toLocalDate());
        final VisualTargetMergedInfo visualTargetMergedInfo = visualTargetMergedInfoMapper.selectOne(
                Wrappers.lambdaQuery(VisualTargetMergedInfo.class)
                        .eq(VisualTargetMergedInfo::getBackendBatchNumber, batchNumber));
        return BeanUtil.toBean(visualTargetMergedInfo, VisualTargetMergedInfoClientVo.class);
    }

    @Override
    public List<VisualTargetMergedInfoClientVo> getByBatchNumber(LocalDateTime localDateTime, List<String> batchNumbers) {
        MybatisDynamicTableNameHandler.setSuffix(localDateTime.toLocalDate());
        final List<VisualTargetMergedInfo> visualTargetMergedInfo = visualTargetMergedInfoMapper.selectList(
                Wrappers.lambdaQuery(VisualTargetMergedInfo.class)
                        .in(VisualTargetMergedInfo::getBackendBatchNumber, batchNumbers));
        return BeanUtil.copyToList(visualTargetMergedInfo, VisualTargetMergedInfoClientVo.class);
    }

    /**
     * 构建合并表信息
     */
    private VisualTargetMergedInfo getMergedInfoFromOriginal(VisualTargetOriginalInfo originalInfo, String imgUrl, LocalDateTime createdTime) {
        return VisualTargetMergedInfo.builder()
                .backendBatchNumber(originalInfo.getBackendBatchNumber())
                .devNum(originalInfo.getDevNum())
                .devType(originalInfo.getDevType())
                .detectSubType(originalInfo.getDetectSubType())
                .detectType(originalInfo.getDetectType())
                .projectId(originalInfo.getProjectId())
                .targetSize(originalInfo.getTargetSize())
                .startLatitude(originalInfo.getLatitude())
                .endLatitude(originalInfo.getLatitude())
                .startLongitude(originalInfo.getLongitude())
                .endLongitude(originalInfo.getLongitude())
                .startTime(createdTime)
                .endTime(createdTime)
                .mergedTime(createdTime)
                .minAzimuth(originalInfo.getAzimuth())
                .maxAzimuth(originalInfo.getAzimuth())
                .minPitch(originalInfo.getPitch())
                .maxPitch(originalInfo.getPitch())
                .minDistance(originalInfo.getDistance())
                .maxDistance(originalInfo.getDistance())
                .minHeight(originalInfo.getHeight())
                .maxHeight(originalInfo.getHeight())
                .minSpeed(originalInfo.getSpeed())
                .maxSpeed(originalInfo.getSpeed())
                .minWingSpan(originalInfo.getWingSpan())
                .maxWingSpan(originalInfo.getWingSpan())
                .startFlightDirection(originalInfo.getFlightDirection())
                .endFlightDirection(originalInfo.getFlightDirection())
                .startImgUrl(imgUrl)
                .endImgUrl(imgUrl)
                .minRunwayDistance(originalInfo.getRunwayDistance())
                .maxRunwayDistance(originalInfo.getRunwayDistance())
                .minRunwayCenterDistance(originalInfo.getRunwayCenterDistance())
                .maxRunwayCenterDistance(originalInfo.getRunwayCenterDistance())
                .maxArea(originalInfo.getArea())
                .maxConfidenceLevel(originalInfo.getConfidenceLevel())
                .maxTargetCount(originalInfo.getTargetCount())
                .mergedCount(1)
                .devRunModel(originalInfo.getDevRunModel())
                .dangerLevel(StrUtil.isNotBlank(originalInfo.getDetectSubType()) ? dangerLevelFacade.getDangerLevelCache(originalInfo.getDetectSubType()) : null)
                .build();
    }


}
