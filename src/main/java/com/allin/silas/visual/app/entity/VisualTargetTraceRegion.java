package com.allin.silas.visual.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 可视目标所属区域表
 */
@Data
@TableName(value = "visual_target_trace_region")
public class VisualTargetTraceRegion {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 设备编号
     */
    @TableField(value = "dev_num")
    private String devNum;

    /**
     * 目标批次号
     */
    @TableField(value = "backend_batch_number")
    private String backendBatchNumber;

    /**
     * 所属区域id
     */
    @TableField(value = "map_region_id")
    private String mapRegionId;

    /**
     * 是否删除,0-否,1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
}