package com.allin.silas.visual.app.entity;

import com.allin.silas.dev.detect.app.enums.DevDetectRunModeEnums;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 合并的可视目标信息表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "visual_target_merged_info")
public class VisualTargetMergedInfo {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 设备编号
     */
    @TableField(value = "dev_num")
    private String devNum;

    /**
     * 设备类型
     */
    @TableField(value = "dev_type")
    private String devType;

    /**
     * 后端生成的批次号
     */
    @TableField(value = "backend_batch_number")
    private String backendBatchNumber;

    /**
     * 开始经度
     */
    @TableField(value = "start_longitude")
    private Double startLongitude;

    /**
     * 开始纬度
     */
    @TableField(value = "start_latitude")
    private Double startLatitude;

    /**
     * 结束经度
     */
    @TableField(value = "end_longitude")
    private Double endLongitude;

    /**
     * 结束纬度
     */
    @TableField(value = "end_latitude")
    private Double endLatitude;

    /**
     * 最小方位值
     */
    @TableField(value = "min_azimuth")
    private Float minAzimuth;

    /**
     * 最大方位值
     */
    @TableField(value = "max_azimuth")
    private Float maxAzimuth;

    /**
     * 最小俯仰值
     */
    @TableField(value = "min_pitch")
    private Float minPitch;

    /**
     * 最大俯仰值
     */
    @TableField(value = "max_pitch")
    private Float maxPitch;

    /**
     * 最小距离
     */
    @TableField(value = "min_distance")
    private Float minDistance;

    /**
     * 最大距离
     */
    @TableField(value = "max_distance")
    private Float maxDistance;

    /**
     * 最小高度
     */
    @TableField(value = "min_height")
    private Float minHeight;

    /**
     * 最大高度
     */
    @TableField(value = "max_height")
    private Float maxHeight;

    /**
     * 最小速度
     */
    @TableField(value = "min_speed")
    private Float minSpeed;

    /**
     * 最大速度
     */
    @TableField(value = "max_speed")
    private Float maxSpeed;

    /**
     * 最小翼展
     */
    @TableField(value = "min_wing_span")
    private Float minWingSpan;

    /**
     * 最大翼展
     */
    @TableField(value = "max_wing_span")
    private Float maxWingSpan;

    /**
     * 开始飞行方向
     */
    @TableField(value = "start_flight_direction")
    private Float startFlightDirection;

    /**
     * 结束飞行方向
     */
    @TableField(value = "end_flight_direction")
    private Float endFlightDirection;

    /**
     * 最大面积
     */
    @TableField(value = "max_area")
    private Float maxArea;

    /**
     * 最大置信度
     */
    @TableField(value = "max_confidence_level")
    private Float maxConfidenceLevel;

    /**
     * 第一帧的切片地址
     */
    @TableField(value = "start_img_url")
    private String startImgUrl;

    /**
     * 最后一帧的切片地址
     */
    @TableField(value = "end_img_url")
    private String endImgUrl;

    /**
     * 最大只数
     */
    @TableField(value = "max_target_count")
    private Integer maxTargetCount;

    /**
     * 离跑道最小距离, 单位米
     */
    @TableField(value = "min_runway_distance")
    private Double minRunwayDistance;

    /**
     * 离跑道最大距离，单位米
     */
    @TableField(value = "max_runway_distance")
    private Double maxRunwayDistance;

    /**
     * 离跑道中心点最小距离, 单位米
     */
    @TableField(value = "min_runway_center_distance")
    private Double minRunwayCenterDistance;

    /**
     * 离跑道中心点最大距离，单位米
     */
    @TableField(value = "max_runway_center_distance")
    private Double maxRunwayCenterDistance;

    /**
     * 目标大小,0点目标,1面目标
     */
    @TableField(value = "target_size")
    private Integer targetSize;

    /**
     * 细分识别类型,子类
     */
    @TableField(value = "detect_sub_type")
    private String detectSubType;

    /**
     * 主要识别类型,一级分类
     */
    @TableField(value = "detect_type")
    private String detectType;

    /**
     * 手动标记类型,无人机/空飘物/鸟类/鸟群/飞机/违建/虚景
     */
    @TableField(value = "mark_type")
    private String markType;

    /**
     * 手动标记子类型
     */
    @TableField(value = "mark_sub_type")
    private String markSubType;

    /**
     * 危险等级
     *
     * @see com.allin.silas.kn.app.entity.KnBirdInfoDangerLevel
     */
    @TableField(value = "danger_level")
    private Integer dangerLevel;

    /**
     * 碰撞可能性
     */
    @TableField(value = "collision_possibility")
    private Integer collisionPossibility;

    /**
     * 推送状态,0未推送,1已推送
     */
    @TableField(value = "push_status")
    private Integer pushStatus;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    private LocalDateTime endTime;

    /**
     * 设备运行状态
     *
     * @see DevDetectRunModeEnums#code
     */
    @TableField(value = "dev_run_model")
    private Integer devRunModel;

    /**
     * 合并记录总数
     */
    @TableField(value = "merged_count")
    private Integer mergedCount;

    /**
     * 合并时间
     */
    @TableField(value = "merged_time")
    private LocalDateTime mergedTime;

    /**
     * 合并状态,0未合并,1已合并
     */
    @TableField(value = "merged_status")
    private Integer mergedStatus;

    /**
     * 是否删除,0-否,1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}