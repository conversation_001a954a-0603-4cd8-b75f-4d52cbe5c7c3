package com.allin.silas.visual.app.enums;

import cn.hutool.core.collection.CollUtil;
import com.allin.view.base.enums.base.IEnums;
import jakarta.annotation.Nullable;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.List;

/**
 * 碰撞可能性枚举
 * 根据离跑道最大距离(米)计算碰撞可能性等级
 *
 * <AUTHOR>
 * @since 2025/7/12
 */
@Slf4j
public enum CollisionPossibilityEnums implements IEnums {

    /**
     * 极不可能（501米以上）
     */
    EXTREMELY_UNLIKELY(1, "极不可能", 501, Integer.MAX_VALUE),

    /**
     * 不太可能（301-500米）
     */
    UNLIKELY(2, "不太可能", 301, 500),

    /**
     * 可能的（101-300米）
     */
    POSSIBLE(3, "可能的", 101, 300),

    /**
     * 很可能（31-100米）
     */
    LIKELY(4, "很可能", 31, 100),

    /**
     * 极有可能（0-30米）
     */
    EXTREMELY_LIKELY(5, "极有可能", 0, 30);

    private final Integer code;

    private final String desc;

    /**
     * 距离范围最小值（米）
     */
    @Getter
    private final int minDistance;

    /**
     * 距离范围最大值（米）
     */
    @Getter
    private final int maxDistance;

    CollisionPossibilityEnums(Integer code, String desc, int minDistance, int maxDistance) {
        this.code = code;
        this.desc = desc;
        this.minDistance = minDistance;
        this.maxDistance = maxDistance;
    }

    /**
     * 根据距离计算碰撞可能性等级
     *
     * @param distance 离跑道的距离（米）
     * @return 碰撞可能性枚举，如果距离为null或负数则返回null
     */
    @Nullable
    public static CollisionPossibilityEnums calculateByDistance(Double distance) {
        if (distance == null || distance < 0) {
            return null;
        }

        // 转换为整数进行比较
        int distanceInt = distance.intValue();

        for (CollisionPossibilityEnums possibility : values()) {
            if (distanceInt >= possibility.minDistance && distanceInt <= possibility.maxDistance) {
                return possibility;
            }
        }

        log.error("距离{}米对应的碰撞可能性等级不存在", distance);
        return null;
    }

    /**
     * 根据距离获取碰撞可能性等级代码
     *
     * @param distance 离跑道的距离（米）
     * @return 碰撞可能性等级代码，如果距离为null或负数则返回null
     */
    @Nullable
    public static Integer getCodeByDistance(Double distance) {
        CollisionPossibilityEnums possibility = calculateByDistance(distance);
        return possibility != null ? possibility.getCode() : null;
    }

    /**
     * 检查距离是否在指定的可能性等级范围内
     *
     * @param distance    离跑道的距离（米）
     * @param possibility 碰撞可能性等级
     * @return 如果距离在范围内返回true，否则返回false
     */
    public static boolean isInRange(Double distance, CollisionPossibilityEnums possibility) {
        if (distance == null || possibility == null || distance < 0) {
            return false;
        }

        int distanceInt = distance.intValue();
        return distanceInt >= possibility.minDistance && distanceInt <= possibility.maxDistance;
    }

    /**
     * 计算碰撞可能性
     * 从多个距离中找出碰撞可能性最高的等级
     *
     * @param distances 距离列表（米）
     * @return 最高的碰撞可能性等级
     */
    @Nullable
    public static CollisionPossibilityEnums calculateHighestRisk(List<Double> distances) {
        if (CollUtil.isEmpty(distances)) {
            log.error("计算撞可能性的距离列表为空");
            return null;
        }

        return distances.stream()
                .map(CollisionPossibilityEnums::calculateByDistance)
                .filter(java.util.Objects::nonNull)
                .max(Comparator.comparingInt(CollisionPossibilityEnums::getCode))
                .orElse(null);
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
