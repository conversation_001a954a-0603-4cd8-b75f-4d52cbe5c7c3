package com.allin.silas.visual.app.enums;

import com.allin.silas.dev.detect.app.enums.DevDetectTypeEnums;
import com.allin.view.base.enums.base.IEnums;

import java.util.List;

/**
 * 采集类型枚举
 */
public enum CollectTypeEnums implements IEnums {

    VIDEO(1, "视频"),
    SLICE(2, "切片");

    final Integer code;

    final String desc;

    CollectTypeEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 搜跟设备就是视频否则是切片
     */
    public static List<String> getByCode(Integer code) {
        return code == 1 ? DevDetectTypeEnums.listSearch() : DevDetectTypeEnums.listCamera();
    }

    /**
     * 搜跟设备就是视频否则是切片
     */
    public static CollectTypeEnums getByDevType(String devType) {
        return DevDetectTypeEnums.listSearch().contains(devType) ? VIDEO : SLICE;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
