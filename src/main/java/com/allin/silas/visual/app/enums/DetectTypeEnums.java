package com.allin.silas.visual.app.enums;

import com.allin.silas.visual.app.entity.VisualTargetMergedInfo;
import com.allin.silas.visual.utils.VisualDetectUtils;
import com.allin.view.base.enums.base.IEnums;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * 识别主类型枚举
 * 鸟科, 鸟群, 无人机, 空飘物, 违建
 * @see VisualDetectUtils
 */
@Slf4j
public enum DetectTypeEnums implements IEnums {

    BIRD("鸟科", null),
    BIRDS("鸟群", 5),
    UAV("无人机", 5),
    FLOAT_OBJECT("空飘物", 5),
    BACKGROUND("违建", 5);

    final String code;

    @Getter
    final Integer dangerLevel;

    DetectTypeEnums(String code, Integer dangerLevel) {
        this.code = code;
        this.dangerLevel = dangerLevel;
    }

    /**
     * 获取所有的识别主类型
     *
     * @see VisualTargetMergedInfo#getDetectType()
     */
    public static List<String> listDetectType() {
        return Arrays.stream(DetectTypeEnums.values()).map(DetectTypeEnums::getCode).toList();
    }

    @Override
    public String getCode() {
        return code;
    }
}
