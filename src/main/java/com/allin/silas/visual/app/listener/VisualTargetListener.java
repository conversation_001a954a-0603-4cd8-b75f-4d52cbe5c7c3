package com.allin.silas.visual.app.listener;

import cn.hutool.core.collection.CollUtil;
import com.allin.silas.common.enums.WsMessageType;
import com.allin.silas.map.client.MapRegionFacade;
import com.allin.silas.visual.adapter.dto.VisualTargetToWsDto;
import com.allin.silas.visual.app.entity.VisualTargetOriginalInfo;
import com.allin.silas.visual.app.manager.VisualTargetTraceRegionManager;
import com.allin.silas.visual.client.event.VisualTargetOriginalSavedEvent;
import com.allin.view.ws.entity.WsMessage;
import com.allin.view.ws.handler.CustomParamWebSocketHandler;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 可视目标事件监控
 *
 * <AUTHOR>
 * @since 2025/7/17
 */
@Component
public class VisualTargetListener {

    private final CustomParamWebSocketHandler webSocketHandler;

    private final MapRegionFacade mapRegionFacade;

    private final VisualTargetTraceRegionManager visualTargetTraceRegionManager;

    public VisualTargetListener(CustomParamWebSocketHandler webSocketHandler, MapRegionFacade mapRegionFacade, VisualTargetTraceRegionManager visualTargetTraceRegionManager) {
        this.webSocketHandler = webSocketHandler;
        this.mapRegionFacade = mapRegionFacade;
        this.visualTargetTraceRegionManager = visualTargetTraceRegionManager;
    }

    /**
     * 发送目标数据到前端
     */
    @Async
    @EventListener
    public void sendTargetToWeb(VisualTargetOriginalSavedEvent savedEvent) {
        VisualTargetOriginalInfo info = savedEvent.getInfo();
        VisualTargetToWsDto dto = new VisualTargetToWsDto(savedEvent.getInfo());
        webSocketHandler.getSender().sendToParam(info.getProjectId(),
                WsMessage.of(WsMessageType.RANGE_TARGET.getCode(), dto));
    }

    /**
     * 保存目标所在区域
     */
    @Async
    @EventListener
    public void saveTargetTraceRegion(VisualTargetOriginalSavedEvent savedEvent) {
        VisualTargetOriginalInfo info = savedEvent.getInfo();
        // 判断目标是否在区域内
        List<String> regionIds = mapRegionFacade.calculateRegion(info.getLongitude(), info.getLatitude());
        // 插入可视目标区域表
        if (CollUtil.isNotEmpty(regionIds)) {
            visualTargetTraceRegionManager.saveTargetTraceRegion(regionIds, info);
        }
    }
}

