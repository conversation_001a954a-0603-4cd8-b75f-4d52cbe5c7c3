package com.allin.silas.visual.app.manager.impl;

import com.allin.silas.visual.app.entity.VisualTargetOriginalInfo;
import com.allin.silas.visual.app.entity.VisualTargetTraceRegion;
import com.allin.silas.visual.app.manager.VisualTargetTraceRegionManager;
import com.allin.silas.visual.infra.repository.VisualTargetTraceRegionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/24
 */
@Slf4j
@Service
public class VisualTargetTraceRegionManagerImpl implements VisualTargetTraceRegionManager {

    private final VisualTargetTraceRegionMapper traceRegionMapper;

    public VisualTargetTraceRegionManagerImpl(VisualTargetTraceRegionMapper traceRegionMapper) {
        this.traceRegionMapper = traceRegionMapper;
    }

    @Override
    public void saveTargetTraceRegion(List<String> regionIds, VisualTargetOriginalInfo originalInfo) {
        for (String regionId : regionIds) {
            try {
                VisualTargetTraceRegion visualTargetTraceRegion = new VisualTargetTraceRegion();
                visualTargetTraceRegion.setProjectId(originalInfo.getProjectId());
                visualTargetTraceRegion.setBackendBatchNumber(originalInfo.getBackendBatchNumber());
                visualTargetTraceRegion.setMapRegionId(regionId);
                visualTargetTraceRegion.setCreatedTime(originalInfo.getCreatedTime());
                traceRegionMapper.insert(visualTargetTraceRegion);
            } catch (DataIntegrityViolationException e) {
                // 数据已存在，忽略异常
            }
        }
    }
}
