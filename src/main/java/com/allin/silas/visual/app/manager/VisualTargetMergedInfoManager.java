package com.allin.silas.visual.app.manager;

import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoListQuery;
import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoPageQuery;
import com.allin.silas.visual.adapter.vo.VisualTargetInfoListVo;
import com.allin.silas.visual.adapter.vo.VisualTargetMergedInfoVo;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 可视目标合并信息通用逻辑层接口
 *
 * <AUTHOR>
 * @since 2025/6/25
 */
public interface VisualTargetMergedInfoManager {
    /**
     * 分页查询
     */
    Page<VisualTargetInfoListVo> page(PageParam pageParam, VisualTargetMergedInfoPageQuery query);

    /**
     * 列表查询
     */
    List<VisualTargetInfoListVo> list(String tableName, VisualTargetMergedInfoListQuery query);

    /**
     * 查询详情
     */
    VisualTargetMergedInfoVo info(LocalDate date, String backendBatchNumber);

    /**
     * 获取缓存中的可识别目标数量
     */
    Long getVisualTargetByCacheTargetNum(String devNum, String backendBatchNumber, LocalDateTime createTime);
}
