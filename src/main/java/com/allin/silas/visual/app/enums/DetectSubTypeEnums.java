package com.allin.silas.visual.app.enums;

import com.allin.silas.visual.app.entity.VisualTargetMergedInfo;
import com.allin.silas.visual.utils.VisualDetectUtils;
import com.allin.view.base.enums.base.IEnums;

import java.util.Collection;

/**
 * 识别子类型枚举
 *
 * @see VisualDetectUtils
 */
public enum DetectSubTypeEnums implements IEnums {

    BACK(0L, "背景"),
    BIRD(1L, "鸟"),
    PLANE(2L, "飞机"),
    BIRDS(3L, "鸟群");

    final Long code;

    final String desc;

    DetectSubTypeEnums(Long code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取所有的识别子类型
     *
     * @see VisualTargetMergedInfo#getDetectSubType()
     */
    public static Collection<String> listDetectSubType() {
        return VisualDetectUtils.TARGET_DETECT_TYPE_MAP.values();
    }

    @Override
    public Long getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
