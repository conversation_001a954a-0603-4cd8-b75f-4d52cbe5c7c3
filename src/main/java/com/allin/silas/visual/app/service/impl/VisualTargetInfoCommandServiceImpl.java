package com.allin.silas.visual.app.service.impl;

import com.allin.silas.common.mybatis.config.MybatisDynamicTableNameHandler;
import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.event.client.EventInvasionFacade;
import com.allin.silas.event.client.dto.TargetManualPushEventInvasionDto;
import com.allin.silas.radar.client.RadarTargetFacade;
import com.allin.silas.visual.adapter.dto.DelVisualTargetDto;
import com.allin.silas.visual.adapter.dto.VisualTargetMarkDto;
import com.allin.silas.visual.adapter.dto.VisualTargetPushDto;
import com.allin.silas.visual.adapter.vo.VisualTargetMergedInfoVo;
import com.allin.silas.visual.adapter.vo.VisualTargetTraceRegionVo;
import com.allin.silas.visual.app.entity.VisualTargetMergedInfo;
import com.allin.silas.visual.app.entity.VisualTargetOriginalInfo;
import com.allin.silas.visual.app.entity.VisualTargetTraceRegion;
import com.allin.silas.visual.app.manager.VisualTargetInfoManager;
import com.allin.silas.visual.app.service.VisualTargetInfoCommandService;
import com.allin.silas.visual.infra.repository.VisualTargetMergedInfoMapper;
import com.allin.silas.visual.infra.repository.VisualTargetOriginalInfoMapper;
import com.allin.silas.visual.infra.repository.VisualTargetTraceRegionMapper;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2025/6/26
 */
@Service
public class VisualTargetInfoCommandServiceImpl implements VisualTargetInfoCommandService {

    private final VisualTargetMergedInfoMapper mergedInfoMapper;

    private final VisualTargetOriginalInfoMapper originalInfoMapper;

    private final VisualTargetTraceRegionMapper traceRegionMapper;

    private final VisualTargetInfoManager visualTargetInfoManager;

    private final RadarTargetFacade radarTargetFacade;

    private final EventInvasionFacade eventInvasionFacade;

    public VisualTargetInfoCommandServiceImpl(VisualTargetMergedInfoMapper mergedInfoMapper,
                                              VisualTargetOriginalInfoMapper originalInfoMapper,
                                              VisualTargetTraceRegionMapper traceRegionMapper,
                                              VisualTargetInfoManager visualTargetInfoManager,
                                              RadarTargetFacade radarTargetFacade,
                                              EventInvasionFacade eventInvasionFacade) {
        this.mergedInfoMapper = mergedInfoMapper;
        this.originalInfoMapper = originalInfoMapper;
        this.traceRegionMapper = traceRegionMapper;
        this.visualTargetInfoManager = visualTargetInfoManager;
        this.radarTargetFacade = radarTargetFacade;
        this.eventInvasionFacade = eventInvasionFacade;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized boolean mark(VisualTargetMarkDto markDto) {
        final String tableName = TableNameUtils.getVisualTargetMergedInfo(markDto.getDate());
        markDto.setDangerLevel(visualTargetInfoManager.getTargetDangerLevel(markDto.getMarkSubType()));
        if (mergedInfoMapper.updateMark(tableName, markDto) > 0) {
            return radarTargetFacade.syncVisualTargetType(markDto.getBackendBatchNumber(),
                    markDto.getDate(),
                    markDto.getMarkType());
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized boolean push(VisualTargetPushDto pushDto) {
        final String tableName = TableNameUtils.getVisualTargetMergedInfo(pushDto.getDate());
        final VisualTargetMergedInfoVo visualTargetMergedInfo = mergedInfoMapper.info(tableName, pushDto.getBackendBatchNumber());
        if (visualTargetMergedInfo == null) {
            throw new ValidationFailureException(I18nUtil.isNotExist());
        }
        if (visualTargetMergedInfo.getPushStatus() == 1) {
            throw new ValidationFailureException("请勿重复推送目标检测事件");
        }
        visualTargetMergedInfo.setPushStatus(1);
        if (mergedInfoMapper.setPushed(tableName, pushDto.getBackendBatchNumber()) > 0) {
            final var pushEventInvasionDto = new TargetManualPushEventInvasionDto(pushDto.getHandleEventInvasionDto());
            pushEventInvasionDto.setSourceId(visualTargetMergedInfo.getBackendBatchNumber());
            if (visualTargetMergedInfo.getMarkType() != null) {
                pushEventInvasionDto.setEventType(visualTargetMergedInfo.getMarkType());
            } else {
                pushEventInvasionDto.setEventType(visualTargetMergedInfo.getDetectType());
            }
            pushEventInvasionDto.setDiscoverTime(visualTargetMergedInfo.getStartTime());
            pushEventInvasionDto.setDiscoverLongitude(visualTargetMergedInfo.getEndLongitude());
            pushEventInvasionDto.setDiscoverLatitude(visualTargetMergedInfo.getEndLatitude());
            pushEventInvasionDto.setAltitude(visualTargetMergedInfo.getMaxHeight().intValue());
            pushEventInvasionDto.setDangerLevel(visualTargetMergedInfo.getDangerLevel());

            // 区域取最后一个
            final VisualTargetTraceRegionVo lastRegion = traceRegionMapper.getLastRegion(visualTargetMergedInfo.getBackendBatchNumber());
            if (lastRegion != null) {
                pushEventInvasionDto.setDiscoverRegionId(lastRegion.getMapRegionId());
            }
            eventInvasionFacade.targetManualPushEvent(pushEventInvasionDto);
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void del(DelVisualTargetDto delVisualTargetDto) {
        // 设置表名
        MybatisDynamicTableNameHandler.setSuffix(delVisualTargetDto.getDate());
        traceRegionMapper.delete(Wrappers.lambdaQuery(VisualTargetTraceRegion.class)
                .in(VisualTargetTraceRegion::getBackendBatchNumber, delVisualTargetDto.getBatchNumbers()));
        mergedInfoMapper.delete(Wrappers.lambdaQuery(VisualTargetMergedInfo.class)
                .in(VisualTargetMergedInfo::getBackendBatchNumber, delVisualTargetDto.getBatchNumbers()));
        originalInfoMapper.delete(Wrappers.lambdaQuery(VisualTargetOriginalInfo.class)
                .in(VisualTargetOriginalInfo::getBackendBatchNumber, delVisualTargetDto.getBatchNumbers()));
    }
}
