package com.allin.silas.visual.app.manager.impl;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.kn.client.KnBirdInfoDangerLevelFacade;
import com.allin.silas.visual.app.enums.DetectTypeEnums;
import com.allin.silas.visual.app.manager.VisualTargetInfoManager;
import com.allin.view.base.enums.base.IEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 可视目标通用逻辑层接口实现
 *
 * <AUTHOR>
 * @since 2025/7/2
 */
@Slf4j
@Service
public class VisualTargetInfoManagerImpl implements VisualTargetInfoManager {

    private final KnBirdInfoDangerLevelFacade knBirdInfoDangerLevelFacade;

    public VisualTargetInfoManagerImpl(KnBirdInfoDangerLevelFacade knBirdInfoDangerLevelFacade) {
        this.knBirdInfoDangerLevelFacade = knBirdInfoDangerLevelFacade;
    }

    @Override
    public int getTargetDangerLevel(String detectSubType) {
        // 没有识别类型
        if (StrUtil.isBlank(detectSubType)) {
            return 1;
        }
        final Optional<DetectTypeEnums> typeEnum = IEnums.tryFindByCode(DetectTypeEnums.class, detectSubType);
        final Optional<Integer> dangerLevel = typeEnum.map(DetectTypeEnums::getDangerLevel);

        return dangerLevel.orElseGet(() -> knBirdInfoDangerLevelFacade.getDangerLevelCache(detectSubType));
    }

}
