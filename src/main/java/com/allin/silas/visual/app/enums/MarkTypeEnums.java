package com.allin.silas.visual.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 手动标记类型枚举
 * 无人机/空飘物/鸟类/鸟群/飞机/违建/虚景
 */
public enum MarkTypeEnums implements IEnums {

    UAV("无人机"),
    FLOAT_OBJECT("空飘物"),
    BIRD("鸟类"),
    BIRDS("鸟群"),
    AIRCRAFT("飞机"),
    BACKGROUND("违建"),
    VIRTUAL_SCENE("虚景");

    final String code;

    MarkTypeEnums(String code) {
        this.code = code;
    }

    @Override
    public String getCode() {
        return code;
    }
}
