package com.allin.silas.visual.app.service;

import com.allin.silas.visual.adapter.dto.DelVisualTargetDto;
import com.allin.silas.visual.adapter.dto.VisualTargetMarkDto;
import com.allin.silas.visual.adapter.dto.VisualTargetPushDto;

/**
 * 可视目标信息命令服务接口
 *
 * <AUTHOR>
 * @since 2025/6/25
 */
public interface VisualTargetInfoCommandService {

    /**
     * 标记可视目标类型
     */
    boolean mark(VisualTargetMarkDto markDto);

    /**
     * 推送可视目标类型
     */
    boolean push(VisualTargetPushDto pushDto);

    /**
     * 删除可视目标
     */
    void del(DelVisualTargetDto delVisualTargetDto);
}
