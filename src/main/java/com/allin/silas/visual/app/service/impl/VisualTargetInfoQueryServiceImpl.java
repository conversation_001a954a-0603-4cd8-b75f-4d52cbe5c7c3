package com.allin.silas.visual.app.service.impl;

import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoListQuery;
import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoPageQuery;
import com.allin.silas.visual.adapter.vo.VisualTargetInfoListVo;
import com.allin.silas.visual.adapter.vo.VisualTargetMergedInfoVo;
import com.allin.silas.visual.adapter.vo.VisualTargetOriginalTraceVo;
import com.allin.silas.visual.app.manager.VisualTargetMergedInfoManager;
import com.allin.silas.visual.app.manager.VisualTargetOriginalInfoManager;
import com.allin.silas.visual.app.service.VisualTargetInfoQueryService;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 可视目标合并信息查询服务接口实现
 *
 * <AUTHOR>
 * @since 2025/6/25
 */
@Service
public class VisualTargetInfoQueryServiceImpl implements VisualTargetInfoQueryService {

    private final VisualTargetMergedInfoManager visualTargetMergedInfoManager;

    private final VisualTargetOriginalInfoManager visualTargetOriginalInfoManager;

    public VisualTargetInfoQueryServiceImpl(VisualTargetMergedInfoManager visualTargetMergedInfoManager,
                                            VisualTargetOriginalInfoManager visualTargetOriginalInfoManager) {
        this.visualTargetMergedInfoManager = visualTargetMergedInfoManager;
        this.visualTargetOriginalInfoManager = visualTargetOriginalInfoManager;
    }

    @Override
    public Page<VisualTargetInfoListVo> page(PageParam pageParam, VisualTargetMergedInfoPageQuery query) {
        return visualTargetMergedInfoManager.page(pageParam, query);
    }

    @Override
    public List<VisualTargetInfoListVo> listExport(VisualTargetMergedInfoListQuery query) {
        String tableName = TableNameUtils.getVisualTargetMergedInfo(query.getStartTime());
        return visualTargetMergedInfoManager.list(tableName, query);
    }

    @Override
    public VisualTargetMergedInfoVo info(LocalDate date, String batchNumber) {
        return visualTargetMergedInfoManager.info(date, batchNumber);
    }

    @Override
    public List<VisualTargetOriginalTraceVo> trace(LocalDate date, String batchNumber) {
        return visualTargetOriginalInfoManager.trace(date, batchNumber);
    }

    @Override
    public List<String> listImgPath(LocalDate date, List<String> batchNumbers) {
        return visualTargetOriginalInfoManager.listImgPath(date, batchNumbers);
    }
}
