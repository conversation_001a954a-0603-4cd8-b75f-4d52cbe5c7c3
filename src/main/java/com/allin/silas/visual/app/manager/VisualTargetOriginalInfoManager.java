package com.allin.silas.visual.app.manager;

import com.allin.silas.visual.adapter.vo.VisualTargetOriginalTraceVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 可视目标原始信息通用逻辑层接口
 *
 * <AUTHOR>
 * @since 2025/6/27
 */
public interface VisualTargetOriginalInfoManager {

    /**
     * 查询目标轨迹列表
     */
    List<VisualTargetOriginalTraceVo> trace(LocalDate date, String batchNumber);

    /**
     * 查询目标图片列表
     */
    List<String> listImgPath(LocalDate date, List<String> batchNumbers);
}
