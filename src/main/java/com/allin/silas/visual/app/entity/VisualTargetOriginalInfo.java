package com.allin.silas.visual.app.entity;

import com.allin.silas.dev.detect.app.enums.DevDetectRunModeEnums;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 原始的可视目标信息表
 */
@Data
@TableName(value = "visual_target_original_info")
public class VisualTargetOriginalInfo {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 设备编号
     */
    @TableField(value = "dev_num")
    private String devNum;

    /**
     * 设备类型
     */
    @TableField(value = "dev_type")
    private String devType;

    /**
     * 后端生成的批次号
     */
    @TableField(value = "backend_batch_number")
    private String backendBatchNumber;

    /**
     * 前端设备发送的批次号
     */
    @TableField(value = "frontend_batch_number")
    private String frontendBatchNumber;

    /**
     * 全景图编号
     */
    @TableField(value = "panorama_id")
    private String panoramaId;

    /**
     * 全景图x坐标
     */
    @TableField(value = "pano_x")
    private Double panoX;

    /**
     * 全景图y坐标
     */
    @TableField(value = "pano_y")
    private Double panoY;

    /**
     * 轨迹x坐标
     */
    @TableField(value = "trajectory_x")
    private String trajectoryX;

    /**
     * 轨迹y坐标
     */
    @TableField(value = "trajectory_y")
    private String trajectoryY;

    /**
     * 方位轨迹
     */
    @TableField(value = "azimuth_trajectory")
    private String azimuthTrajectory;

    /**
     * 俯仰轨迹
     */
    @TableField(value = "pitch_trajectory")
    private String pitchTrajectory;

    /**
     * 经度
     */
    @TableField(value = "longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @TableField(value = "latitude")
    private Double latitude;

    /**
     * 方位值
     */
    @TableField(value = "azimuth")
    private Float azimuth;

    /**
     * 俯仰值
     */
    @TableField(value = "pitch")
    private Float pitch;

    /**
     * 距离
     */
    @TableField(value = "distance")
    private Float distance;

    /**
     * 高度
     */
    @TableField(value = "height")
    private Float height;

    /**
     * 当前分割位
     */
    @TableField(value = "split_bit")
    private Integer splitBit;

    /**
     * 速度
     */
    @TableField(value = "speed")
    private Float speed;

    /**
     * 翼展
     */
    @TableField(value = "wing_span")
    private Float wingSpan;

    /**
     * 飞行方向
     */
    @TableField(value = "flight_direction")
    private Float flightDirection;

    /**
     * 目标大小,0点目标,1面目标
     */
    @TableField(value = "target_size")
    private Integer targetSize;

    /**
     * 细分识别类型,子类
     */
    @TableField(value = "detect_sub_type")
    private String detectSubType;

    /**
     * 主要识别类型,一级分类
     */
    @TableField(value = "detect_type")
    private String detectType;

    /**
     * 置信度
     */
    @TableField(value = "confidence_level")
    private Float confidenceLevel;

    /**
     * 面积
     */
    @TableField(value = "area")
    private Float area;

    /**
     * 切片宽度
     */
    @TableField(value = "img_width")
    private Integer imgWidth;

    /**
     * 切片高度
     */
    @TableField(value = "img_height")
    private Integer imgHeight;

    /**
     * 切片地址
     */
    @TableField(value = "img_url")
    private String imgUrl;

    /**
     * 目标数量
     */
    @TableField(value = "target_count")
    private Integer targetCount;

    /**
     * 设备运行状态
     *
     * @see DevDetectRunModeEnums#code
     */
    @TableField(value = "dev_run_model")
    private Integer devRunModel;

    /**
     * 离跑道的距离
     */
    @TableField(value = "runway_distance")
    private Double runwayDistance;

    /**
     * 离跑道中心距离
     */
    @TableField(value = "runway_center_distance")
    private Double runwayCenterDistance;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 是否删除,0-否,1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}