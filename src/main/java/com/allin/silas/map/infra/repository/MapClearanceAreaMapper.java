package com.allin.silas.map.infra.repository;

import com.allin.silas.map.app.entity.MapClearanceArea;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MapClearanceAreaMapper extends BaseMapper<MapClearanceArea> {

    /**
     * 根据跑道id删除净空区
     */
    default void deleteByRunwayId(String runWayId) {
        delete(Wrappers.lambdaQuery(MapClearanceArea.class).eq(MapClearanceArea::getMapRunwayId, runWayId));
    }
}