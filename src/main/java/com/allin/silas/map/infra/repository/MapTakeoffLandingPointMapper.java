package com.allin.silas.map.infra.repository;

import com.allin.silas.map.adapter.dto.AddMapTakeoffLandingPointDto;
import com.allin.silas.map.app.entity.MapRunway;
import com.allin.silas.map.app.entity.MapTakeoffLandingPoint;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface MapTakeoffLandingPointMapper extends BaseMapper<MapTakeoffLandingPoint> {

    /**
     * 根据跑道id删除起飞降落点
     */
    default void deleteByRunwayId(String runWayId) {
        delete(Wrappers.lambdaQuery(MapTakeoffLandingPoint.class)
                .eq(MapTakeoffLandingPoint::getMapRunwayId, runWayId));
    }

    /**
     * 批量插入
     */
    default void insert(MapRunway mapRunway, List<AddMapTakeoffLandingPointDto> takeoffLandingPoints) {
        final List<MapTakeoffLandingPoint> points = takeoffLandingPoints.stream()
                .map(addMapTakeoffLandingPointDto -> {
                    var takeoffLandingPoint = new MapTakeoffLandingPoint();
                    takeoffLandingPoint.setMapRunwayId(mapRunway.getId());
                    takeoffLandingPoint.setPointType(addMapTakeoffLandingPointDto.getPointType());
                    takeoffLandingPoint.setPointCoord(addMapTakeoffLandingPointDto.getPointCoord());
                    takeoffLandingPoint.setPointName(addMapTakeoffLandingPointDto.getPointName());
                    takeoffLandingPoint.setPointSeq(addMapTakeoffLandingPointDto.getPointSeq());
                    return takeoffLandingPoint;
                }).toList();
        insert(points);
    }
}