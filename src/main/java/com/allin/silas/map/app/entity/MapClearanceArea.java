package com.allin.silas.map.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 地图跑道净空区表
 */
@Data
@TableName(value = "map_clearance_area")
public class MapClearanceArea {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 跑道id
     */
    @TableField(value = "map_runway_id")
    private String mapRunwayId;

    /**
     * 净空区编号,1第一段,2第二段,3水平段
     */
    @TableField(value = "area_code")
    private Integer areaCode;

    /**
     * 区域geojson
     */
    @TableField(value = "area_coord")
    private String areaCoord;

    /**
     * 净空区段长度
     */
    @TableField(value = "area_length")
    private Integer areaLength;

    /**
     * 净空区方向,1:A到B,2:B到A
     */
    @TableField(value = "area_direction")
    private Integer areaDirection;

    /**
     * 显示配置
     */
    @TableField(value = "display_config")
    private String displayConfig;

    /**
     * 是否删除,0-否,1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}