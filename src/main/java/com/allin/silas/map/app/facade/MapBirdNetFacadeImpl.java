package com.allin.silas.map.app.facade;

import com.allin.silas.map.app.entity.MapBirdNet;
import com.allin.silas.map.client.MapBirdNetFacade;
import com.allin.silas.map.infra.repository.MapBirdNetMapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@Service
@Slf4j
public class MapBirdNetFacadeImpl implements MapBirdNetFacade {

    private final MapBirdNetMapper mapBirdNetMapper;

    public MapBirdNetFacadeImpl(MapBirdNetMapper mapBirdNetMapper) {
        this.mapBirdNetMapper = mapBirdNetMapper;
    }

    @Override
    public Map<String, String> mapBirdNetIdByProjectId(String projectId) {
        List<MapBirdNet> mapBirdNetList = mapBirdNetMapper.selectList(Wrappers.lambdaQuery(MapBirdNet.class)
                .eq(MapBirdNet::getProjectId, projectId)
                .eq(MapBirdNet::getIsDeleted, 0));
        return CollectionUtils.isNotEmpty(mapBirdNetList) ?
                mapBirdNetList.stream()
                        .collect(Collectors.toMap(
                                MapBirdNet::getBirdNetName,
                                MapBirdNet::getId,
                                (existing, replacement) -> replacement)) : Collections.emptyMap();
    }
}
