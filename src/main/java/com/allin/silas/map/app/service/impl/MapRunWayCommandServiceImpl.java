package com.allin.silas.map.app.service.impl;

import com.allin.silas.map.adapter.dto.AddMapRunwayFormDto;
import com.allin.silas.map.adapter.dto.EditMapRunwayFromDto;
import com.allin.silas.map.app.entity.MapRunway;
import com.allin.silas.map.app.manager.MapClearanceAreaManager;
import com.allin.silas.map.app.manager.MapRunwayManager;
import com.allin.silas.map.app.service.MapRunWayCommandService;
import com.allin.silas.map.infra.repository.MapClearanceAreaMapper;
import com.allin.silas.map.infra.repository.MapRunwayMapper;
import com.allin.silas.map.infra.repository.MapTakeoffLandingPointMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 地图跑道接口
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
@Service
public class MapRunWayCommandServiceImpl implements MapRunWayCommandService {

    private final MapRunwayMapper runwayMapper;

    private final MapClearanceAreaMapper clearanceAreaMapper;

    private final MapTakeoffLandingPointMapper takeoffLandingPointMapper;

    private final MapRunwayManager runwayManager;

    private final MapClearanceAreaManager clearanceAreaManager;

    public MapRunWayCommandServiceImpl(MapRunwayMapper runwayMapper,
                                       MapClearanceAreaMapper clearanceAreaMapper,
                                       MapTakeoffLandingPointMapper takeoffLandingPointMapper,
                                       MapRunwayManager runwayManager,
                                       MapClearanceAreaManager clearanceAreaManager) {
        this.runwayMapper = runwayMapper;
        this.clearanceAreaMapper = clearanceAreaMapper;
        this.takeoffLandingPointMapper = takeoffLandingPointMapper;
        this.runwayManager = runwayManager;
        this.clearanceAreaManager = clearanceAreaManager;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean del(String id) {
        boolean isSuccess = runwayMapper.deleteById(id) > 0;
        if (isSuccess) {
            clearanceAreaMapper.deleteByRunwayId(id);
            takeoffLandingPointMapper.deleteByRunwayId(id);
        }
        return isSuccess;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean add(AddMapRunwayFormDto formDto) {
        // 保存跑道信息
        final MapRunway mapRunway = runwayManager.addRunway(formDto.getRunway());
        if (mapRunway != null) {
            // 生成净空区
            clearanceAreaManager.generateAreas(mapRunway, formDto.getClearanceAreas());
            // 保存起飞降落点
            takeoffLandingPointMapper.insert(mapRunway, formDto.getTakeoffLandingPoints());
            return true;
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean edit(EditMapRunwayFromDto formDto) {
        // 保存跑道信息
        final MapRunway mapRunway = runwayManager.editRunway(formDto);
        if (mapRunway != null) {
            // 删除旧净空区
            clearanceAreaMapper.deleteByRunwayId(mapRunway.getId());
            // 生成净空区
            clearanceAreaManager.generateAreas(mapRunway, formDto.getClearanceAreas());
            // 删除旧起飞降落点
            takeoffLandingPointMapper.deleteByRunwayId(mapRunway.getId());
            // 保存起飞降落点
            takeoffLandingPointMapper.insert(mapRunway, formDto.getTakeoffLandingPoints());
            return true;
        }
        return false;
    }
}
