package com.allin.silas.map.app.manager;

import com.allin.silas.map.app.entity.MapRegion;
import jakarta.annotation.Nullable;

import java.util.List;

/**
 * 地图区域通用逻辑层接口
 *
 * <AUTHOR>
 * @since 2025/6/17
 */
public interface MapRegionManager {

    /**
     * 查询区域名称（优先从缓存获取）
     */
    @Nullable
    String listNameFromCacheById(String id);

    /**
     * 查询区域名称（优先从缓存获取）
     */
    List<String> listNameFromCacheByIds(List<String> id);

    /**
     * 查询上报区域（优先从缓存获取）
     */
    List<MapRegion> listReportRegionFromCache(String projectId);
}
