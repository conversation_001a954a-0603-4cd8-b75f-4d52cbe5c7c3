package com.allin.silas.map.app.facade;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.map.app.entity.MapRunway;
import com.allin.silas.map.app.manager.MapRunwayManager;
import com.allin.silas.map.client.MapRunWayFacade;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.geotools.api.referencing.operation.TransformException;
import org.geotools.geometry.Position2D;
import org.geotools.geometry.jts.JTS;
import org.geotools.referencing.crs.DefaultGeographicCRS;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.operation.distance.DistanceOp;
import org.springframework.stereotype.Service;

/**
 * 地图跑道服务入口
 *
 * <AUTHOR>
 * @since 2025/6/24
 */
@Slf4j
@Service
public class MapRunWayFacadeImpl implements MapRunWayFacade {

    private final MapRunwayManager mapRunwayManager;

    public MapRunWayFacadeImpl(MapRunwayManager mapRunwayManager) {
        this.mapRunwayManager = mapRunwayManager;
    }

    @Nullable
    @Override
    public Double calculateDistance(double longitude, double latitude, String runWayId) {
        if (StrUtil.isBlank(runWayId)) {
            log.error("跑道id不能为空{}", runWayId);
            return null;
        }
        MapRunway mapRunway = mapRunwayManager.getRunwayFromCacheById(runWayId);
        if (mapRunway == null) {
            log.error("未找到{}的跑道数据", runWayId);
            return null;
        }
        if (longitude == 0 && latitude == 0) {
            log.error("经纬度数据异常");
            return null;
        }
        // 跑道中心线
        final LineString centerLineGeometry = mapRunway.centerLineGeometry();
        // 目标的位置
        final Point targetPoint = JTS.toGeometry(new Position2D(DefaultGeographicCRS.WGS84, longitude, latitude));
        // 中心线离目标最近的点
        final Coordinate nearestPoint = DistanceOp.nearestPoints(centerLineGeometry, targetPoint)[0];
        // 目标离跑道中心线的距离
        try {
            return JTS.orthodromicDistance(nearestPoint, targetPoint.getCoordinate(), DefaultGeographicCRS.WGS84);
        } catch (TransformException e) {
            log.error("坐标转换异常", e);
        }
        return null;
    }

    @Nullable
    @Override
    public Double calculateCenterDistance(double longitude, double latitude, String runWayId) {
        if (StrUtil.isBlank(runWayId)) {
            log.error("跑道id不能为空{}", runWayId);
            return null;
        }
        MapRunway mapRunway = mapRunwayManager.getRunwayFromCacheById(runWayId);
        if (mapRunway == null) {
            log.error("未找到{}的跑道数据", runWayId);
            return null;
        }
        if (longitude == 0 && latitude == 0) {
            log.error("经纬度数据异常");
            return null;
        }
        // 跑道中心点
        final Geometry centerGeometry = mapRunway.centerGeometry();
        // 目标的位置
        final Point targetPoint = JTS.toGeometry(new Position2D(DefaultGeographicCRS.WGS84, longitude, latitude));
        // 目标离跑道中心线的距离
        try {
            return JTS.orthodromicDistance(centerGeometry.getCoordinate(), targetPoint.getCoordinate(), DefaultGeographicCRS.WGS84);
        } catch (TransformException e) {
            log.error("坐标转换异常", e);
        }
        return null;
    }
}
