package com.allin.silas.map.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.geotools.data.geojson.GeoJSONReader;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Polygon;

import java.time.LocalDateTime;

/**
 * 地图跑道表
 */
@Data
@TableName(value = "map_runway")
public class MapRunway {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 跑道名称,长度不超过10字
     */
    @TableField(value = "runway_name")
    private String runwayName;

    /**
     * 中心线geojson
     */
    @TableField(value = "center_line_coord")
    private String centerLineCoord;

    /**
     * 中心点geojson
     */
    @TableField(value = "center_coord")
    private String centerCoord;

    /**
     * 跑道区域geojson
     */
    @TableField(value = "area_coord")
    private String areaCoord;

    /**
     * 跑道宽度
     */
    @TableField(value = "runway_width")
    private Integer runwayWidth;

    /**
     * 显示配置
     */
    @TableField(value = "display_config")
    private String displayConfig;

    /**
     * 备注信息
     */
    @TableField(value = "remarks")
    private String remarks;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除,0-否,1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;

    /**
     * 跑道中心点geojson
     */
    @JsonIgnore
    public Geometry centerGeometry() {
        return GeoJSONReader.parseGeometry(centerCoord);
    }

    /**
     * 跑道中心线geojson
     */
    @JsonIgnore
    public LineString centerLineGeometry() {
        return (LineString) GeoJSONReader.parseGeometry(centerLineCoord);
    }

    /**
     * 跑道区域geojson
     */
    @JsonIgnore
    public Polygon areaGeometry() {
        return (Polygon) GeoJSONReader.parseGeometry(areaCoord);
    }
}