package com.allin.silas.map.app.manager;

import com.allin.silas.map.adapter.dto.AddMapRunwayDto;
import com.allin.silas.map.adapter.dto.EditMapRunwayFromDto;
import com.allin.silas.map.app.entity.MapRunway;
import jakarta.annotation.Nullable;

/**
 * 跑道通用逻辑层接口
 *
 * <AUTHOR>
 * @since 2025/6/17
 */
public interface MapRunwayManager {

    /**
     * 新增跑道
     */
    @Nullable
    MapRunway addRunway(AddMapRunwayDto addDto);

    /**
     * 编辑跑道
     */
    @Nullable
    MapRunway editRunway(EditMapRunwayFromDto addDto);

    /**
     * 查询缓存获取跑道信息
     */
    MapRunway getRunwayFromCacheById(String runWayId);
}
