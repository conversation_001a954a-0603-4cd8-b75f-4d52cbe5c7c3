package com.allin.silas.map.app.manager.impl;

import cn.hutool.core.collection.CollUtil;
import com.allin.silas.map.app.entity.MapRegion;
import com.allin.silas.map.app.enums.RegionTypeEnums;
import com.allin.silas.map.app.manager.MapRegionManager;
import com.allin.silas.map.infra.repository.MapRegionMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 地图区域通用逻辑层接口实现
 *
 * <AUTHOR>
 * @since 2025/7/4
 */
@Slf4j
@Service
public class MapRegionManagerImpl implements MapRegionManager {

    private final MapRegionMapper mapRegionMapper;

    public MapRegionManagerImpl(MapRegionMapper mapRegionMapper) {
        this.mapRegionMapper = mapRegionMapper;
    }

    @Cacheable(value = "allRegion#20", key = "'id:' + #id", unless = "#result == null")
    @Override
    public String listNameFromCacheById(String id) {
        final MapRegion mapRegion = mapRegionMapper.selectById(id);
        if (mapRegion == null) {
            return null;
        }
        return mapRegion.getRegionName();
    }

    @Cacheable(value = "allRegion#20", key = "'ids:' + T(org.springframework.util.DigestUtils).md5DigestAsHex(#ids.toString().getBytes())", unless = "#result == null or #result.isEmpty()")
    @Override
    public List<String> listNameFromCacheByIds(List<String> ids) {
        final List<MapRegion> mapRegions = mapRegionMapper.selectByIds(ids);
        if (CollUtil.isEmpty(mapRegions)) {
            return Collections.emptyList();
        }
        return mapRegions.stream().map(MapRegion::getRegionName).toList();
    }

    @Cacheable(value = "reportRegion#20", key = "all", unless = "#result == null or #result.isEmpty()")
    @Override
    public List<MapRegion> listReportRegionFromCache(String projectId) {
        final List<MapRegion> mapRegions = mapRegionMapper.selectList(Wrappers.lambdaQuery(MapRegion.class)
                .eq(MapRegion::getRegionType, RegionTypeEnums.REPORT.getCode())
                .eq(MapRegion::getProjectId, projectId)
                .eq(MapRegion::getIsDeleted, 0));
        if (CollUtil.isEmpty(mapRegions)) {
            return Collections.emptyList();
        }
        return mapRegions;
    }
}
