package com.allin.silas.map.app.enums;

import com.allin.view.base.enums.base.IEnums;
import lombok.Getter;

/**
 * 净空区编号,1第一段,2第二段,3水平段
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
public enum ClearanceAreaTypeEnums implements IEnums {

    FIRST_SECTION(1, "第一段", 0.15, 0.02),
    SECOND_SECTION(2, "第二段", 0.15, 0.025),
    HORIZONTAL_SECTION(3, "水平段", 0.15, 0);

    private final Integer code;

    private final String desc;

    /**
     * 散开率
     */
    @Getter
    private final double spreadRate;

    /**
     * 坡度
     */
    @Getter
    private final double slope;

    ClearanceAreaTypeEnums(Integer code, String desc, double spreadRate, double slope) {
        this.code = code;
        this.desc = desc;
        this.spreadRate = spreadRate;
        this.slope = slope;
    }


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}
