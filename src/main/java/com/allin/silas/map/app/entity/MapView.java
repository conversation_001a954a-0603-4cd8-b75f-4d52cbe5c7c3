package com.allin.silas.map.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 地图视角表
 */
@Data
@TableName(value = "map_view")
public class MapView {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 视角名称,长度不超过20个字
     */
    @TableField(value = "view_name")
    private String viewName;

    /**
     * 显示设置
     */
    @TableField(value = "display_config")
    private String displayConfig;

    /**
     * 视图类型,1-系统管理
     */
    @TableField(value = "view_type")
    private Integer viewType;

    /**
     * 是否为默认视角,0-否,1-是
     */
    @TableField(value = "is_default")
    private Integer isDefault;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
}