package com.allin.silas.map.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 区域位置,1-核心区,2-场内,3-场外,4-复合区
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
public enum RegionPositionEnums implements IEnums {

    CORE(1, "核心区"),
    INSIDE(2, "场内"),
    OUTSIDE(3, "场外"),
    COMPLEX(4, "复合区");

    private final Integer code;

    private final String desc;

    RegionPositionEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
