package com.allin.silas.map.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模型配置表
 */
@Data
@TableName(value = "map_model")
public class MapModel {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 模型名称
     */
    @TableField(value = "model_name")
    private String modelName;

    /**
     * 模型文件地址
     */
    @TableField(value = "model_url")
    private String modelUrl;

    /**
     * 模型坐标,geojson.geometry
     */
    @TableField(value = "model_coord")
    private String modelCoord;

    /**
     * 高度,默认0
     */
    @TableField(value = "altitude")
    private Double altitude;

    /**
     * 缩放值,默认0
     */
    @TableField(value = "scale")
    private Double scale;

    /**
     * 绕X轴旋转角度,默认0
     */
    @TableField(value = "rotation_x")
    private Double rotationX;

    /**
     * 绕Y轴旋转角度,默认0
     */
    @TableField(value = "rotation_y")
    private Double rotationY;

    /**
     * 绕Z轴旋转角度,默认0
     */
    @TableField(value = "rotation_z")
    private Double rotationZ;

    /**
     * 是否可用,0-否,1-是
     */
    @TableField(value = "is_useable")
    private Integer isUseable;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除,0-否,1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}