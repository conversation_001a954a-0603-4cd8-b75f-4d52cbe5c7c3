package com.allin.silas.map.app.facade;

import com.allin.silas.map.app.entity.MapRegion;
import com.allin.silas.map.app.manager.MapRegionManager;
import com.allin.silas.map.client.MapRegionFacade;
import com.allin.silas.map.infra.repository.MapRegionMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.geotools.data.geojson.GeoJSONReader;
import org.geotools.geometry.Position2D;
import org.geotools.geometry.jts.JTS;
import org.geotools.referencing.crs.DefaultGeographicCRS;
import org.locationtech.jts.geom.Geometry;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@Service
@Slf4j
public class MapRegionFacadeImpl implements MapRegionFacade {

    private final MapRegionMapper mapRegionMapper;

    private final MapRegionManager mapRegionManager;

    public MapRegionFacadeImpl(MapRegionMapper mapRegionMapper, MapRegionManager mapRegionManager) {
        this.mapRegionMapper = mapRegionMapper;
        this.mapRegionManager = mapRegionManager;
    }

    @Override
    public Map<String, String> mapRegionIdByProjectId(String projectId) {
        List<MapRegion> mapRegionList = mapRegionMapper.selectList(Wrappers.lambdaQuery(MapRegion.class)
                .eq(MapRegion::getProjectId, projectId)
                .eq(MapRegion::getIsDeleted, 0));
        return CollectionUtils.isNotEmpty(mapRegionList) ?
                mapRegionList.stream()
                        .collect(Collectors.toMap(
                                MapRegion::getRegionName,
                                MapRegion::getId,
                                (existing, replacement) -> replacement)) : Collections.emptyMap();
    }

    /**
     * 计算目标所在区域
     */
    @Override
    public List<String> calculateRegion(double longitude, double latitude) {
        List<String> regionIds = new ArrayList<>();
        // 全部上报区域
        final List<MapRegion> reportRegions = mapRegionManager.listReportRegionFromCache(SecurityContextHolder.getProjectId());
        for (MapRegion reportRegion : reportRegions) {
            final Geometry geometry = GeoJSONReader.parseGeometry(reportRegion.getRegionCoord());
            final Position2D position2D = new Position2D(DefaultGeographicCRS.WGS84, longitude, latitude);
            // 判断区域是否包含该点
            if (geometry.contains(JTS.toGeometry(position2D))) {
                regionIds.add(reportRegion.getId());
            }
        }
        return regionIds;
    }
}
