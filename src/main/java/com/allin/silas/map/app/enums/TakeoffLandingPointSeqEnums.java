package com.allin.silas.map.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 起飞降落点序号,1,2
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
public enum TakeoffLandingPointSeqEnums implements IEnums {

    ONE(1, "起飞降落点1"),
    TWO(2, "起飞降落点2");

    private final Integer code;

    private final String desc;

    TakeoffLandingPointSeqEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
