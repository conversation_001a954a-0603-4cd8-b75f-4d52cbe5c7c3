package com.allin.silas.map.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 地图起飞降落点表
 */
@Data
@TableName(value = "map_takeoff_landing_point")
public class MapTakeoffLandingPoint {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 跑道id
     */
    @TableField(value = "map_runway_id")
    private String mapRunwayId;

    /**
     * 类型, takeoff, landing
     */
    @TableField(value = "point_type")
    private String pointType;

    /**
     * 起飞降落点geojson
     */
    @TableField(value = "point_coord")
    private String pointCoord;

    /**
     * 点名称,起飞点1,降落点1
     */
    @TableField(value = "point_name")
    private String pointName;

    /**
     * 起飞降落点序号,1,2
     */
    @TableField(value = "point_seq")
    private Integer pointSeq;
}