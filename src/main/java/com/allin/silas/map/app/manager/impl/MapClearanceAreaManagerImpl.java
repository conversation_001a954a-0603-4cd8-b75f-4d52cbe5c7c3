package com.allin.silas.map.app.manager.impl;

import com.allin.silas.common.util.geo.CoordinateUtils;
import com.allin.silas.map.adapter.dto.AddMapClearanceAreaDto;
import com.allin.silas.map.app.entity.MapClearanceArea;
import com.allin.silas.map.app.entity.MapRunway;
import com.allin.silas.map.app.manager.MapClearanceAreaManager;
import com.allin.silas.map.infra.repository.MapClearanceAreaMapper;
import org.geotools.data.geojson.GeoJSONWriter;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Polygon;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 净空区通用逻辑层接口实现
 *
 * <AUTHOR>
 * @since 2025/6/17
 */
@Service
public class MapClearanceAreaManagerImpl implements MapClearanceAreaManager {

    private final MapClearanceAreaMapper clearanceAreaMapper;

    public MapClearanceAreaManagerImpl(MapClearanceAreaMapper clearanceAreaMapper) {
        this.clearanceAreaMapper = clearanceAreaMapper;
    }

    @Override
    public List<MapClearanceArea> generateAreas(MapRunway mapRunway, List<AddMapClearanceAreaDto> areaConfigs) {
        // 跑道中心线
        LineString centerLine = mapRunway.centerLineGeometry();
        // 跑道区域
        Polygon runwayArea = mapRunway.areaGeometry();
        // 跑道多边形坐标
        Coordinate[] coords = runwayArea.getCoordinates();
        Coordinate sideA_1 = coords[0];
        Coordinate sideA_2 = coords[1];
        Coordinate sideB_3 = coords[2];
        Coordinate sideB_4 = coords[3];

        double azimuthAtoB = CoordinateUtils.calculateAzimuth(centerLine.getStartPoint().getCoordinate(),
                centerLine.getEndPoint().getCoordinate());
        double azimuthBtoA = (azimuthAtoB + 180) % 360;

        // 从 A 出发，沿 B→A 的方向生成净空区
        final List<MapClearanceArea> BtoAClearanceArea = doGenerateDirectionAreas(sideA_1, sideA_2, azimuthBtoA, areaConfigs);
        for (MapClearanceArea mapClearanceArea : BtoAClearanceArea) {
            mapClearanceArea.setMapRunwayId(mapRunway.getId());
            mapClearanceArea.setAreaDirection(2);
        }
        // 净空区段列表
        final List<MapClearanceArea> clearanceAreas = new ArrayList<>(BtoAClearanceArea);
        final List<MapClearanceArea> AtoBClearanceArea = doGenerateDirectionAreas(sideB_3, sideB_4, azimuthAtoB, areaConfigs);
        for (MapClearanceArea mapClearanceArea : AtoBClearanceArea) {
            mapClearanceArea.setMapRunwayId(mapRunway.getId());
            mapClearanceArea.setAreaDirection(1);
        }
        // 从 B 出发，沿 A→B 的方向生成净空区
        clearanceAreas.addAll(AtoBClearanceArea);
        clearanceAreaMapper.insert(clearanceAreas);

        return clearanceAreas;
    }

    /**
     * 生成净空区
     * <p>
     * 净空区分为三段：
     * 第一段：散开率 15%，坡度 2%
     * 第二段：散开率 15%，坡度 2.5%
     * 水平段：散开率 15%，坡度 0%
     *
     * @param startLeft   起点左坐标（跑道左边）
     * @param startRight  起点右坐标（跑道右边）
     * @param azimuth     方位角（正向净空方向）
     * @param areaConfigs 净空段配置列表（每段包含长度、散开率、坡度等信息）
     * @return 净空区段构成的列表，每段为一个顶部 Polygon 的 GeoJSON 表达
     */
    private List<MapClearanceArea> doGenerateDirectionAreas(Coordinate startLeft,
                                                            Coordinate startRight,
                                                            Double azimuth,
                                                            List<AddMapClearanceAreaDto> areaConfigs) {
        List<MapClearanceArea> list = new ArrayList<>();

        // 当前段的起始高度（底部固定为 0）
        double currentHeight = 0.0;

        // 当前段的起点左右边界
        Coordinate currentLeft = new Coordinate(startLeft.x, startLeft.y, currentHeight);
        Coordinate currentRight = new Coordinate(startRight.x, startRight.y, currentHeight);

        // 左右散开方向（±90°）
        double leftAzimuth = (azimuth - 90 + 360) % 360;
        double rightAzimuth = (azimuth + 90) % 360;

        // 按区域编号升序排序，确保顺序为：第一段 -> 第二段 -> 水平段
        List<AddMapClearanceAreaDto> sortedDtos = areaConfigs.stream()
                .sorted(Comparator.comparing(AddMapClearanceAreaDto::getAreaCode))
                .toList();

        for (AddMapClearanceAreaDto dto : sortedDtos) {
            int length = dto.getAreaLength();           // 当前段长度（米）
            double spreadRate = dto.getSpreadRate();    // 当前段侧边散开率（如 0.15）
            double slope = dto.getSlope();              // 当前段坡度（如 0.02）

            // 计算从起点到终点边界的扩展宽度（米）
            double deltaWidth = length * spreadRate;

            // 沿 azimuth 方向从当前左右起点各推进 length 米
            Coordinate roughLeft = CoordinateUtils.moveTo(currentLeft, azimuth, length);
            Coordinate roughRight = CoordinateUtils.moveTo(currentRight, azimuth, length);

            // 计算当前段终点高度（起始高度 + 长度 × 坡度）
            double topEndHeight = currentHeight + slope * length;

            // 在推进后坐标上分别向左右扩展 deltaWidth，得到终点左右坐标
            Coordinate topEndLeft = CoordinateUtils.moveTo3D(roughLeft, leftAzimuth, deltaWidth, topEndHeight);
            Coordinate topEndRight = CoordinateUtils.moveTo3D(roughRight, rightAzimuth, deltaWidth, topEndHeight);

            // 构建闭合 Polygon（顶面四点）
            Polygon polygon = JTSFactoryFinder.getGeometryFactory().createPolygon(new Coordinate[]{
                    currentLeft, currentRight, topEndRight, topEndLeft, currentLeft
            });

            MapClearanceArea area = new MapClearanceArea();
            area.setAreaCode(dto.getAreaCode());
            area.setAreaLength(length);
            area.setDisplayConfig(dto.getDisplayConfig());
            area.setAreaCoord(GeoJSONWriter.toGeoJSON(polygon));
            list.add(area);

            // 更新下一段的起点与起始高度
            currentHeight = topEndHeight;
            currentLeft = topEndLeft;
            currentRight = topEndRight;
        }

        return list;
    }

}
