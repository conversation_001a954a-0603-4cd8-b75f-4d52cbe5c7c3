package com.allin.silas.map.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 地图区域表
 */
@Data
@TableName(value = "map_region")
public class MapRegion {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 区域类型,1-上报区域,2-屏蔽区域,3-作业区域
     */
    @TableField(value = "region_type")
    private Integer regionType;

    /**
     * 区域名称,长度不超过10字
     */
    @TableField(value = "region_name")
    private String regionName;

    /**
     * 区域geojson
     */
    @TableField(value = "region_coord")
    private String regionCoord;

    /**
     * 区域位置,1-核心区,2-场内,3-场外,4-复合区
     */
    @TableField(value = "region_position")
    private Integer regionPosition;

    /**
     * 显示配置
     */
    @TableField(value = "display_config")
    private String displayConfig;

    /**
     * 备注信息
     */
    @TableField(value = "remarks")
    private String remarks;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除,0-否,1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}