package com.allin.silas.map.app.manager.impl;

import com.allin.silas.common.util.geo.CoordinateUtils;
import com.allin.silas.map.adapter.dto.AddMapRunwayDto;
import com.allin.silas.map.adapter.dto.EditMapRunwayDto;
import com.allin.silas.map.adapter.dto.EditMapRunwayFromDto;
import com.allin.silas.map.app.entity.MapRunway;
import com.allin.silas.map.app.manager.MapRunwayManager;
import com.allin.silas.map.infra.repository.MapRunwayMapper;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.geotools.data.geojson.GeoJSONWriter;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 跑道通用逻辑层接口实现
 *
 * <AUTHOR>
 * @since 2025/6/17
 */
@Slf4j
@Service
public class MapRunwayManagerImpl implements MapRunwayManager {

    private final MapRunwayMapper runwayMapper;

    public MapRunwayManagerImpl(MapRunwayMapper runwayMapper) {
        this.runwayMapper = runwayMapper;
    }

    /**
     * 根据跑道中心线的两点计算跑道中心点
     *
     * @param centerLine 中心线geometry
     * @return 跑道中心点
     */
    private String calculateCenterCoord(LineString centerLine) {
        if (centerLine == null || centerLine.isEmpty()) {
            throw new ValidationFailureException(I18nUtil.isEmpty("centerLine"));
        }
        // 计算中心点
        Point centroid = centerLine.getCentroid();
        return GeoJSONWriter.toGeoJSON(centroid);
    }

    /**
     * 根据跑道中心线和跑道宽度左右外扩，计算跑道区域
     *
     * @param centerLine  中心线geometry
     * @param runwayWidth 跑道宽度，单位米
     * @return 跑道区域
     */
    private String calculateAreaCoord(LineString centerLine, Integer runwayWidth) {
        if (centerLine == null || centerLine.isEmpty()) {
            throw new ValidationFailureException(I18nUtil.isEmpty("centerLine"));
        }
        if (runwayWidth == null) {
            throw new ValidationFailureException(I18nUtil.isEmpty("runwayWidth"));
        }

        Coordinate p1 = centerLine.getStartPoint().getCoordinate();
        Coordinate p2 = centerLine.getEndPoint().getCoordinate();

        // 使用地理坐标计算偏移点
        double halfWidth = (double) runwayWidth / 2;

        // 计算方向角（正北起顺时针）
        double azimuth = CoordinateUtils.calculateAzimuth(p1, p2);

        // 计算四个角点, C1为多边形的左下点，顺时针连线
        Coordinate c1 = CoordinateUtils.moveTo(p1, azimuth + 90, halfWidth);
        Coordinate c2 = CoordinateUtils.moveTo(p1, azimuth - 90, halfWidth);
        Coordinate c3 = CoordinateUtils.moveTo(p2, azimuth - 90, halfWidth);
        Coordinate c4 = CoordinateUtils.moveTo(p2, azimuth + 90, halfWidth);

        // 封闭
        Coordinate[] coords = new Coordinate[]{c1, c2, c3, c4, c1};
        Polygon polygon = JTSFactoryFinder.getGeometryFactory().createPolygon(coords);
        return GeoJSONWriter.toGeoJSON(polygon);
    }

    @Override
    public MapRunway addRunway(AddMapRunwayDto addDto) {
        MapRunway mapRunway = addDto.toEntity();
        // 计算中心点
        mapRunway.setCenterCoord(calculateCenterCoord(addDto.centerLineGeometry()));
        // 计算跑道区域
        mapRunway.setAreaCoord(calculateAreaCoord(addDto.centerLineGeometry(), addDto.getRunwayWidth()));
        if (runwayMapper.insert(mapRunway) > 0) {
            return mapRunway;
        }
        return null;
    }

    @Override
    public MapRunway editRunway(EditMapRunwayFromDto formDto) {
        final EditMapRunwayDto runway = formDto.getRunway();
        MapRunway mapRunway = runway.toEntity(runway.getId());
        // 计算中心点
        mapRunway.setCenterCoord(calculateCenterCoord(runway.centerLineGeometry()));
        // 计算跑道区域
        mapRunway.setAreaCoord(calculateAreaCoord(runway.centerLineGeometry(), runway.getRunwayWidth()));
        if (runwayMapper.updateById(mapRunway) > 0) {
            return mapRunway;
        }
        return null;
    }

    @Cacheable(cacheNames = "mapRunway#60", key = "#runWayId", unless = "#result == null")
    @Override
    public MapRunway getRunwayFromCacheById(String runWayId) {
        return runwayMapper.selectById(runWayId);
    }
}
