package com.allin.silas.map.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 地图类型
 * 区域类型,1-上报区域,2-屏蔽区域,3-作业区域,4-复合区域
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
public enum RegionTypeEnums implements IEnums {

    REPORT(1, "上报区域"),
    SHIELD(2, "屏蔽区域"),
    WORK(3, "作业区域")
    ;

    private final Integer code;

    private final String desc;

    RegionTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
