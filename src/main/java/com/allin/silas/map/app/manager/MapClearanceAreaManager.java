package com.allin.silas.map.app.manager;

import com.allin.silas.map.adapter.dto.AddMapClearanceAreaDto;
import com.allin.silas.map.app.entity.MapClearanceArea;
import com.allin.silas.map.app.entity.MapRunway;

import java.util.List;

/**
 * 跑道净空区
 *
 * <AUTHOR>
 * @since 2025/6/17
 */
public interface MapClearanceAreaManager {

    /**
     * 生成跑道净空区
     *
     * @param mapRunway 跑道信息
     * @param areaConfigs  净空区配置信息
     */
    List<MapClearanceArea> generateAreas(MapRunway mapRunway, List<AddMapClearanceAreaDto> areaConfigs);
}
