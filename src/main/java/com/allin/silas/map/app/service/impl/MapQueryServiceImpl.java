package com.allin.silas.map.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.allin.silas.map.adapter.vo.*;
import com.allin.silas.map.app.entity.*;
import com.allin.silas.map.app.service.MapQueryService;
import com.allin.silas.map.infra.repository.*;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 地图查询服务接口实现类
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@Service
public class MapQueryServiceImpl implements MapQueryService {

    private final MapBaseMapper mapBaseMapper;

    private final MapModelMapper mapModelMapper;

    private final MapViewMapper mapViewMapper;

    private final MapRegionMapper mapRegionMapper;

    private final MapBirdNetMapper mapBirdNetMapper;

    private final MapRunwayMapper mapRunwayMapper;

    private final MapClearanceAreaMapper mapClearanceAreaMapper;

    private final MapTakeoffLandingPointMapper mapTakeoffLandingPointMapper;

    public MapQueryServiceImpl(MapBaseMapper mapBaseMapper,
                               MapModelMapper mapModelMapper,
                               MapViewMapper mapViewMapper,
                               MapRegionMapper mapRegionMapper,
                               MapBirdNetMapper mapBirdNetMapper,
                               MapRunwayMapper mapRunwayMapper,
                               MapClearanceAreaMapper mapClearanceAreaMapper, MapTakeoffLandingPointMapper mapTakeoffLandingPointMapper) {
        this.mapBaseMapper = mapBaseMapper;
        this.mapModelMapper = mapModelMapper;
        this.mapViewMapper = mapViewMapper;
        this.mapRegionMapper = mapRegionMapper;
        this.mapBirdNetMapper = mapBirdNetMapper;
        this.mapRunwayMapper = mapRunwayMapper;
        this.mapClearanceAreaMapper = mapClearanceAreaMapper;
        this.mapTakeoffLandingPointMapper = mapTakeoffLandingPointMapper;
    }

    @Override
    public MapBase isExistMap(Integer mapType, String projectId, String mapId) {
        return mapBaseMapper.selectOne(Wrappers.lambdaQuery(MapBase.class)
                .eq(MapBase::getMapType, mapType)
                .eq(MapBase::getProjectId, projectId)
                .ne(mapId != null, MapBase::getId, mapId));
    }

    @Override
    public List<MapBaseVo> listMap(String projectId) {
        final List<MapBase> mapBases = mapBaseMapper.selectList(
                Wrappers.lambdaQuery(MapBase.class)
                        .eq(MapBase::getProjectId, projectId));
        if (CollUtil.isNotEmpty(mapBases)) {
            return mapBases.stream().map(MapBaseVo::of).toList();
        }
        return Collections.emptyList();
    }

    @Override
    public List<MapModelVo> listModel(String projectId) {
        final List<MapModel> mapModels = mapModelMapper.selectList(
                Wrappers.lambdaQuery(MapModel.class)
                        .eq(MapModel::getProjectId, projectId));
        if (CollUtil.isNotEmpty(mapModels)) {
            return mapModels.stream().map(MapModelVo::of).toList();
        }
        return Collections.emptyList();
    }

    @Override
    public List<MapViewVo> listView(String projectId) {
        final List<MapView> mapModels = mapViewMapper.selectList(
                Wrappers.lambdaQuery(MapView.class)
                        .eq(MapView::getProjectId, projectId));
        if (CollUtil.isNotEmpty(mapModels)) {
            return mapModels.stream().map(MapViewVo::of).toList();
        }
        return Collections.emptyList();
    }

    @Override
    public List<MapRegionVo> listRegion(String projectId, Integer regionType) {
        final List<MapRegion> mapRegions = mapRegionMapper.selectList(
                Wrappers.lambdaQuery(MapRegion.class)
                        .eq(MapRegion::getProjectId, projectId)
                        .eq(regionType != null, MapRegion::getRegionType, regionType));
        if (CollUtil.isNotEmpty(mapRegions)) {
            return mapRegions.stream().map(MapRegionVo::of).toList();
        }
        return Collections.emptyList();
    }

    @Override
    public List<MapBirdNetVo> listBirdNet(String projectId) {
        final List<MapBirdNet> mapBirdNets = mapBirdNetMapper.selectList(
                Wrappers.lambdaQuery(MapBirdNet.class)
                        .eq(MapBirdNet::getProjectId, projectId));
        if (CollUtil.isNotEmpty(mapBirdNets)) {
            return mapBirdNets.stream().map(MapBirdNetVo::of).toList();
        }
        return Collections.emptyList();
    }

    @Override
    public List<MapRunwayVo> listRunWay(String projectId) {
        final List<MapRunway> mapRunways = mapRunwayMapper.selectList(
                Wrappers.lambdaQuery(MapRunway.class)
                        .eq(MapRunway::getProjectId, projectId));
        if (CollUtil.isEmpty(mapRunways)) {
            return Collections.emptyList();
        }
        // 构建 VO 并设置关联数据
        return mapRunways.stream()
                .map(MapRunwayVo::of)
                .toList();
    }

    @Override
    public List<MapClearanceAreaVo> listClearanceArea(String projectId) {
        final List<MapClearanceArea> mapClearanceAreas = mapClearanceAreaMapper.selectList(
                Wrappers.lambdaQuery(MapClearanceArea.class)
                        .eq(MapClearanceArea::getProjectId, projectId));
        if (CollUtil.isEmpty(mapClearanceAreas)) {
            return Collections.emptyList();
        }
        return mapClearanceAreas.stream()
                .map(MapClearanceAreaVo::of)
                .toList();
    }

    @Override
    public List<MapTakeoffLandingPointVo> listTakeoffLanding(String projectId) {
        final var mapClearanceAreas = mapTakeoffLandingPointMapper.selectList(
                Wrappers.lambdaQuery(MapTakeoffLandingPoint.class)
                        .eq(MapTakeoffLandingPoint::getProjectId, projectId));
        if (CollUtil.isEmpty(mapClearanceAreas)) {
            return Collections.emptyList();
        }
        return mapClearanceAreas.stream()
                .map(MapTakeoffLandingPointVo::of)
                .toList();
    }

    @Override
    public MapBaseVo getMapInfo(String mapId) {
        return MapBaseVo.of(mapBaseMapper.selectById(mapId));
    }

    @Override
    public MapModelVo getModelInfo(String modelId) {
        return MapModelVo.of(mapModelMapper.selectById(modelId));
    }

    @Override
    public MapRegionVo getMapRegion(String regionId) {
        return MapRegionVo.of(mapRegionMapper.selectById(regionId));
    }

    @Override
    public MapBirdNetVo getMapBirdNet(String birdNetId) {
        return MapBirdNetVo.of(mapBirdNetMapper.selectById(birdNetId));
    }

    @Override
    public MapRunwayAggVo getRunWayAgg(String runwayId) {
        final MapRunway mapRunway = mapRunwayMapper.selectById(runwayId);
        if (mapRunway == null) {
            throw new ValidationFailureException(I18nUtil.isNotExist());
        }

        // 查询 clearance area
        final List<MapClearanceArea> clearanceAreas = mapClearanceAreaMapper.selectList(
                Wrappers.lambdaQuery(MapClearanceArea.class)
                        .eq(MapClearanceArea::getMapRunwayId, runwayId)
        );

        // 查询起飞降落点
        final List<MapTakeoffLandingPoint> takeoffLandingPoints = mapTakeoffLandingPointMapper.selectList(
                Wrappers.lambdaQuery(MapTakeoffLandingPoint.class)
                        .eq(MapTakeoffLandingPoint::getMapRunwayId, runwayId)
        );

        final MapRunwayAggVo mapRunwayAggVo = new MapRunwayAggVo();
        mapRunwayAggVo.setRunway(MapRunwayVo.of(mapRunway));
        mapRunwayAggVo.setClearanceAreas(clearanceAreas.stream().map(MapClearanceAreaVo::of).toList());
        mapRunwayAggVo.setTakeoffLandingPoints(takeoffLandingPoints.stream().map(MapTakeoffLandingPointVo::of).toList());

        return mapRunwayAggVo;
    }
}
