package com.allin.silas.map.app.service.impl;

import com.allin.silas.map.adapter.dto.AddMapViewDto;
import com.allin.silas.map.app.entity.MapView;
import com.allin.silas.map.app.service.MapViewCommandService;
import com.allin.silas.map.infra.repository.MapViewMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;

/**
 * 地图视图服务接口实现
 *
 * <AUTHOR>
 * @since 2025/6/9
 */
@Service
public class MapViewCommandServiceImpl implements MapViewCommandService {

    private final MapViewMapper mapViewMapper;

    public MapViewCommandServiceImpl(MapViewMapper mapViewMapper) {
        this.mapViewMapper = mapViewMapper;
    }

    @Override
    public boolean del(String id) {
        return mapViewMapper.deleteById(id) > 0;
    }

    @Override
    public boolean add(AddMapViewDto addMapViewDto) {
        // 设置为默认时需要将其他地图全部设置为非默认
        if (addMapViewDto.getIsDefault() == 1) {
            mapViewMapper.update(Wrappers.lambdaUpdate(MapView.class)
                    .eq(MapView::getProjectId, SecurityContextHolder.getProjectId())
                    .set(MapView::getIsDefault, 0));
        }
        return mapViewMapper.insert(addMapViewDto.toEntity()) > 0;
    }

    @Override
    public boolean setDefault(String id) {
        mapViewMapper.update(Wrappers.lambdaUpdate(MapView.class)
                .eq(MapView::getProjectId, SecurityContextHolder.getProjectId())
                .set(MapView::getIsDefault, 0));
        return mapViewMapper.update(Wrappers.lambdaUpdate(MapView.class)
                .eq(MapView::getId, id)
                .set(MapView::getIsDefault, 1)) > 0;
    }
}
