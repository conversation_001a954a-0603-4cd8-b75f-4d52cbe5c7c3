package com.allin.silas.map.app.service.impl;

import com.allin.silas.map.adapter.dto.AddMapRegionDto;
import com.allin.silas.map.adapter.dto.EditMapRegionDto;
import com.allin.silas.map.app.entity.MapRegion;
import com.allin.silas.map.app.service.MapRegionCommandService;
import com.allin.silas.map.infra.repository.MapRegionMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;

/**
 * 地图区域服务实现类
 *
 * <AUTHOR>
 * @since 2025/6/13
 */
@Service
public class MapRegionCommandServiceImpl implements MapRegionCommandService {

    private final MapRegionMapper mapRegionMapper;

    public MapRegionCommandServiceImpl(MapRegionMapper mapRegionMapper) {
        this.mapRegionMapper = mapRegionMapper;
    }

    @Override
    public boolean del(String id) {
        return mapRegionMapper.deleteById(id) > 0;
    }

    @Override
    public boolean add(AddMapRegionDto addMapRegionDto) {
        // 校验区域名称不能重复
        if (mapRegionMapper.selectCount(Wrappers.lambdaQuery(MapRegion.class)
                .eq(MapRegion::getProjectId, SecurityContextHolder.getProjectId())
                .eq(MapRegion::getRegionName, addMapRegionDto.getRegionName())) > 0) {
            throw new ValidationFailureException(I18nUtil.isExist("regionName"));
        }
        return mapRegionMapper.insert(addMapRegionDto.toEntity()) > 0;
    }

    @Override
    public boolean edit(EditMapRegionDto editMapRegionDto) {
        // 校验区域名称不能重复
        if (mapRegionMapper.selectCount(Wrappers.lambdaQuery(MapRegion.class)
                .ne(MapRegion::getId, editMapRegionDto.getId())
                .eq(MapRegion::getProjectId, SecurityContextHolder.getProjectId())
                .eq(MapRegion::getRegionName, editMapRegionDto.getRegionName())) > 0) {
            throw new ValidationFailureException(I18nUtil.isExist("regionName"));
        }
        return mapRegionMapper.updateById(editMapRegionDto.toEntity()) > 0;
    }
}
