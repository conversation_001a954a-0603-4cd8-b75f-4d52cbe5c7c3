package com.allin.silas.map.app.service.impl;

import com.allin.silas.map.adapter.dto.AddMapModelDto;
import com.allin.silas.map.adapter.dto.EditMapModelDto;
import com.allin.silas.map.app.service.MapModelCommandService;
import com.allin.silas.map.infra.repository.MapModelMapper;
import org.springframework.stereotype.Service;

/**
 * 地图模型服务实现类
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@Service
public class MapModelCommandServiceImpl implements MapModelCommandService {

    private final MapModelMapper mapModelMapper;

    public MapModelCommandServiceImpl(MapModelMapper mapModelMapper) {
        this.mapModelMapper = mapModelMapper;
    }

    @Override
    public boolean add(AddMapModelDto addMapModelDto) {
        return mapModelMapper.insert(addMapModelDto.toEntity()) > 0;
    }

    @Override
    public boolean edit(EditMapModelDto editMapModelDto) {
        return mapModelMapper.updateById(editMapModelDto.toEntity()) > 0;
    }

    @Override
    public boolean del(String id) {
        return mapModelMapper.deleteById(id) > 0;
    }
}
