package com.allin.silas.map.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 地图坐标类型
 * WGS84,GCJ02,BD09
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
public enum MapCoordEnums implements IEnums {

    WGS84("WGS84", "WGS84"),
    GCJ02("GCJ02", "GCJ02"),
    BD09("BD09", "BD09");

    private final String code;

    private final String desc;

    MapCoordEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
