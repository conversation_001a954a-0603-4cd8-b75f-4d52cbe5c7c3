package com.allin.silas.map.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 地图类型
 * 1-卫星,2-街道,3-轮廓,4-地形
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
public enum MapTypeEnums implements IEnums {

    SATELLITE(1, "卫星"),
    STREET(2, "街道"),
    OUTLINE(3, "轮廓"),
    TERRAIN(4, "地形");

    private final Integer code;

    private final String desc;

    MapTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
