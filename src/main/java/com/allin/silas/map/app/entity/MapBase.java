package com.allin.silas.map.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 地图基础信息表
 */
@Data
@TableName(value = "map_base")
public class MapBase {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 地图类型,1-卫星,2-街道,3-轮廓,4-地形
     */
    @TableField(value = "map_type")
    private Integer mapType;

    /**
     * 坐标类型,WGS84,GCJ02,BD09
     */
    @TableField(value = "coord_type")
    private String coordType;

    /**
     * 地图地址
     */
    @TableField(value = "map_url")
    private String mapUrl;

    /**
     * 中心点geojson
     */
    @TableField(value = "center_coord")
    private String centerCoord;

    /**
     * 是否可用,0-否,1-是
     */
    @TableField(value = "is_useable")
    private Integer isUseable;

    /**
     * 是否为默认地图,0-否,1-是
     */
    @TableField(value = "is_default")
    private Integer isDefault;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除,0-否,1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}