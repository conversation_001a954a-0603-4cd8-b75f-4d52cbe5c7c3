package com.allin.silas.map.app.service.impl;

import com.allin.silas.map.adapter.dto.AddMapBaseDto;
import com.allin.silas.map.adapter.dto.EditMapBaseDto;
import com.allin.silas.map.app.entity.MapBase;
import com.allin.silas.map.app.service.MapCommandService;
import com.allin.silas.map.infra.repository.MapBaseMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2025/5/29
 */
@Service
public class MapCommandServiceImpl implements MapCommandService {

    private final MapBaseMapper mapBaseMapper;

    public MapCommandServiceImpl(MapBaseMapper mapBaseMapper) {
        this.mapBaseMapper = mapBaseMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean add(AddMapBaseDto formDto) {
        // 设置为默认时需要将其他地图全部设置为非默认
        if (formDto.getIsDefault() == 1) {
            mapBaseMapper.update(Wrappers.lambdaUpdate(MapBase.class)
                    .eq(MapBase::getProjectId, SecurityContextHolder.getProjectId())
                    .set(MapBase::getIsDefault, 0));
        }
        return mapBaseMapper.insert(formDto.toEntity()) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean edit(EditMapBaseDto formDto) {
        // 设置为默认时需要将其他地图全部设置为非默认
        if (formDto.getIsDefault() == 1) {
            mapBaseMapper.update(Wrappers.lambdaUpdate(MapBase.class)
                    .eq(MapBase::getProjectId, SecurityContextHolder.getProjectId())
                    .set(MapBase::getIsDefault, 0));
        }
        return mapBaseMapper.updateById(formDto.toEntity()) > 0;
    }

    @Override
    public boolean del(String id) {
        return mapBaseMapper.deleteById(id) > 0;
    }
}
