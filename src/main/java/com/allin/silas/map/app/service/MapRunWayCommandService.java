package com.allin.silas.map.app.service;

import com.allin.silas.map.adapter.dto.AddMapRunwayFormDto;
import com.allin.silas.map.adapter.dto.EditMapRunwayFromDto;

/**
 * 地图跑道接口
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
public interface MapRunWayCommandService {

    /**
     * 删除跑道
     */
    boolean del(String id);

    /**
     * 新增跑道
     */
    boolean add(AddMapRunwayFormDto formDto);

    /**
     * 修改跑道
     */
    boolean edit(EditMapRunwayFromDto fromDto);
}
