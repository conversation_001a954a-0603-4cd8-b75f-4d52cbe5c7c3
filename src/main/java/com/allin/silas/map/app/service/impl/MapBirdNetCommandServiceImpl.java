package com.allin.silas.map.app.service.impl;

import com.allin.silas.map.adapter.dto.AddMapBirdNetDto;
import com.allin.silas.map.adapter.dto.EditMapBirdNetDto;
import com.allin.silas.map.app.service.MapBirdNetCommandService;
import com.allin.silas.map.infra.repository.MapBirdNetMapper;
import org.springframework.stereotype.Service;

/**
 * 地图捕鸟网服务接口实现
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
@Service
public class MapBirdNetCommandServiceImpl implements MapBirdNetCommandService {

    private final MapBirdNetMapper mapBirdNetMapper;

    public MapBirdNetCommandServiceImpl(MapBirdNetMapper mapBirdNetMapper) {
        this.mapBirdNetMapper = mapBirdNetMapper;
    }

    @Override
    public boolean del(String id) {
        return mapBirdNetMapper.deleteById(id) > 0;
    }

    @Override
    public boolean add(AddMapBirdNetDto addMapBirdNetDto) {
        return mapBirdNetMapper.insert(addMapBirdNetDto.toEntity()) > 0;
    }

    @Override
    public boolean edit(EditMapBirdNetDto editMapBirdNetDto) {
        return mapBirdNetMapper.updateById(editMapBirdNetDto.toEntity()) > 0;
    }
}
