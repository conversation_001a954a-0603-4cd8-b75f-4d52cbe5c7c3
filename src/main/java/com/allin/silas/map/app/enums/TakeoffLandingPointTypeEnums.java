package com.allin.silas.map.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 类型, takeoff, landing
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
public enum TakeoffLandingPointTypeEnums implements IEnums {

    TAKE_OFF("takeoff", "起飞"),
    LANDING("landing", "降落");

    private final String code;

    private final String desc;

    TakeoffLandingPointTypeEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
