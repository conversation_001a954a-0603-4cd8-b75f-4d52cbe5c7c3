package com.allin.silas.map.app.service;

import com.allin.silas.map.adapter.vo.*;
import com.allin.silas.map.app.entity.MapBase;
import jakarta.annotation.Nullable;

import java.util.List;

/**
 * 地图查询服务接口
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
public interface MapQueryService {

    /**
     * 判断地图是否存在
     *
     * @param mapId 地图id, 允许为空, 不为空时表示排除自身
     */
    @Nullable
    MapBase isExistMap(Integer mapType, String projectId, @Nullable String mapId);

    /**
     * 查询地图列表
     */
    List<MapBaseVo> listMap(String projectId);

    /**
     * 查询地图模型列表
     */
    List<MapModelVo> listModel(String projectId);

    /**
     * 查询地图模型列表
     */
    List<MapViewVo> listView(String projectId);

    /**
     * 查询地图区域列表
     */
    List<MapRegionVo> listRegion(String projectId, Integer regionType);

    /**
     * 查询地图捕鸟网列表
     */
    List<MapBirdNetVo> listBirdNet(String projectId);

    /**
     * 查询跑道列表
     */
    List<MapRunwayVo> listRunWay(String projectId);

    /**
     * 查看净空区列表
     */
    List<MapClearanceAreaVo> listClearanceArea(String projectId);

    /**
     * 查看起飞降落点列表
     */
    List<MapTakeoffLandingPointVo> listTakeoffLanding(String projectId);

    /**
     * 查询地图基础信息
     */
    MapBaseVo getMapInfo(String mapId);

    /**
     * 查询地图基础信息
     */
    MapModelVo getModelInfo(String modelId);

    /**
     * 查询地图区域信息
     */
    MapRegionVo getMapRegion(String regionId);

    /**
     * 查询地图捕鸟网信息
     */
    MapBirdNetVo getMapBirdNet(String birdNetId);

    /**
     * 查询跑道聚合详情
     * 包含跑道信息、净空区信息、起降点信息
     */
    MapRunwayAggVo getRunWayAgg(String runwayId);
}
