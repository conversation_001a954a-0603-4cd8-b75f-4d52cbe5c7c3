package com.allin.silas.map.app.manager.impl;

import com.allin.silas.map.app.entity.MapBirdNet;
import com.allin.silas.map.app.manager.MapBirdNetManager;
import com.allin.silas.map.infra.repository.MapBirdNetMapper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
@Service
public class MapBirdNetManagerImpl implements MapBirdNetManager {

    private final MapBirdNetMapper mapBirdNetMapper;

    public MapBirdNetManagerImpl(MapBirdNetMapper mapBirdNetMapper) {
        this.mapBirdNetMapper = mapBirdNetMapper;
    }

    @Cacheable(value = "mapBirdNet#20", key = "'id:' + #id", unless = "#result == null")
    @Override
    public String listNameFromCacheById(String id) {
        final MapBirdNet mapBirdNet = mapBirdNetMapper.selectById(id);
        return Objects.nonNull(mapBirdNet) ? mapBirdNet.getBirdNetName() : null;
    }
}
