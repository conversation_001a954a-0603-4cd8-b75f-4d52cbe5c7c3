package com.allin.silas.map.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 地图捕鸟网表
 */
@Data
@TableName(value = "map_bird_net")
public class MapBirdNet {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;
    /**
     * 捕鸟网名称,长度不超过10字
     */
    @TableField(value = "bird_net_name")
    private String birdNetName;

    /**
     * 捕鸟网geojson
     */
    @TableField(value = "bird_net_coord")
    private String birdNetCoord;

    /**
     * 捕鸟网位置,1-核心区,2-场内,3-场外,4-复合区
     */
    @TableField(value = "bird_net_position")
    private Integer birdNetPosition;

    /**
     * 跑道id
     */
    @TableField(value = "map_runway_Id")
    private String mapRunwayId;

    /**
     * 显示配置
     */
    @TableField(value = "display_config")
    private String displayConfig;

    /**
     * 备注信息
     */
    @TableField(value = "remarks")
    private String remarks;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除,0-否,1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}