package com.allin.silas.map.adapter.controller;

import com.allin.silas.common.util.geo.GeoJsonUtils;
import com.allin.silas.map.adapter.dto.AddMapRegionDto;
import com.allin.silas.map.adapter.dto.EditMapRegionDto;
import com.allin.silas.map.adapter.vo.MapRegionVo;
import com.allin.silas.map.app.service.MapQueryService;
import com.allin.silas.map.app.service.MapRegionCommandService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import com.allin.view.log.annotation.Log;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地图区域管理
 *
 * <AUTHOR>
 * @since 2025/6/9
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/map/region")
class MapRegionController {

    private final MapQueryService mapQueryService;

    private final MapRegionCommandService mapRegionCommandService;

    MapRegionController(MapQueryService mapQueryService,
                        MapRegionCommandService mapRegionCommandService) {
        this.mapQueryService = mapQueryService;
        this.mapRegionCommandService = mapRegionCommandService;
    }

    /**
     * 查看区域列表
     * @param regionType 区域类型
     */
    @GetMapping("/list")
    public Result<List<MapRegionVo>> list(@RequestParam(required = false) Integer regionType) {
        return Result.ok(mapQueryService.listRegion(SecurityContextHolder.getProjectId(), regionType));
    }

    /**
     * 查看区域详情
     */
    @GetMapping("/{regionId}")
    public Result<MapRegionVo> info(@PathVariable("regionId") String regionId) {
        return Result.ok(mapQueryService.getMapRegion(regionId));
    }

    /**
     * 新增区域
     */
    @Log(title = "地图区域管理", operDesc = "新增区域")
    @PostMapping
    public Result<Void> add(@RequestBody @Validated AddMapRegionDto addMapRegionDto) {
        GeoJsonUtils.checkGeoJsonGeometry(addMapRegionDto.getRegionCoord());
        return mapRegionCommandService.add(addMapRegionDto) ? Result.ok() : Result.fail();
    }

    /**
     * 修改区域
     */
    @Log(title = "地图区域管理", operDesc = "修改区域")
    @PutMapping
    public Result<Void> edit(@RequestBody @Validated EditMapRegionDto editMapRegionDto) {
        GeoJsonUtils.checkGeoJsonGeometry(editMapRegionDto.getRegionCoord());
        return mapRegionCommandService.edit(editMapRegionDto) ? Result.ok() : Result.fail();
    }

    /**
     * 删除区域
     */
    @Log(title = "地图区域管理", operDesc = "删除区域")
    @DeleteMapping("/{id}")
    public Result<String> del(@PathVariable("id") String id) {
        return mapRegionCommandService.del(id) ? Result.ok() : Result.fail();
    }

}
