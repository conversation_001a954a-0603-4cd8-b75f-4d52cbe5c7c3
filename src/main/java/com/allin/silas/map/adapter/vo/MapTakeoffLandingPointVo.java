package com.allin.silas.map.adapter.vo;

import com.allin.silas.map.app.entity.MapTakeoffLandingPoint;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 地图起飞降落点表
 */
@Data
public class MapTakeoffLandingPointVo {
    /**
     * 主键id
     */
    private String id;

    /**
     * 跑道id
     */
    private String mapRunwayId;

    /**
     * 类型, takeoff, landing
     */
    private String pointType;

    /**
     * 起飞降落点geojson
     */
    private String pointCoord;

    /**
     * 点名称,起飞点1,降落点1
     */
    private String pointName;

    /**
     * 起飞降落点序号,1,2
     */
    private Integer pointSeq;

    public static MapTakeoffLandingPointVo of(MapTakeoffLandingPoint mapTakeoffLandingPoint) {
        final MapTakeoffLandingPointVo vo = new MapTakeoffLandingPointVo();
        BeanUtils.copyProperties(mapTakeoffLandingPoint, vo);
        return vo;
    }
}