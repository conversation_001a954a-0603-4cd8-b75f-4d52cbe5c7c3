package com.allin.silas.map.adapter.dto;

import com.allin.silas.map.app.entity.MapView;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;

/**
 * 地图视角表
 */
@Data
public class AddMapViewDto {

    /**
     * 视角名称,长度不超过20个字
     */
    @NotBlank
    @Length(max = 20)
    private String viewName;

    /**
     * 显示设置
     */
    @NotBlank
    private String displayConfig;

    /**
     * 视图类型,1-系统管理
     */
    @NotNull
    @Range(min = 1, max = 1)
    private Integer viewType;

    /**
     * 是否为默认视角,0-否,1-是
     */
    @NotNull
    @Range(min = 0, max = 1)
    private Integer isDefault;

    public MapView toEntity() {
        final MapView mapView = new MapView();
        BeanUtils.copyProperties(this, mapView);
        return mapView;
    }
}