package com.allin.silas.map.adapter.controller;

import com.allin.silas.map.adapter.dto.AddMapRunwayDto;
import com.allin.silas.map.adapter.dto.AddMapRunwayFormDto;
import com.allin.silas.map.adapter.dto.EditMapRunwayDto;
import com.allin.silas.map.adapter.dto.EditMapRunwayFromDto;
import com.allin.silas.map.adapter.vo.MapClearanceAreaVo;
import com.allin.silas.map.adapter.vo.MapRunwayAggVo;
import com.allin.silas.map.adapter.vo.MapRunwayVo;
import com.allin.silas.map.adapter.vo.MapTakeoffLandingPointVo;
import com.allin.silas.map.app.entity.MapRunway;
import com.allin.silas.map.app.service.MapQueryService;
import com.allin.silas.map.app.service.MapRunWayCommandService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import com.allin.view.base.i18n.I18nUtil;
import com.allin.view.log.annotation.Log;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地图跑道管理
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/map/runway")
class MapRunwayController {

    private final MapQueryService mapQueryService;

    private final MapRunWayCommandService mapRunWayCommandService;

    MapRunwayController(MapQueryService mapQueryService,
                        MapRunWayCommandService mapRunWayCommandService) {
        this.mapQueryService = mapQueryService;
        this.mapRunWayCommandService = mapRunWayCommandService;
    }

    /**
     * 查看跑道列表
     */
    @GetMapping("/list")
    public Result<List<MapRunwayVo>> list() {
        return Result.ok(mapQueryService.listRunWay(SecurityContextHolder.getProjectId()));
    }

    /**
     * 查看净空区列表
     */
    @GetMapping("/clearance_area/list")
    public Result<List<MapClearanceAreaVo>> listClearanceArea() {
        return Result.ok(mapQueryService.listClearanceArea(SecurityContextHolder.getProjectId()));
    }

    /**
     * 查看起飞降落点列表
     */
    @GetMapping("/takeoff_landing/list")
    public Result<List<MapTakeoffLandingPointVo>> listTakeoffLanding() {
        return Result.ok(mapQueryService.listTakeoffLanding(SecurityContextHolder.getProjectId()));
    }

    /**
     * 查看跑道聚合详情
     * <p>
     * 包含跑道、净空区、跑道起降点信息
     */
    @GetMapping("/agg/info/{runwayId}")
    public Result<MapRunwayAggVo> aggInfo(@PathVariable("runwayId") String runwayId) {
        return Result.ok(mapQueryService.getRunWayAgg(runwayId));
    }

    /**
     * 新增跑道
     */
    @Log(title = "地图跑道管理", operDesc = "新增跑道")
    @PostMapping
    public Result<Void> add(@RequestBody @Validated AddMapRunwayFormDto formDto) {
        formDto.validate();
        final AddMapRunwayDto runway = formDto.getRunway();
        final long count = Db.count(Wrappers.lambdaQuery(MapRunway.class)
                .eq(MapRunway::getProjectId, SecurityContextHolder.getProjectId())
                .eq(MapRunway::getRunwayName, runway.getRunwayName()));
        if (count > 1) {
            return Result.fail(I18nUtil.isExist("runwayName"));
        }
        return mapRunWayCommandService.add(formDto) ? Result.ok() : Result.fail();
    }

    /**
     * 修改跑道
     */
    @Log(title = "地图跑道管理", operDesc = "修改跑道")
    @PutMapping
    public Result<Void> add(@RequestBody @Validated EditMapRunwayFromDto fromDto) {
        fromDto.validate();
        final EditMapRunwayDto runway = fromDto.getRunway();
        final long count = Db.count(Wrappers.lambdaQuery(MapRunway.class)
                .eq(MapRunway::getProjectId, SecurityContextHolder.getProjectId())
                .eq(MapRunway::getRunwayName, runway.getRunwayName())
                .ne(MapRunway::getId, runway.getId()));
        if (count > 1) {
            return Result.fail(I18nUtil.isExist("runwayName"));
        }
        return mapRunWayCommandService.edit(fromDto) ? Result.ok() : Result.fail();
    }

    /**
     * 删除跑道
     */
    @Log(title = "地图跑道管理", operDesc = "删除跑道")
    @DeleteMapping("/{id}")
    public Result<String> del(@PathVariable("id") String id) {
        return mapRunWayCommandService.del(id) ? Result.ok() : Result.fail();
    }

}
