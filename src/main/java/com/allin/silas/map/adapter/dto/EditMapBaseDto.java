package com.allin.silas.map.adapter.dto;

import com.allin.view.base.validator.ValidateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 地图基础信息表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EditMapBaseDto extends AddMapBaseDto {
    /**
     * 主键id
     */
    @NotBlank(groups = ValidateGroup.Edit.class)
    private String id;
}