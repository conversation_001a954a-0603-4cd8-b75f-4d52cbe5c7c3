package com.allin.silas.map.adapter.vo;

import com.allin.silas.map.app.entity.MapClearanceArea;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 地图跑道净空区表
 */
@Data
public class MapClearanceAreaVo {
    /**
     * 主键id
     */
    private String id;

    /**
     * 跑道id
     */
    private String mapRunwayId;

    /**
     * 净空区编号,1第一段,2第二段,3水平段
     */
    private Integer areaCode;

    /**
     * 区域geojson
     */
    private String areaCoord;

    /**
     * 净空区段长度
     */
    private Integer areaLength;

    /**
     * 净空区方向,1:A到B,2:B到A
     */
    private Integer areaDirection;

    /**
     * 显示配置
     */
    private String displayConfig;

    public static MapClearanceAreaVo of(MapClearanceArea mapClearanceArea) {
        final MapClearanceAreaVo vo = new MapClearanceAreaVo();
        BeanUtils.copyProperties(mapClearanceArea, vo);
        return vo;
    }
}