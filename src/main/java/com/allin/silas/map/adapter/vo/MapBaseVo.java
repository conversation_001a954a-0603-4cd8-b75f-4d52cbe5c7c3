package com.allin.silas.map.adapter.vo;

import com.allin.silas.map.app.entity.MapBase;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 地图基本信息对象
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@Data
public class MapBaseVo {

    /**
     * 主键id
     */
    private String id;

    /**
     * 地图类型,1-卫星,2-街道,3-轮廓,4-地形
     */
    private Integer mapType;

    /**
     * 坐标类型,WGS84,GCJ02,BD09
     */
    private String coordType;

    /**
     * 地图地址
     */
    private String mapUrl;

    /**
     * 中心点geojson.geometry
     */
    private String centerCoord;

    /**
     * 是否可用,0-否,1-是
     */
    private Integer isUseable;

    /**
     * 是否为默认地图,0-否,1-是
     */
    private Integer isDefault;

    public static MapBaseVo of(MapBase mapBase) {
        final MapBaseVo mapBaseVo = new MapBaseVo();
        BeanUtils.copyProperties(mapBase, mapBaseVo);
        return mapBaseVo;
    }
}
