package com.allin.silas.map.adapter.vo;

import com.allin.silas.map.app.entity.MapBirdNet;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 地图捕鸟网表
 */
@Data
public class MapBirdNetVo {
    /**
     * 主键id
     */
    private String id;

    /**
     * 捕鸟网名称,长度不超过10字
     */
    private String birdNetName;

    /**
     * 捕鸟网geojson
     */
    private String birdNetCoord;

    /**
     * 捕鸟网位置,1-核心区,2-场内,3-场外,4-复合区
     */
    private Integer birdNetPosition;

    /**
     * 跑道id
     */
    private String mapRunwayId;

    /**
     * 显示配置
     */
    private String displayConfig;

    /**
     * 备注信息
     */
    private String remarks;

    public static MapBirdNetVo of(MapBirdNet mapBirdNet) {
        final MapBirdNetVo mapBirdNetVo = new MapBirdNetVo();
        BeanUtils.copyProperties(mapBirdNet, mapBirdNetVo);
        return mapBirdNetVo;
    }
}