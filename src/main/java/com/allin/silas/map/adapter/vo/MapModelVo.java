package com.allin.silas.map.adapter.vo;

import com.allin.silas.map.app.entity.MapModel;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 模型配置表
 */
@Data
public class MapModelVo {
    /**
     * 主键id
     */
    private String id;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型文件地址,上传所得路径
     */
    private String modelUrl;

    /**
     * 模型坐标,geojson.geometry
     */
    private String modelCoord;

    /**
     * 高度,默认0
     */
    private Double altitude;

    /**
     * 缩放值,默认0
     */
    private Double scale;

    /**
     * 绕X轴旋转角度,默认0
     */
    private Double rotationX;

    /**
     * 绕Y轴旋转角度,默认0
     */
    private Double rotationY;

    /**
     * 绕Z轴旋转角度,默认0
     */
    private Double rotationZ;

    /**
     * 是否可用,0-否,1-是
     */
    private Integer isUseable;

    public static MapModelVo of(MapModel mapModel) {
        final MapModelVo mapModelVo = new MapModelVo();
        BeanUtils.copyProperties(mapModel, mapModelVo);
        return mapModelVo;
    }
}