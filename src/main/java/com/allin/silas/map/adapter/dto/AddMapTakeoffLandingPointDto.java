package com.allin.silas.map.adapter.dto;

import com.allin.silas.map.app.enums.TakeoffLandingPointSeqEnums;
import com.allin.silas.map.app.enums.TakeoffLandingPointTypeEnums;
import com.allin.view.base.enums.validator.IEnumValid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 地图起飞降落点表
 */
@Data
public class AddMapTakeoffLandingPointDto {

    /**
     * 类型, takeoff, landing
     *
     * @see TakeoffLandingPointTypeEnums#code
     */
    @NotBlank
    @IEnumValid(target = TakeoffLandingPointTypeEnums.class)
    private String pointType;

    /**
     * 起飞降落点geojson
     */
    @NotBlank
    private String pointCoord;

    /**
     * 点名称,起飞点1,降落点1
     *
     * @see TakeoffLandingPointSeqEnums#code
     */
    @NotBlank
    private String pointName;

    /**
     * 点序号,1,2
     */
    @NotNull
    @IEnumValid(target = TakeoffLandingPointSeqEnums.class)
    private Integer pointSeq;
}