package com.allin.silas.map.adapter.vo;

import com.allin.silas.map.app.entity.MapView;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 地图视角表
 */
@Data
public class MapViewVo {

    /**
     * 主键id
     */
    private String id;

    /**
     * 视角名称,长度不超过20个字
     */
    private String viewName;

    /**
     * 显示设置
     */
    private String displayConfig;

    /**
     * 视图类型,1-系统管理
     */
    private Integer viewType;

    /**
     * 是否为默认视角,0-否,1-是
     */
    private Integer isDefault;

    public static MapViewVo of(MapView mapView) {
        final MapViewVo mapViewVo = new MapViewVo();
        BeanUtils.copyProperties(mapView, mapViewVo);
        return mapViewVo;
    }
}