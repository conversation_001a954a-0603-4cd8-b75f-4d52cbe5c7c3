package com.allin.silas.map.adapter.controller;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.common.util.geo.GeoJsonUtils;
import com.allin.silas.map.adapter.dto.AddMapModelDto;
import com.allin.silas.map.adapter.dto.EditMapModelDto;
import com.allin.silas.map.adapter.vo.MapModelVo;
import com.allin.silas.map.app.service.MapModelCommandService;
import com.allin.silas.map.app.service.MapQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.constant.FileConstants;
import com.allin.view.base.domain.Result;
import com.allin.view.log.annotation.Log;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.CacheControl;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 地图模型管理
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/map/model")
class MapModelController {

    private final MapQueryService mapQueryService;

    private final MapModelCommandService mapModelCommandService;

    MapModelController(MapQueryService mapQueryService,
                       MapModelCommandService mapModelCommandService) {
        this.mapQueryService = mapQueryService;
        this.mapModelCommandService = mapModelCommandService;
    }

    /**
     * 查看地图模型列表
     */
    @GetMapping("/list")
    public Result<List<MapModelVo>> list() {
        return Result.ok(mapQueryService.listModel(SecurityContextHolder.getProjectId()));
    }

    /**
     * 查看模型详情
     */
    @GetMapping("/{id}")
    public Result<MapModelVo> info(@PathVariable("id") String id) {
        return Result.ok(mapQueryService.getModelInfo(id));
    }

    /**
     * 获取模型
     *
     * @param path 模型路径需要在项目部署路径下
     */
    @GetMapping
    public ResponseEntity<?> getModel(@RequestParam("path") String path) throws IOException {
        // 只允许匹配如下带后缀的文件
        String regex = "^[\\w\\-/]+\\.(glb|gltf|obj)$";
        if (StrUtil.isBlank(path) || !path.matches(regex)) {
            return ResponseEntity.notFound().build();
        }

        // 规范化路径并防止路径穿越
        Path baseDir = Paths.get(FileConstants.USER_DIR).toAbsolutePath().normalize();
        Path filePath = Paths.get(FileConstants.USER_DIR, path).toAbsolutePath().normalize();

        if (!filePath.startsWith(baseDir)) {
            log.error("非法路径{}", path);
            return ResponseEntity.notFound().build();
        }

        Resource resource = new FileSystemResource(filePath);
        if (!resource.exists()) {
            return ResponseEntity.notFound().build();
        }

        // 返回带有 ETag 的响应
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .cacheControl(CacheControl.maxAge(30, TimeUnit.DAYS)
                        .cachePrivate()
                        .noTransform()
                        .mustRevalidate())
                .eTag(String.valueOf(Files.getLastModifiedTime(filePath).toMillis()))
                .body(resource);
    }

    /**
     * 新增模型
     */
    @Log(title = "地图模型管理", operDesc = "新增模型")
    @PostMapping
    public Result<String> add(@RequestBody @Validated AddMapModelDto addMapModelDto) {
        GeoJsonUtils.checkGeoJsonGeometry(addMapModelDto.getModelCoord());
        return mapModelCommandService.add(addMapModelDto) ? Result.ok() : Result.fail();
    }

    /**
     * 编辑模型
     */
    @Log(title = "地图模型管理", operDesc = "编辑模型")
    @PutMapping
    public Result<String> edit(@RequestBody @Validated EditMapModelDto editMapModelDto) {
        GeoJsonUtils.checkGeoJsonGeometry(editMapModelDto.getModelCoord());
        return mapModelCommandService.edit(editMapModelDto) ? Result.ok() : Result.fail();
    }

    /**
     * 删除模型
     */
    @Log(title = "地图模型管理", operDesc = "删除模型")
    @DeleteMapping("/{id}")
    public Result<String> del(@PathVariable("id") String id) {
        return mapModelCommandService.del(id) ? Result.ok() : Result.fail();
    }

}
