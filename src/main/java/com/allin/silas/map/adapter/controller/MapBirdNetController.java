package com.allin.silas.map.adapter.controller;

import com.allin.silas.common.util.geo.GeoJsonUtils;
import com.allin.silas.map.adapter.dto.AddMapBirdNetDto;
import com.allin.silas.map.adapter.dto.EditMapBirdNetDto;
import com.allin.silas.map.adapter.vo.MapBirdNetVo;
import com.allin.silas.map.app.service.MapBirdNetCommandService;
import com.allin.silas.map.app.service.MapQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import com.allin.view.log.annotation.Log;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地图捕鸟网管理
 *
 * <AUTHOR>
 * @since 2025/6/9
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/map/bird_net")
class MapBirdNetController {

    private final MapQueryService mapQueryService;

    private final MapBirdNetCommandService mapBirdNetCommandService;

    MapBirdNetController(MapQueryService mapQueryService,
                         MapBirdNetCommandService mapBirdNetCommandService) {
        this.mapQueryService = mapQueryService;
        this.mapBirdNetCommandService = mapBirdNetCommandService;
    }

    /**
     * 查看捕鸟网列表
     */
    @GetMapping("/list")
    public Result<List<MapBirdNetVo>> list() {
        return Result.ok(mapQueryService.listBirdNet(SecurityContextHolder.getProjectId()));
    }

    /**
     * 查看捕鸟网详情
     */
    @GetMapping("/{id}")
    public Result<MapBirdNetVo> info(@PathVariable("id") String id) {
        return Result.ok(mapQueryService.getMapBirdNet(id));
    }

    /**
     * 新增捕鸟网
     */
    @Log(title = "地图捕鸟网管理", operDesc = "新增捕鸟网")
    @PostMapping
    public Result<Void> add(@RequestBody @Validated AddMapBirdNetDto addMapBirdNetDto) {
        GeoJsonUtils.checkGeoJsonGeometry(addMapBirdNetDto.getBirdNetCoord());
        return mapBirdNetCommandService.add(addMapBirdNetDto) ? Result.ok() : Result.fail();
    }

    /**
     * 修改捕鸟网
     */
    @Log(title = "地图捕鸟网管理", operDesc = "修改捕鸟网")
    @PutMapping
    public Result<Void> edit(@RequestBody @Validated EditMapBirdNetDto editMapBirdNetDto) {
        GeoJsonUtils.checkGeoJsonGeometry(editMapBirdNetDto.getBirdNetCoord());
        return mapBirdNetCommandService.edit(editMapBirdNetDto) ? Result.ok() : Result.fail();
    }

    /**
     * 删除捕鸟网
     */
    @Log(title = "地图捕鸟网管理", operDesc = "删除捕鸟网")
    @DeleteMapping("/{id}")
    public Result<String> del(@PathVariable("id") String id) {
        return mapBirdNetCommandService.del(id) ? Result.ok() : Result.fail();
    }

}
