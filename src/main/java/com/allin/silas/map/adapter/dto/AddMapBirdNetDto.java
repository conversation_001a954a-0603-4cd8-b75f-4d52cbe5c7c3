package com.allin.silas.map.adapter.dto;

import com.allin.silas.map.app.entity.MapBirdNet;
import com.allin.silas.map.app.enums.RegionPositionEnums;
import com.allin.silas.map.app.enums.RegionTypeEnums;
import com.allin.view.base.enums.validator.IEnumValid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;

/**
 * 地图捕鸟网新增 dto
 */
@Data
public class AddMapBirdNetDto {

    /**
     * 捕鸟网名称,长度不超过10字
     */
    @Length(max = 10)
    @NotBlank
    private String birdNetName;

    /**
     * 捕鸟网geojson
     */
    @NotBlank
    private String birdNetCoord;

    /**
     * 捕鸟网位置,1-核心区,2-场内,3-场外,4-复合区
     *
     * @see RegionPositionEnums#code
     */
    @NotNull
    @IEnumValid(target = RegionTypeEnums.class)
    private Integer birdNetPosition;

    /**
     * 跑道id
     */
    @NotBlank
    private String mapRunwayId;

    /**
     * 显示配置
     */
    @NotBlank
    private String displayConfig;

    /**
     * 备注信息
     */
    private String remarks;

    public MapBirdNet toEntity() {
        final MapBirdNet mapBirdNet = new MapBirdNet();
        BeanUtils.copyProperties(this, mapBirdNet);
        return mapBirdNet;
    }
}