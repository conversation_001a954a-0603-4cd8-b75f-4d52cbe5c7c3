package com.allin.silas.map.adapter.dto;

import com.allin.silas.map.app.entity.MapBase;
import com.allin.silas.map.app.enums.MapCoordEnums;
import com.allin.silas.map.app.enums.MapTypeEnums;
import com.allin.view.base.enums.validator.IEnumValid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.BeanUtils;

/**
 * 地图基础信息表
 */
@Data
public class AddMapBaseDto {

    /**
     * 地图类型,1-卫星,2-街道,3-轮廓,4-地形
     */
    @IEnumValid(target = MapTypeEnums.class, isNotNull = true)
    private Integer mapType;

    /**
     * 坐标类型,WGS84,GCJ02,BD09
     */
    @IEnumValid(target = MapCoordEnums.class, isNotNull = true)
    private String coordType;

    /**
     * 地图地址
     */
    @NotBlank
    private String mapUrl;

    /**
     * 中心点geojson.geometry
     */
    @NotBlank
    private String centerCoord;

    /**
     * 是否可用,0-否,1-是
     */
    @Range(min = 0, max = 1)
    private Integer isUseable;

    /**
     * 是否为默认地图,0-否,1-是
     */
    @Range(min = 0, max = 1)
    private Integer isDefault;

    public MapBase toEntity() {
        final MapBase mapBase = new MapBase();
        BeanUtils.copyProperties(this, mapBase);
        return mapBase;
    }
}