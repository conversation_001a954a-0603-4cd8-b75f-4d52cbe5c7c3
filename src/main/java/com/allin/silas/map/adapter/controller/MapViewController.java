package com.allin.silas.map.adapter.controller;

import com.allin.silas.map.adapter.dto.AddMapViewDto;
import com.allin.silas.map.adapter.vo.MapViewVo;
import com.allin.silas.map.app.service.MapQueryService;
import com.allin.silas.map.app.service.MapViewCommandService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import com.allin.view.log.annotation.Log;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地图视图管理
 *
 * <AUTHOR>
 * @since 2025/6/9
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/map/view")
class MapViewController {

    private final MapQueryService mapQueryService;

    private final MapViewCommandService mapViewCommandService;

    MapViewController(MapQueryService mapQueryService,
                      MapViewCommandService mapViewCommandService) {
        this.mapQueryService = mapQueryService;
        this.mapViewCommandService = mapViewCommandService;
    }

    /**
     * 查看视图列表
     */
    @GetMapping("/list")
    public Result<List<MapViewVo>> list() {
        return Result.ok(mapQueryService.listView(SecurityContextHolder.getProjectId()));
    }

    /**
     * 新增视图
     */
    @Log(title = "地图视图管理", operDesc = "新增视图")
    @PostMapping
    public Result<String> add(@RequestBody @Validated AddMapViewDto addMapViewDto) {
        return mapViewCommandService.add(addMapViewDto) ? Result.ok() : Result.fail();
    }

    /**
     * 设置默认视图
     */
    @Log(title = "地图视图管理", operDesc = "设置默认视图")
    @PutMapping("/default/{id}")
    public Result<String> setDefault(@PathVariable("id") String id) {
        return mapViewCommandService.setDefault(id) ? Result.ok() : Result.fail();
    }

    /**
     * 删除视图
     */
    @Log(title = "地图视图管理", operDesc = "删除视图")
    @DeleteMapping("/{id}")
    public Result<String> del(@PathVariable("id") String id) {
        return mapViewCommandService.del(id) ? Result.ok() : Result.fail();
    }

}
