package com.allin.silas.map.adapter.vo;

import com.allin.silas.map.app.entity.MapRegion;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 地图区域列表vo
 */
@Data
public class MapRegionVo {
    private String id;

    /**
     * 区域类型,1-上报区域,2-屏蔽区域,3-作业区域
     */
    private Integer regionType;

    /**
     * 区域名称,长度不超过10字
     */
    private String regionName;

    /**
     * 区域geojson
     */
    private String regionCoord;

    /**
     * 区域位置,1-核心区,2-场内,3-场外,4-复合区
     */
    private Integer regionPosition;

    /**
     * 显示配置
     */
    private String displayConfig;

    /**
     * 备注信息
     */
    private String remarks;

    public static MapRegionVo of(MapRegion mapRegion) {
        final MapRegionVo mapRegionVo = new MapRegionVo();
        BeanUtils.copyProperties(mapRegion, mapRegionVo);
        return mapRegionVo;
    }
}