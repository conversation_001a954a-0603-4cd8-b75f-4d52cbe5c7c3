package com.allin.silas.map.adapter.dto;

import com.allin.silas.map.app.enums.ClearanceAreaTypeEnums;
import com.allin.view.base.enums.base.IEnums;
import com.allin.view.base.enums.validator.IEnumValid;
import com.allin.view.base.i18n.I18nUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增地图跑道净空区配置信息
 */
@Data
public class AddMapClearanceAreaDto {
    /**
     * 净空区编号,1第一段,2第二段,3水平段
     *
     * @see ClearanceAreaTypeEnums#code
     */
    @NotNull
    @IEnumValid(target = ClearanceAreaTypeEnums.class)
    private Integer areaCode;

    /**
     * 净空区段长度, 单位米
     */
    @NotNull
    private Integer areaLength;

    /**
     * 显示配置
     */
    private String displayConfig;

    /**
     * 获取扩散率
     */
    @JsonIgnore
    public double getSpreadRate() {
        return IEnums.tryFindByCode(ClearanceAreaTypeEnums.class, areaCode)
                .orElseThrow(() -> new IllegalArgumentException(I18nUtil.isParamException("areaCode")))
                .getSpreadRate();
    }

    /**
     * 获取坡度
     */
    @JsonIgnore
    public double getSlope() {
        return IEnums.tryFindByCode(ClearanceAreaTypeEnums.class, areaCode)
                .orElseThrow(() -> new IllegalArgumentException(I18nUtil.isParamException("areaCode")))
                .getSlope();
    }
}