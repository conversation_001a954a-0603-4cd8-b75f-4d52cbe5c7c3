package com.allin.silas.map.adapter.dto;

import com.allin.silas.map.app.entity.MapRunway;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.geotools.data.geojson.GeoJSONReader;
import org.hibernate.validator.constraints.Length;
import org.locationtech.jts.geom.LineString;

/**
 * 地图跑道表
 */
@Data
public class AddMapRunwayDto {

    /**
     * 跑道名称,长度不超过20字
     */
    @Length(max = 20)
    @NotBlank
    private String runwayName;

    /**
     * 中心线geojson
     */
    @NotBlank
    private String centerLineCoord;

    /**
     * 跑道宽度
     */
    @NotNull
    private Integer runwayWidth;

    /**
     * 显示配置
     */
    @NotBlank
    private String displayConfig;

    /**
     * 跑道中心点geojson
     */
    @JsonIgnore
    public LineString centerLineGeometry() {
        return (LineString) GeoJSONReader.parseGeometry(centerLineCoord);
    }

    public MapRunway toEntity() {
        MapRunway mapRunway = new MapRunway();
        mapRunway.setRunwayName(runwayName);
        mapRunway.setCenterLineCoord(centerLineCoord);
        mapRunway.setRunwayWidth(runwayWidth);
        mapRunway.setDisplayConfig(displayConfig);
        return mapRunway;
    }

    public MapRunway toEntity(String id) {
        final MapRunway entity = toEntity();
        entity.setId(id);
        return entity;
    }
}