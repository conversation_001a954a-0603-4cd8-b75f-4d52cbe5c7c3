package com.allin.silas.map.adapter.controller;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.common.util.geo.GeoJsonUtils;
import com.allin.silas.map.adapter.dto.AddMapBaseDto;
import com.allin.silas.map.adapter.dto.EditMapBaseDto;
import com.allin.silas.map.adapter.vo.MapBaseVo;
import com.allin.silas.map.app.entity.MapBase;
import com.allin.silas.map.app.service.MapCommandService;
import com.allin.silas.map.app.service.MapQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.constant.FileConstants;
import com.allin.view.base.domain.Result;
import com.allin.view.base.validator.ValidateGroup;
import com.allin.view.log.annotation.Log;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.CacheControl;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 地图管理
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/map")
class MapController {

    private final MapQueryService mapQueryService;

    private final MapCommandService mapCommandService;

    MapController(MapQueryService mapQueryService,
                  MapCommandService mapCommandService) {
        this.mapQueryService = mapQueryService;
        this.mapCommandService = mapCommandService;
    }

    /**
     * 查看地图列表
     */
    @GetMapping("/list")
    public Result<List<MapBaseVo>> list() {
        return Result.ok(mapQueryService.listMap(SecurityContextHolder.getProjectId()));
    }

    /**
     * 查看地图详情
     */
    @GetMapping("/{id}")
    public Result<MapBaseVo> info(@PathVariable("id") String id) {
        return Result.ok(mapQueryService.getMapInfo(id));
    }

    /**
     * 获取GIS地图
     *
     * @param path 地图路径需要在项目部署路径下
     */
    @GetMapping("/tile")
    public ResponseEntity<?> getGisImg(@RequestParam("path") String path) throws IOException {
        // 只允许匹配如下带后缀的文件
        String regex = "^[\\w\\-/]+\\.(jpg|png|jpeg|webp)$";
        if (StrUtil.isBlank(path) || !path.matches(regex)) {
            return ResponseEntity.notFound().build();
        }

        // 规范化路径并防止路径穿越
        Path baseDir = Paths.get(FileConstants.USER_DIR).toAbsolutePath().normalize();
        Path filePath = Paths.get(FileConstants.USER_DIR, path).toAbsolutePath().normalize();

        if (!filePath.startsWith(baseDir)) {
            log.error("非法路径{}", path);
            return ResponseEntity.notFound().build();
        }

        Resource resource = new FileSystemResource(filePath);
        if (!resource.exists()) {
            return ResponseEntity.notFound().build();
        }

        // 返回带有 ETag 的响应
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .cacheControl(CacheControl.maxAge(30, TimeUnit.DAYS)
                        .cachePrivate()
                        .noTransform()
                        .mustRevalidate())
                .eTag(String.valueOf(Files.getLastModifiedTime(filePath).toMillis()))
                .body(resource);
    }

    /**
     * 校验地图是否已经存在
     *
     * @return 已有的地图id
     */
    @PostMapping("/is_exist")
    public Result<String> isExist(@RequestBody @Validated EditMapBaseDto formDto) {
        final MapBase existMap = mapQueryService.isExistMap(formDto.getMapType(),
                SecurityContextHolder.getProjectId(),
                formDto.getId());
        if (existMap == null) {
            return Result.ok();
        }
        return Result.ok(existMap.getId());
    }

    /**
     * 新增地图
     */
    @Log(title = "地图管理", operDesc = "新增地图")
    @PostMapping
    public Result<String> add(@RequestBody @Validated AddMapBaseDto formDto) {
        GeoJsonUtils.checkGeoJsonGeometry(formDto.getCenterCoord());
        return mapCommandService.add(formDto) ? Result.ok() : Result.fail();
    }

    /**
     * 编辑地图
     */
    @Log(title = "地图管理", operDesc = "编辑地图")
    @PutMapping
    public Result<String> edit(@RequestBody @Validated(ValidateGroup.Edit.class) EditMapBaseDto formDto) {
        GeoJsonUtils.checkGeoJsonGeometry(formDto.getCenterCoord());
        return mapCommandService.edit(formDto) ? Result.ok() : Result.fail();
    }

    /**
     * 删除地图
     */
    @Log(title = "地图管理", operDesc = "删除地图")
    @DeleteMapping("/{id}")
    public Result<String> del(@PathVariable("id") String id) {
        return mapCommandService.del(id) ? Result.ok() : Result.fail();
    }

}
