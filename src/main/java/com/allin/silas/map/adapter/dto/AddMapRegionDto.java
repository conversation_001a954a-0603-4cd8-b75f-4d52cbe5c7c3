package com.allin.silas.map.adapter.dto;

import com.allin.silas.map.app.entity.MapRegion;
import com.allin.silas.map.app.enums.RegionPositionEnums;
import com.allin.silas.map.app.enums.RegionTypeEnums;
import com.allin.view.base.enums.validator.IEnumValid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;

/**
 * 地图区域新增 dto
 */
@Data
public class AddMapRegionDto {

    /**
     * 区域类型,1-上报区域,2-屏蔽区域,3-作业区域
     *
     * @see RegionTypeEnums#code
     */
    @NotNull
    @IEnumValid(target = RegionTypeEnums.class)
    private Integer regionType;

    /**
     * 区域名称,长度不超过10字
     */
    @Length(max = 10)
    @NotBlank
    private String regionName;

    /**
     * 区域geojson
     */
    @NotBlank
    private String regionCoord;

    /**
     * 区域位置,1-核心区,2-场内,3-场外,4-复合区
     *
     * @see RegionPositionEnums#code
     */
    @NotNull
    @IEnumValid(target = RegionPositionEnums.class)
    private Integer regionPosition;

    /**
     * 显示配置
     */
    @NotBlank
    private String displayConfig;

    /**
     * 备注信息
     */
    private String remarks;

    public MapRegion toEntity() {
        final MapRegion mapRegion = new MapRegion();
        BeanUtils.copyProperties(this, mapRegion);
        return mapRegion;
    }
}