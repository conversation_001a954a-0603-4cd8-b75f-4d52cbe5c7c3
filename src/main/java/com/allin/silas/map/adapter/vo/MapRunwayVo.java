package com.allin.silas.map.adapter.vo;

import com.allin.silas.map.app.entity.MapRunway;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 地图跑道聚合对象
 */
@Data
public class MapRunwayVo {
    /**
     * 主键id
     */
    private String id;

    /**
     * 跑道名称,长度不超过10字
     */
    private String runwayName;

    /**
     * 中心线geojson
     */
    private String centerLineCoord;

    /**
     * 中心点geojson
     */
    private String centerCoord;

    /**
     * 跑道区域geojson
     */
    private String areaCoord;

    /**
     * 跑道宽度
     */
    private Integer runwayWidth;

    /**
     * 显示配置
     */
    private String displayConfig;

    /**
     * 备注信息
     */
    private String remarks;

    public static MapRunwayVo of(MapRunway mapRunway) {
        final MapRunwayVo vo = new MapRunwayVo();
        BeanUtils.copyProperties(mapRunway, vo);
        return vo;
    }
}