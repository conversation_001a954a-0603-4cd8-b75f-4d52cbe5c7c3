package com.allin.silas.map.adapter.dto;

import com.allin.silas.map.app.entity.MapModel;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.BeanUtils;

/**
 * 模型配置表
 */
@Data
public class AddMapModelDto {

    /**
     * 模型名称
     */
    @NotBlank
    @Size(max = 10)
    private String modelName;

    /**
     * 模型文件地址
     */
    @NotNull
    private String modelUrl;

    /**
     * 模型坐标,geojson.geometry
     */
    @NotBlank
    private String modelCoord;

    /**
     * 高度,默认0
     */
    @NotNull
    private Double altitude;

    /**
     * 缩放值,默认0
     */
    @NotNull
    private Double scale;

    /**
     * 绕X轴旋转角度,默认0
     */
    @NotNull
    private Double rotationX;

    /**
     * 绕Y轴旋转角度,默认0
     */
    @NotNull
    private Double rotationY;

    /**
     * 绕Z轴旋转角度,默认0
     */
    @NotNull
    private Double rotationZ;

    /**
     * 是否可用,0-否,1-是
     */
    @Range(min = 0, max = 1)
    private Integer isUseable;

    public MapModel toEntity() {
        final MapModel mapModel = new MapModel();
        BeanUtils.copyProperties(this, mapModel);
        return mapModel;
    }
}