package com.allin.silas.map.adapter.dto;

import com.allin.silas.common.util.geo.GeoJsonUtils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 地图跑道表
 */
@Data
public class EditMapRunwayFromDto {

    /**
     * 跑道配置
     */
    private EditMapRunwayDto runway;

    /**
     * 净空区配置
     */
    @NotEmpty
    private List<@Valid AddMapClearanceAreaDto> clearanceAreas;

    /**
     * 起降点配置
     */
    private List<@Valid AddMapTakeoffLandingPointDto> takeoffLandingPoints;

    /**
     * 校验参数
     */
    public void validate() {
        GeoJsonUtils.checkGeoJsonGeometry(runway.getCenterLineCoord());
        for (AddMapTakeoffLandingPointDto takeoffLandingPoint : getTakeoffLandingPoints()) {
            GeoJsonUtils.checkGeoJsonGeometry(takeoffLandingPoint.getPointCoord(), "pointCoord");
        }
    }

}