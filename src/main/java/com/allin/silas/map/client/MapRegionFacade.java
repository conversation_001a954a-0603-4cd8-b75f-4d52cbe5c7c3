package com.allin.silas.map.client;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
public interface MapRegionFacade {
    /**
     * 获取当前projectId下所有区域名称和id
     *
     * @return key 区域名称 value 区域id
     */
    Map<String, String> mapRegionIdByProjectId(String projectId);

    /**
     * 计算目标所在区域
     *
     * @return 返回目标所在区域列表，可能为空集合
     */
    List<String> calculateRegion(double longitude, double latitude);
}
