package com.allin.silas.map.client.excel;

import cn.hutool.extra.spring.SpringUtil;
import cn.idev.excel.converters.Converter;
import cn.idev.excel.converters.WriteConverterContext;
import cn.idev.excel.metadata.data.WriteCellData;
import com.allin.silas.map.app.manager.MapBirdNetManager;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * Excel 捕鸟网id转捕鸟网名称转换器
 *
 * <AUTHOR>
 * @since 2025/7/8
 */
@Slf4j
public class MapBirdNetIdToNameConverter implements Converter<Object> {

    private final MapBirdNetManager mapBirdNetManager;

    public MapBirdNetIdToNameConverter() {
        this.mapBirdNetManager = SpringUtil.getBean(MapBirdNetManager.class);
    }

    @Override
    @SuppressWarnings("unchecked")
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Object> context) {
        final Object value = context.getValue();
        if (Objects.isNull(context.getValue())) {
            return new WriteCellData<>("-");
        }
        try {
            if (value instanceof String birdNetId) {
                final String birdNetName = mapBirdNetManager.listNameFromCacheById(birdNetId);
                return new WriteCellData<>(Objects.requireNonNullElse(birdNetName, "-"));
            }
            log.warn("不支持的数据类型: {}", value.getClass());
            return new WriteCellData<>("-");
        } catch (Exception e) {
            log.error("捕鸟网名转换失败", e);
            return new WriteCellData<>("-");
        }
    }
}
