package com.allin.silas.map.client.annotation;

import com.allin.silas.map.client.annotation.serializer.MapRegionIdAsNullSerializer;
import com.allin.silas.map.client.annotation.serializer.MapRegionIdToNameSerializer;
import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * 区域id转区域名称
 *
 * <AUTHOR>
 * @since 2025/7/3
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonSerialize(nullsUsing = MapRegionIdAsNullSerializer.class, using = MapRegionIdToNameSerializer.class)
public @interface MapRegionIdToName {

    /**
     * 如果配置了key就会将转换后的值放入新的key中，被标记的字段的值依然不变
     *
     * @see com.allin.view.config.serialize.annotation.ApiFoxNoIgnore
     */
    String key() default "";
}
