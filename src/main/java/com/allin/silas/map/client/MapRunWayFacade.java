package com.allin.silas.map.client;

import jakarta.annotation.Nullable;

/**
 * 地图跑道服务门面
 * <p>
 * 对外统一封装调用能力
 *
 * <AUTHOR>
 * @since 2025/6/24
 */
public interface MapRunWayFacade {

    /**
     * 计算目标离跑道中心线的距离
     * 没有找到跑道时返回 null
     *
     * @return 距离, 单位米
     */
    @Nullable
    Double calculateDistance(double longitude, double latitude, String runWayId);

    /**
     * 计算目标离跑道中心的距离
     * 没有找到跑道时返回 null
     *
     * @return 距离, 单位米
     */
    @Nullable
    Double calculateCenterDistance(double longitude, double latitude, String runWayId);
}
