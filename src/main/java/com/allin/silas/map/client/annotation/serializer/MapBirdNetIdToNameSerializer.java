package com.allin.silas.map.client.annotation.serializer;

import cn.hutool.extra.spring.SpringUtil;
import com.allin.silas.map.app.manager.MapBirdNetManager;
import com.allin.silas.map.client.annotation.MapBirdNetIdToName;
import com.allin.view.base.jackson.IJacksonSerializer;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Objects;

/**
 * 捕鸟网id转名称
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
@Slf4j
@NoArgsConstructor
public class MapBirdNetIdToNameSerializer extends JsonSerializer<Object> implements ContextualSerializer, IJacksonSerializer {

    protected MapBirdNetManager mapBirdNetManager;

    protected String key;

    public MapBirdNetIdToNameSerializer(MapBirdNetManager mapBirdNetManager, String key) {
        this.mapBirdNetManager = mapBirdNetManager;
        this.key = key;
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public void serialize(Object mapBirdNetId, JsonGenerator json, SerializerProvider serializerProvider) throws IOException {
        Object newObj = null;
        if (Objects.isNull(mapBirdNetId)) {
            return;
        }
        try {
            if (mapBirdNetId instanceof String birdNetId) {
                newObj = mapBirdNetManager.listNameFromCacheById(birdNetId);
            }

        } catch (Exception e) {
            log.error("无法将mapBirdNetId转换为Name", e);
        } finally {
            doSerialize(json, mapBirdNetId, newObj);
        }
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider provider, BeanProperty property) throws JsonMappingException {
        if (property == null) {
            return provider.findNullValueSerializer(null);
        }

        Class<?> rawClass = property.getType().getRawClass();
        if (handledType(rawClass)) {
            MapBirdNetIdToName annotation = property.getAnnotation(MapBirdNetIdToName.class);
            if (annotation == null) {
                annotation = property.getContextAnnotation(MapBirdNetIdToName.class);
            }
            return new MapBirdNetIdToNameSerializer(SpringUtil.getBean(MapBirdNetManager.class), annotation.key());
        }

        return provider.findValueSerializer(property.getType(), property);
    }


    public boolean handledType(Class<?> rawClass) {
        return rawClass == String.class;
    }
}

