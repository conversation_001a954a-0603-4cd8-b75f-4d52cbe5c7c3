package com.allin.silas.map.client.annotation.serializer;

import cn.hutool.extra.spring.SpringUtil;
import com.allin.silas.map.app.manager.MapRegionManager;
import com.allin.silas.map.client.annotation.MapRegionIdToName;
import com.allin.view.base.jackson.IJacksonSerializer;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 区域id转名称
 *
 * <AUTHOR>
 * @since 2024/3/12
 */
@Slf4j
@NoArgsConstructor
public class MapRegionIdToNameSerializer extends JsonSerializer<Object> implements ContextualSerializer, IJacksonSerializer {

    protected MapRegionManager mapRegionManager;

    protected String key;

    public MapRegionIdToNameSerializer(MapRegionManager mapRegionManager, String key) {
        this.mapRegionManager = mapRegionManager;
        this.key = key;
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public void serialize(Object mapRegionId, JsonGenerator json, SerializerProvider serializerProvider) throws IOException {
        Object newObj = null;
        if (Objects.isNull(mapRegionId)) {
            return;
        }
        try {
            if (mapRegionId instanceof String regionId) {
                newObj = mapRegionManager.listNameFromCacheById(regionId);
            } else if (mapRegionId instanceof List regionIds) {
                newObj = mapRegionManager.listNameFromCacheByIds(regionIds);
            }

        } catch (Exception e) {
            log.error("无法将mapRegionId转换为Name", e);
        } finally {
            doSerialize(json, mapRegionId, newObj);
        }
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider provider, BeanProperty property) throws JsonMappingException {
        if (property == null) {
            return provider.findNullValueSerializer(null);
        }

        Class<?> rawClass = property.getType().getRawClass();
        if (handledType(rawClass)) {
            MapRegionIdToName annotation = property.getAnnotation(MapRegionIdToName.class);
            if (annotation == null) {
                annotation = property.getContextAnnotation(MapRegionIdToName.class);
            }
            return new MapRegionIdToNameSerializer(SpringUtil.getBean(MapRegionManager.class), annotation.key());
        }

        return provider.findValueSerializer(property.getType(), property);
    }


    public boolean handledType(Class<?> rawClass) {
        return rawClass == String.class || List.class.isAssignableFrom(rawClass);
    }
}

