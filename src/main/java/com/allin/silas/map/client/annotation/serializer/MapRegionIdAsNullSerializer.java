package com.allin.silas.map.client.annotation.serializer;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.map.client.annotation.MapRegionIdToName;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import java.io.IOException;
import java.lang.reflect.Field;

/**
 * MapRegionId为null的处理逻辑
 *
 * <AUTHOR>
 * @since 2025/7/3
 */
@Slf4j
@NoArgsConstructor
public class MapRegionIdAsNullSerializer extends JsonSerializer<Object> {

    @Override
    public void serialize(Object s, JsonGenerator json, SerializerProvider serializerProvider) throws IOException {
        // 获取当前正在序列化的属性的名称
        String fieldName = json.getOutputContext().getCurrentName();

        // 获取当前正在序列化的属性的所属类
        Class<?> declaringClass = json.getOutputContext().getCurrentValue().getClass();

        // 通过反射获取属性上的注解信息
        Field field = ReflectionUtils.findField(declaringClass, fieldName);
        if (field == null) {
            json.writeObject(s);
            return;
        }

        MapRegionIdToName annotation = field.getAnnotation(MapRegionIdToName.class);

        if (annotation != null && StrUtil.isNotBlank(annotation.key())) {
            json.writeObject(s);
        } else {
            json.writeNull();
        }
    }
}

