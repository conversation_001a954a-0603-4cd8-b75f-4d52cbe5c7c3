package com.allin.silas.map.client.excel;

import cn.hutool.extra.spring.SpringUtil;
import cn.idev.excel.converters.Converter;
import cn.idev.excel.converters.WriteConverterContext;
import cn.idev.excel.metadata.data.WriteCellData;
import com.allin.silas.map.app.manager.MapRegionManager;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * Excel 区域id转区域名称转换器
 *
 * <AUTHOR>
 * @since 2025/7/8
 */
@Slf4j
public class MapRegionIdToNameConverter implements Converter<Object> {

    private final MapRegionManager mapRegionManager;

    public MapRegionIdToNameConverter() {
        this.mapRegionManager = SpringUtil.getBean(MapRegionManager.class);
    }

    @Override
    @SuppressWarnings("unchecked")
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Object> context) {
        final Object value = context.getValue();
        if (Objects.isNull(context.getValue())) {
            return new WriteCellData<>("-");
        }
        try {
            if (value instanceof String regionId) {
                final String regionName = mapRegionManager.listNameFromCacheById(regionId);
                return new WriteCellData<>(Objects.requireNonNullElse(regionName, "-"));
            }
            if (value instanceof List regionIds) {
                return new WriteCellData<>(String.join(",", mapRegionManager.listNameFromCacheByIds(regionIds)));
            }
            log.warn("不支持的数据类型: {}", value.getClass());
            return new WriteCellData<>("-");
        } catch (Exception e) {
            log.error("用户名转换失败", e);
            return new WriteCellData<>("-");
        }
    }
}
