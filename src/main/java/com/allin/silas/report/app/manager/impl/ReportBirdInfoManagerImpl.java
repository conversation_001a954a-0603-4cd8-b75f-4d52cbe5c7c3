package com.allin.silas.report.app.manager.impl;

import com.allin.silas.event.client.EventInvasionFacade;
import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import com.allin.silas.event.client.enums.EventInvasionTypeEnums;
import com.allin.silas.report.app.entity.ReportBirdInfo;
import com.allin.silas.report.app.manager.ReportBirdInfoManager;
import com.allin.view.base.exception.service.ValidationFailureException;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
@Service
public class ReportBirdInfoManagerImpl implements ReportBirdInfoManager {

    private final EventInvasionFacade eventInvasionFacade;

    public ReportBirdInfoManagerImpl(EventInvasionFacade eventInvasionFacade) {
        this.eventInvasionFacade = eventInvasionFacade;
    }

    @Override
    public void pushInvasionEvent(ReportBirdInfo reportBirdInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion) {
        if (reportBirdInfo.getHandleStatus() == 0) {
            return;
        }

        boolean isOk = eventInvasionFacade.reportPushEvent(buildReportPushEventInvasion(reportBirdInfo, eventInvasion));
        if (!isOk) {
            // 推送失败
            throw new ValidationFailureException("入侵处置事件推送失败");
        }
    }

    @Override
    public void pushInvasionEvent(Map<ReportBirdInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto> map) {
        map.forEach((reportBirdInfo, eventInvasion) -> {
            boolean isOk = eventInvasionFacade.reportPushEvent(buildReportPushEventInvasion(reportBirdInfo, eventInvasion));
            if (!isOk) {
                // 推送失败
                throw new ValidationFailureException("入侵处置事件推送失败");
            }
        });
    }

    private ReportPushEventInvasionDto buildReportPushEventInvasion(ReportBirdInfo reportBirdInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion) {
        return ReportPushEventInvasionDto.builder()
                .sourceId(reportBirdInfo.getId())
                .eventType(EventInvasionTypeEnums.BIRD_QUA.getCode())
                .discoverTime(reportBirdInfo.getDiscoverTime())
                .discoverRegionId(reportBirdInfo.getRegionId())
                .discoverLongitude(reportBirdInfo.getLongitude())
                .discoverLatitude(reportBirdInfo.getLatitude())
                .altitude(reportBirdInfo.getHeight())
                .dangerLevel(reportBirdInfo.getBirdRiskLevel())
                .handleEventInvasionDto(eventInvasion)
                .build();
    }
}
