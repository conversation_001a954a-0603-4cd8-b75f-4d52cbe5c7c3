package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.common.util.QueryWrapperUtils;
import com.allin.silas.report.adapter.query.ReportBirdNetInfoQuery;
import com.allin.silas.report.adapter.vo.ReportBirdNetInfoVo;
import com.allin.silas.report.app.entity.ReportBirdNetInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.service.ReportBirdNetInfoQueryService;
import com.allin.silas.report.infra.repository.ReportBirdNetInfoMapper;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportBirdNetQueryServiceImpl implements ReportBirdNetInfoQueryService {

    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportBirdNetInfoMapper reportBirdNetInfoMapper;

    public ReportBirdNetQueryServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                         ReportBirdNetInfoMapper reportBirdNetInfoMapper) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportBirdNetInfoMapper = reportBirdNetInfoMapper;
    }

    @Override
    public PageData<ReportBirdNetInfoVo> page(PageParam pageParam, ReportBirdNetInfoQuery query) {
        final Page<ReportBirdNetInfo> page = pageParam.toPage();
        LambdaQueryWrapper<ReportBirdNetInfo> wrapper = Wrappers.lambdaQuery(ReportBirdNetInfo.class)
                .eq(ReportBirdNetInfo::getProjectId, query.getProjectId())
                .eq(ReportBirdNetInfo::getIsDeleted, 0)
                .eq(StringUtils.isNotBlank(query.getBirdNetId()), ReportBirdNetInfo::getBirdNetId, query.getBirdNetId())
                .eq(StringUtils.isNotBlank(query.getBirdName()), ReportBirdNetInfo::getBirdName, query.getBirdName())
                .eq(Objects.nonNull(query.getBirdRiskLevel()), ReportBirdNetInfo::getBirdRiskLevel, query.getBirdRiskLevel())
                .orderByDesc(ReportBirdNetInfo::getDiscoverTime);

        QueryWrapperUtils.buildDateRangeQuery(wrapper, query.getDiscoverStartTime(), query.getDiscoverEndTime(), ReportBirdNetInfo::getDiscoverTime);
        reportBirdNetInfoMapper.selectPage(page, wrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageData.empty();
        }

        Map<String, List<String>> map = reportDiscoverPersonMapper.map(ReportDiscoverPersonReportTypeEnums.BIRD_NET, page.getRecords().stream().map(ReportBirdNetInfo::getId).toList());
        return PageData.getInstance(page, page.getRecords().stream().map(reportBirdInfo -> {
            ReportBirdNetInfoVo vo = BeanUtil.copyProperties(reportBirdInfo, ReportBirdNetInfoVo.class);
            vo.setDiscoverUserIds(map.getOrDefault(reportBirdInfo.getId(), Collections.emptyList()));
            return vo;
        }).toList());
    }

    @Override
    public ReportBirdNetInfoVo info(String id) {
        ReportBirdNetInfo info = reportBirdNetInfoMapper.selectOne(Wrappers.lambdaQuery(ReportBirdNetInfo.class)
                .eq(ReportBirdNetInfo::getId, id)
                .eq(ReportBirdNetInfo::getProjectId, SecurityContextHolder.getProjectId()));
        if (Objects.isNull(info)) {
            return null;
        }

        ReportBirdNetInfoVo vo = BeanUtil.copyProperties(info, ReportBirdNetInfoVo.class);
        vo.setDiscoverUserIds(reportDiscoverPersonMapper.list(ReportDiscoverPersonReportTypeEnums.BIRD_NET, id));
        return vo;
    }
}
