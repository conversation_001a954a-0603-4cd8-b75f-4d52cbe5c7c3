package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.common.util.QueryWrapperUtils;
import com.allin.silas.report.adapter.query.ReportDroneInfoQuery;
import com.allin.silas.report.adapter.vo.ReportDroneInfoVo;
import com.allin.silas.report.app.entity.ReportDroneInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.service.ReportDroneInfoQueryService;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import com.allin.silas.report.infra.repository.ReportDroneInfoMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportDroneInfoQueryServiceImpl implements ReportDroneInfoQueryService {

    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportDroneInfoMapper reportDroneInfoMapper;

    public ReportDroneInfoQueryServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                           ReportDroneInfoMapper reportDroneInfoMapper) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportDroneInfoMapper = reportDroneInfoMapper;
    }

    @Override
    public PageData<ReportDroneInfoVo> page(PageParam pageParam, ReportDroneInfoQuery query) {
        final Page<ReportDroneInfo> page = pageParam.toPage();

        LambdaQueryWrapper<ReportDroneInfo> wrapper = getWrapper(query);

        QueryWrapperUtils.buildDateRangeQuery(wrapper, query.getDiscoverStartTime(), query.getDiscoverEndTime(), ReportDroneInfo::getDiscoverTime);
        reportDroneInfoMapper.selectPage(page, wrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageData.empty();
        }

        Map<String, List<String>> map = reportDiscoverPersonMapper.map(ReportDiscoverPersonReportTypeEnums.DRONE, page.getRecords().stream().map(ReportDroneInfo::getId).toList());
        return PageData.getInstance(page, page.getRecords().stream().map(reportDroneInfo -> {
            ReportDroneInfoVo vo = BeanUtil.copyProperties(reportDroneInfo, ReportDroneInfoVo.class);
            vo.setDiscoverUserIds(map.getOrDefault(reportDroneInfo.getId(), Collections.emptyList()));
            return vo;
        }).toList());
    }

    @Override
    public ReportDroneInfoVo info(String id) {
        ReportDroneInfo info = reportDroneInfoMapper.selectOne(Wrappers.lambdaQuery(ReportDroneInfo.class)
                .eq(ReportDroneInfo::getId, id)
                .eq(ReportDroneInfo::getProjectId, SecurityContextHolder.getProjectId()));
        if (Objects.isNull(info)) {
            return null;
        }

        ReportDroneInfoVo vo = BeanUtil.copyProperties(info, ReportDroneInfoVo.class);
        vo.setDiscoverUserIds(reportDiscoverPersonMapper.list(ReportDiscoverPersonReportTypeEnums.DRONE, id));
        return vo;
    }

    @Override
    public Map<Integer, Long> statisticsDroneTypeCount(ReportDroneInfoQuery query) {
        LambdaQueryWrapper<ReportDroneInfo> wrapper = getWrapper(query);
        QueryWrapperUtils.buildDateRangeQuery(wrapper, query.getDiscoverStartTime(), query.getDiscoverEndTime(), ReportDroneInfo::getDiscoverTime);
        List<ReportDroneInfo> list = reportDroneInfoMapper.selectList(wrapper);
        return CollectionUtils.isEmpty(list) ? Collections.emptyMap() : list.stream().collect(Collectors.groupingBy(ReportDroneInfo::getDroneType, Collectors.counting()));
    }

    private LambdaQueryWrapper<ReportDroneInfo> getWrapper(ReportDroneInfoQuery query) {
        return Wrappers.lambdaQuery(ReportDroneInfo.class)
                .eq(ReportDroneInfo::getProjectId, query.getProjectId())
                .eq(ReportDroneInfo::getIsDeleted, 0)
                .eq(Objects.nonNull(query.getHandleStatus()), ReportDroneInfo::getHandleStatus, query.getHandleStatus())
                .eq(StringUtils.isNotBlank(query.getRegionId()), ReportDroneInfo::getRegionId, query.getRegionId())
                .eq(Objects.nonNull(query.getDroneType()), ReportDroneInfo::getDroneType, query.getDroneType())
                .eq(Objects.nonNull(query.getIsAirspaceRegulated()), ReportDroneInfo::getIsAirspaceRegulated, query.getIsAirspaceRegulated())
                .like(StringUtils.isNotBlank(query.getDescription()), ReportDroneInfo::getDescription, query.getDescription())
                .orderByDesc(ReportDroneInfo::getDiscoverTime);
    }
}
