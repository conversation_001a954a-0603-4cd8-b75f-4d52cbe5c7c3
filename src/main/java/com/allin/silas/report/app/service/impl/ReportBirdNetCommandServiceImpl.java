package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.report.adapter.dto.AddReportBirdNetInfoDto;
import com.allin.silas.report.app.entity.ReportBirdNetInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.service.ReportBirdNetInfoCommandService;
import com.allin.silas.report.infra.repository.ReportBirdNetInfoMapper;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportBirdNetCommandServiceImpl implements ReportBirdNetInfoCommandService {
    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportBirdNetInfoMapper reportBirdNetInfoMapper;

    public ReportBirdNetCommandServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                           ReportBirdNetInfoMapper reportBirdNetInfoMapper) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportBirdNetInfoMapper = reportBirdNetInfoMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AddReportBirdNetInfoDto dto) {
        ReportBirdNetInfo reportBirdInfo = BeanUtil.copyProperties(dto, ReportBirdNetInfo.class);
        reportBirdNetInfoMapper.insert(reportBirdInfo);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.BIRD_NET, reportBirdInfo.getId(), dto.getDiscoverUserIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<AddReportBirdNetInfoDto> dtoList) {
        List<ReportBirdNetInfo> list = dtoList.stream().map(dto -> BeanUtil.copyProperties(dto, ReportBirdNetInfo.class)).toList();
        reportBirdNetInfoMapper.insert(list);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.BIRD_NET, list.stream().collect(Collectors.toMap(ReportBirdNetInfo::getId, ReportBirdNetInfo::getDiscoverUserIds)));
    }
}
