package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.report.adapter.dto.AddReportPlantInfoDto;
import com.allin.silas.report.app.entity.ReportPlantInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.service.ReportPlantInfoCommandService;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import com.allin.silas.report.infra.repository.ReportPlantInfoMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportPlantInfoCommandServiceImpl implements ReportPlantInfoCommandService {
    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportPlantInfoMapper reportPlantInfoMapper;

    public ReportPlantInfoCommandServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                             ReportPlantInfoMapper reportPlantInfoMapper) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportPlantInfoMapper = reportPlantInfoMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AddReportPlantInfoDto dto) {
        ReportPlantInfo reportInsectInfo = BeanUtil.copyProperties(dto, ReportPlantInfo.class);
        reportPlantInfoMapper.insert(reportInsectInfo);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.PLANT, reportInsectInfo.getId(), dto.getDiscoverUserIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<AddReportPlantInfoDto> dtoList) {
        List<ReportPlantInfo> list = dtoList.stream().map(dto -> BeanUtil.copyProperties(dto, ReportPlantInfo.class)).toList();
        reportPlantInfoMapper.insert(list);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.PLANT, list.stream().collect(Collectors.toMap(ReportPlantInfo::getId, ReportPlantInfo::getDiscoverUserIds)));
    }
}
