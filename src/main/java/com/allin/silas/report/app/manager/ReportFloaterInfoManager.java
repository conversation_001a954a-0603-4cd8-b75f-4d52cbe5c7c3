package com.allin.silas.report.app.manager;

import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import com.allin.silas.report.app.entity.ReportFloaterInfo;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
public interface ReportFloaterInfoManager {
    /**
     * 推送入侵处置事件
     */
    void pushInvasionEvent(ReportFloaterInfo reportFloaterInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion);

    /**
     * 推送入侵处置事件
     */
    void pushInvasionEvent(Map<ReportFloaterInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto> map);
}
