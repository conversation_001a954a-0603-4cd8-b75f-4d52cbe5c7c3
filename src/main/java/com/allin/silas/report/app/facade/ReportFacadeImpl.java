package com.allin.silas.report.app.facade;

import com.allin.silas.report.app.entity.ReportBirdInfo;
import com.allin.silas.report.app.entity.ReportDroneInfo;
import com.allin.silas.report.app.entity.ReportFloaterInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.client.ReportFacade;
import com.allin.silas.report.infra.repository.ReportBirdInfoMapper;
import com.allin.silas.report.infra.repository.ReportDroneInfoMapper;
import com.allin.silas.report.infra.repository.ReportFloaterInfoMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2025/7/8
 **/
@Service
public class ReportFacadeImpl implements ReportFacade {

    private static final Map<ReportDiscoverPersonReportTypeEnums, Function<String, Boolean>> UPDATE_MAP = new EnumMap<>(ReportDiscoverPersonReportTypeEnums.class);

    public ReportFacadeImpl(ReportBirdInfoMapper reportBirdInfoMapper, ReportDroneInfoMapper reportDroneInfoMapper, ReportFloaterInfoMapper reportFloaterInfoMapper) {
        UPDATE_MAP.putAll(Map.of(
                ReportDiscoverPersonReportTypeEnums.BIRD, id -> reportBirdInfoMapper.update(
                        Wrappers.lambdaUpdate(ReportBirdInfo.class)
                                .eq(ReportBirdInfo::getId, id)
                                .set(ReportBirdInfo::getHandleStatus, 2)) > 0,
                ReportDiscoverPersonReportTypeEnums.DRONE, id -> reportDroneInfoMapper.update(
                        Wrappers.lambdaUpdate(ReportDroneInfo.class)
                                .eq(ReportDroneInfo::getId, id)
                                .set(ReportDroneInfo::getHandleStatus, 2)) > 0,
                ReportDiscoverPersonReportTypeEnums.FLOATER, id -> reportFloaterInfoMapper.update(
                        Wrappers.lambdaUpdate(ReportFloaterInfo.class)
                                .eq(ReportFloaterInfo::getId, id)
                                .set(ReportFloaterInfo::getHandleStatus, 2)) > 0));
    }

    @Override
    public boolean callbackUpHandleStatus(String reportId, ReportDiscoverPersonReportTypeEnums enums) {
        return Optional.ofNullable(UPDATE_MAP.get(enums))
                .map(func -> func.apply(reportId))
                .orElse(false);
    }
}
