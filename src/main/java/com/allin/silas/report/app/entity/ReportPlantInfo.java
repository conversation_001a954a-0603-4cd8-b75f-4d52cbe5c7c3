package com.allin.silas.report.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 信息上报-植物
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "report_plant_info")
public class ReportPlantInfo {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 发现时间
     */
    @TableField(value = "discover_time")
    private LocalDateTime discoverTime;

    /**
     * 发现区域
     */
    @TableField(value = "region_id")
    private String regionId;

    /**
     * 植物名称
     */
    @TableField(value = "plant_name")
    private String plantName;

    /**
     * 平均高度(厘米)
     */
    @TableField(value = "plant_avg_height")
    private Integer plantAvgHeight;

    /**
     * 生长速度(厘米/天)
     */
    @TableField(value = "plant_growth_speed")
    private Integer plantGrowthSpeed;

    /**
     * 分布面积(平方米)
     */
    @TableField(value = "distribution_area")
    private BigDecimal distributionArea;

    /**
     * 附件
     */
    @TableField(value = "attachment")
    private String attachment;

    @TableField(value = "remarks")
    private String remarks;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除,0-否,1-是
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;

    /**
     * 发现人员
     */
    @TableField(exist = false)
    private List<String> discoverUserIds;
}