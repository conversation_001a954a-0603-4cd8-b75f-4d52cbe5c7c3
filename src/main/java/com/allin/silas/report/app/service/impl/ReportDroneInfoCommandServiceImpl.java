package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.report.adapter.dto.AddReportDroneInfoDto;
import com.allin.silas.report.app.entity.ReportDroneInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.manager.ReportDroneInfoManager;
import com.allin.silas.report.app.service.ReportDroneInfoCommandService;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import com.allin.silas.report.infra.repository.ReportDroneInfoMapper;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportDroneInfoCommandServiceImpl implements ReportDroneInfoCommandService {
    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportDroneInfoMapper reportDroneInfoMapper;

    private final ReportDroneInfoManager reportDroneInfoManager;

    public ReportDroneInfoCommandServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                             ReportDroneInfoMapper reportDroneInfoMapper,
                                             ReportDroneInfoManager reportDroneInfoManager) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportDroneInfoMapper = reportDroneInfoMapper;
        this.reportDroneInfoManager = reportDroneInfoManager;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AddReportDroneInfoDto dto) {
        if ((dto.getHandleStatus() == 1 || dto.getHandleStatus() == 2)
            && (Objects.isNull(dto.getEventInvasion()) || CollectionUtils.isEmpty(dto.getEventInvasion().getHandlePersons()))) {
            throw new ValidationFailureException("需要处置时处置人员不允许为空");
        }

        ReportDroneInfo reportDroneInfo = BeanUtil.copyProperties(dto, ReportDroneInfo.class);
        reportDroneInfoMapper.insert(reportDroneInfo);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.DRONE, reportDroneInfo.getId(), dto.getDiscoverUserIds());

        // 推送入侵处置事件
        reportDroneInfoManager.pushInvasionEvent(reportDroneInfo, dto.getEventInvasion());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<AddReportDroneInfoDto> dtoList) {
        List<ReportDroneInfo> list = dtoList.stream().map(dto -> BeanUtil.copyProperties(dto, ReportDroneInfo.class)).toList();
        reportDroneInfoMapper.insert(list);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.DRONE, list.stream().collect(Collectors.toMap(ReportDroneInfo::getId, ReportDroneInfo::getDiscoverUserIds)));

        // 推送入侵处置事件
        reportDroneInfoManager.pushInvasionEvent(list.stream().filter(report -> report.getHandleStatus() != 0).collect(Collectors.toMap(report -> report, ReportDroneInfo::getEventInvasion)));
    }
}
