package com.allin.silas.report.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
public enum ReportHandleStatusEnums implements IEnums {

    NO_NEED(0, "不需要处置"),
    NEED_HANDLE(1, "待处置"),
    DISPOSED(2, "已处置"),
    ;

    private final Integer code;

    private final String desc;

    ReportHandleStatusEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
