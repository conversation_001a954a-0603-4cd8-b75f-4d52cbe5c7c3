package com.allin.silas.report.app.manager;

import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import com.allin.silas.report.app.entity.ReportDroneInfo;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
public interface ReportDroneInfoManager {
    /**
     * 推送入侵处置事件
     */
    void pushInvasionEvent(ReportDroneInfo reportDroneInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion);

    /**
     * 推送入侵处置事件
     */
    void pushInvasionEvent(Map<ReportDroneInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto> map);
}
