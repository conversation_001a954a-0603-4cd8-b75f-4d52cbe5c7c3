package com.allin.silas.report.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 信息上报发现人员表
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "report_discover_person")
public class ReportDiscoverPerson {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 0:鸟情 1:虫情 2:植物 3:网捕 4:无人机 5:空飘物
     */
    @TableField(value = "report_type")
    private Integer reportType;

    /**
     * 信息上报id
     */
    @TableField(value = "report_id")
    private String reportId;

    /**
     * 发现人员id
     */
    @TableField(value = "discover_user_id")
    private String discoverUserId;
}