package com.allin.silas.report.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 0:微型 1:轻型 2:小型 3:中型 4:大型
 *
 * <AUTHOR>
 * @since 2025/7/23
 **/
public enum ReportDroneTypeEnums implements IEnums {
    MICRO(0, "微型"),
    LIGHT(1, "轻型"),
    SMALL(2, "小型"),
    MEDIUM(3, "中型"),
    LARGE(4, "大型"),
    ;

    private final Integer code;

    private final String desc;

    ReportDroneTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
