package com.allin.silas.report.app.service;

import com.allin.silas.report.adapter.query.ReportPlantInfoQuery;
import com.allin.silas.report.adapter.vo.ReportPlantInfoVo;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
public interface ReportPlantInfoQueryService {
    /**
     * 分页查询
     */
    PageData<ReportPlantInfoVo> page(PageParam pageParam, ReportPlantInfoQuery query);

    /**
     * 详情
     */
    ReportPlantInfoVo info(String id);
}
