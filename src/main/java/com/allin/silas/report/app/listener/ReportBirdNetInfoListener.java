package com.allin.silas.report.app.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.util.ListUtils;
import com.allin.silas.kn.client.KnBirdInfoDangerLevelFacade;
import com.allin.silas.map.client.MapBirdNetFacade;
import com.allin.silas.report.adapter.dto.AddReportBirdNetInfoDto;
import com.allin.silas.report.adapter.dto.excel.ErrorImportReportBirdNetInfoDto;
import com.allin.silas.report.adapter.dto.excel.ImportReportBirdNetInfoDto;
import com.allin.silas.report.app.service.ReportBirdNetInfoCommandService;
import com.allin.view.auth.api.UserInfoQueryApi;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.pojo.entity.SysUser;
import com.allin.view.base.execl.listener.AbstractImportListener;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import jakarta.validation.ConstraintViolation;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@Slf4j
public class ReportBirdNetInfoListener extends AbstractImportListener<ImportReportBirdNetInfoDto, ErrorImportReportBirdNetInfoDto> {

    private final ReportBirdNetInfoCommandService commandService;

    private final KnBirdInfoDangerLevelFacade knBirdInfoDangerLevelFacade;

    // 捕鸟网Map
    Map<String, String> mapBirdNet;

    // 用户Map
    Map<String, String> userMap;

    /**
     * 新增列表
     */
    private List<AddReportBirdNetInfoDto> addList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    public ReportBirdNetInfoListener(ReportBirdNetInfoCommandService commandService,
                                     MapBirdNetFacade mapBirdNetFacade,
                                     UserInfoQueryApi userInfoQueryApi,
                                     KnBirdInfoDangerLevelFacade knBirdInfoDangerLevelFacade) {
        this.commandService = commandService;
        this.knBirdInfoDangerLevelFacade = knBirdInfoDangerLevelFacade;
        // 填充区域Map
        mapBirdNet = mapBirdNetFacade.mapBirdNetIdByProjectId(SecurityContextHolder.getProjectId());
        // 填充用户Map
        List<SysUser> listResult = userInfoQueryApi.listByProjectId(SecurityContextHolder.getProjectId()).getData();
        userMap = CollectionUtils.isNotEmpty(listResult) ? listResult.stream().collect(Collectors.toMap(SysUser::getFullName, SysUser::getId)) : Collections.emptyMap();
    }

    @Override
    protected void invokeHandle(ImportReportBirdNetInfoDto data, AnalysisContext analysisContext) {
        if (!checkData(data)) {
            return;
        }

        // 所在捕鸟网
        String birdNetId = mapBirdNet.get(data.getBirdNetId());
        if (StringUtils.isBlank(birdNetId)) {
            addErrorData(data, "所在捕鸟网不存在");
            return;
        }

        // 鸟种名称
        int birdRiskLevel = knBirdInfoDangerLevelFacade.getDangerLevelCache(data.getBirdName());

        // 发现人员
        List<String> userIds = Arrays.stream(data.getDiscoverUserIds().split(StrPool.COMMA))
                .map(userMap::get)
                .filter(StringUtils::isNotBlank)
                .toList();
        if (CollectionUtils.isEmpty(userIds)) {
            addErrorData(data, "发现人员不存在");
            return;
        }

        AddReportBirdNetInfoDto dto = AddReportBirdNetInfoDto.builder()
                .birdNetId(birdNetId)
                .birdName(data.getBirdName())
                .birdRiskLevel(birdRiskLevel)
                .birdCount(data.getBirdCount())
                .remarks(data.getRemarks())
                .build();

        dto.setDiscoverTime(data.getDiscoverTime());
        dto.setDiscoverUserIds(userIds);

        addList.add(dto);

        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (addList.size() >= BATCH_COUNT) {
            defaultSaveData();
            // 存储完成清理 list
            addList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 参数验证
     */
    private boolean checkData(ImportReportBirdNetInfoDto data) {
        Set<ConstraintViolation<ImportReportBirdNetInfoDto>> violationSet = validator.validate(data);
        if (CollUtil.isEmpty(violationSet)) {
            return true;
        }

        addErrorData(data, violationSet
                .stream().map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; ")));
        return false;
    }

    @Override
    protected ErrorImportReportBirdNetInfoDto createErrorDataInstance() {
        return new ErrorImportReportBirdNetInfoDto();
    }

    @Override
    protected void saveData() {
        log.info("信息上报-网捕导入{}条数据，开始存储数据库！", addList.size());
        succTotal += addList.size();
        commandService.saveBatch(addList);
        log.info("存储数据库成功！");
    }
}
