package com.allin.silas.report.app.entity;

import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 信息上报-无人机
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "report_drone_info")
public class ReportDroneInfo {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 发现时间
     */
    @TableField(value = "discover_time")
    private LocalDateTime discoverTime;

    /**
     * 发现区域
     */
    @TableField(value = "region_id")
    private String regionId;

    /**
     * 经度
     */
    @TableField(value = "longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @TableField(value = "latitude")
    private Double latitude;

    /**
     * 飞行高度(真高)
     */
    @TableField(value = "height")
    private Integer height;

    /**
     * 是否进入管制空域 0:否 1:是
     */
    @TableField(value = "is_airspace_regulated")
    private Integer isAirspaceRegulated;

    /**
     * 无人机类型 0:微型 1:轻型 2:小型 3:中型 4:大型
     */
    @TableField(value = "drone_type")
    private Integer droneType;

    /**
     * 简要描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 附件
     */
    @TableField(value = "attachment")
    private String attachment;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @TableField(value = "handle_status")
    private Integer handleStatus;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除,0-否,1-是
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;

    /**
     * 发现人员
     */
    @TableField(exist = false)
    private List<String> discoverUserIds;

    /**
     * 入侵处置事件
     */
    @TableField(exist = false)
    private ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion;
}