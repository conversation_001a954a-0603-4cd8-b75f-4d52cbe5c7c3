package com.allin.silas.report.app.service;

import com.allin.silas.report.adapter.query.ReportBirdInfoQuery;
import com.allin.silas.report.adapter.vo.ReportBirdInfoVo;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
public interface ReportBirdInfoQueryService {
    /**
     * 分页查询
     */
    PageData<ReportBirdInfoVo> page(PageParam pageParam, ReportBirdInfoQuery query);

    /**
     * 统计各危险等级的鸟情数量
     */
    Map<Integer, Long> statisticsBirdRiskLevel(ReportBirdInfoQuery query);

    /**
     * 详情
     */
    ReportBirdInfoVo info(String id);
}
