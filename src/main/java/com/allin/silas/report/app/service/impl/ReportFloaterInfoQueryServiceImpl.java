package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.common.util.QueryWrapperUtils;
import com.allin.silas.report.adapter.query.ReportFloaterInfoQuery;
import com.allin.silas.report.adapter.vo.ReportFloaterInfoVo;
import com.allin.silas.report.app.entity.ReportFloaterInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.service.ReportFloaterInfoQueryService;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import com.allin.silas.report.infra.repository.ReportFloaterInfoMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportFloaterInfoQueryServiceImpl implements ReportFloaterInfoQueryService {

    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportFloaterInfoMapper reportFloaterInfoMapper;

    public ReportFloaterInfoQueryServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                             ReportFloaterInfoMapper reportFloaterInfoMapper) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportFloaterInfoMapper = reportFloaterInfoMapper;
    }

    @Override
    public PageData<ReportFloaterInfoVo> page(PageParam pageParam, ReportFloaterInfoQuery query) {
        final Page<ReportFloaterInfo> page = pageParam.toPage();

        LambdaQueryWrapper<ReportFloaterInfo> wrapper = Wrappers.lambdaQuery(ReportFloaterInfo.class)
                .eq(ReportFloaterInfo::getProjectId, query.getProjectId())
                .eq(ReportFloaterInfo::getIsDeleted, 0)
                .eq(Objects.nonNull(query.getHandleStatus()), ReportFloaterInfo::getHandleStatus, query.getHandleStatus())
                .eq(StringUtils.isNotBlank(query.getRegionId()), ReportFloaterInfo::getRegionId, query.getRegionId())
                .eq(Objects.nonNull(query.getFloaterType()), ReportFloaterInfo::getFloaterType, query.getFloaterType())
                .like(StringUtils.isNotBlank(query.getDescription()), ReportFloaterInfo::getDescription, query.getDescription())
                .orderByDesc(ReportFloaterInfo::getDiscoverTime);

        QueryWrapperUtils.buildDateRangeQuery(wrapper, query.getDiscoverStartTime(), query.getDiscoverEndTime(), ReportFloaterInfo::getDiscoverTime);
        reportFloaterInfoMapper.selectPage(page, wrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageData.empty();
        }

        Map<String, List<String>> map = reportDiscoverPersonMapper.map(ReportDiscoverPersonReportTypeEnums.FLOATER, page.getRecords().stream().map(ReportFloaterInfo::getId).toList());
        return PageData.getInstance(page, page.getRecords().stream().map(reportFloaterInfo -> {
            ReportFloaterInfoVo vo = BeanUtil.copyProperties(reportFloaterInfo, ReportFloaterInfoVo.class);
            vo.setDiscoverUserIds(map.getOrDefault(reportFloaterInfo.getId(), Collections.emptyList()));
            return vo;
        }).toList());
    }

    @Override
    public ReportFloaterInfoVo info(String id) {
        ReportFloaterInfo info = reportFloaterInfoMapper.selectOne(Wrappers.lambdaQuery(ReportFloaterInfo.class)
                .eq(ReportFloaterInfo::getId, id)
                .eq(ReportFloaterInfo::getProjectId, SecurityContextHolder.getProjectId()));
        if (Objects.isNull(info)) {
            return null;
        }

        ReportFloaterInfoVo vo = BeanUtil.copyProperties(info, ReportFloaterInfoVo.class);
        vo.setDiscoverUserIds(reportDiscoverPersonMapper.list(ReportDiscoverPersonReportTypeEnums.FLOATER, id));
        return vo;
    }

}
