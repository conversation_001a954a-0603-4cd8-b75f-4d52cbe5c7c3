package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.report.adapter.dto.AddReportBirdInfoDto;
import com.allin.silas.report.app.entity.ReportBirdInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.manager.ReportBirdInfoManager;
import com.allin.silas.report.app.service.ReportBirdInfoCommandService;
import com.allin.silas.report.infra.repository.ReportBirdInfoMapper;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportBirdInfoCommandServiceImpl implements ReportBirdInfoCommandService {

    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportBirdInfoMapper reportBirdInfoMapper;

    private final ReportBirdInfoManager reportBirdInfoManager;

    public ReportBirdInfoCommandServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                            ReportBirdInfoMapper reportBirdInfoMapper,
                                            ReportBirdInfoManager reportBirdInfoManager) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportBirdInfoMapper = reportBirdInfoMapper;
        this.reportBirdInfoManager = reportBirdInfoManager;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AddReportBirdInfoDto dto) {
        if ((dto.getHandleStatus() == 1 || dto.getHandleStatus() == 2)
            && (Objects.isNull(dto.getEventInvasion()) || CollectionUtils.isEmpty(dto.getEventInvasion().getHandlePersons()))) {
            throw new ValidationFailureException("需要处置时处置人员不允许为空");
        }

        ReportBirdInfo reportBirdInfo = BeanUtil.copyProperties(dto, ReportBirdInfo.class);
        reportBirdInfoMapper.insert(reportBirdInfo);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.BIRD, reportBirdInfo.getId(), dto.getDiscoverUserIds());

        // 推送入侵处置事件
        reportBirdInfoManager.pushInvasionEvent(reportBirdInfo, dto.getEventInvasion());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<AddReportBirdInfoDto> dtoList) {
        List<ReportBirdInfo> list = dtoList.stream().map(dto -> BeanUtil.copyProperties(dto, ReportBirdInfo.class)).toList();
        reportBirdInfoMapper.insert(list);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.BIRD, list.stream().collect(Collectors.toMap(ReportBirdInfo::getId, ReportBirdInfo::getDiscoverUserIds)));

        // 推送入侵处置事件
        reportBirdInfoManager.pushInvasionEvent(list.stream().filter(report -> report.getHandleStatus() != 0).collect(Collectors.toMap(report -> report, ReportBirdInfo::getEventInvasion)));
    }
}
