package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.common.util.QueryWrapperUtils;
import com.allin.silas.report.adapter.query.ReportBirdInfoQuery;
import com.allin.silas.report.adapter.vo.ReportBirdInfoVo;
import com.allin.silas.report.app.entity.ReportBirdInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.service.ReportBirdInfoQueryService;
import com.allin.silas.report.infra.repository.ReportBirdInfoMapper;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportBirdInfoQueryServiceImpl implements ReportBirdInfoQueryService {

    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportBirdInfoMapper reportBirdInfoMapper;

    public ReportBirdInfoQueryServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                          ReportBirdInfoMapper reportBirdInfoMapper) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportBirdInfoMapper = reportBirdInfoMapper;
    }

    @Override
    public PageData<ReportBirdInfoVo> page(PageParam pageParam, ReportBirdInfoQuery query) {
        final Page<ReportBirdInfo> page = pageParam.toPage();

        LambdaQueryWrapper<ReportBirdInfo> wrapper = getWrapper(query);
        QueryWrapperUtils.buildDateRangeQuery(wrapper, query.getDiscoverStartTime(), query.getDiscoverEndTime(), ReportBirdInfo::getDiscoverTime);
        reportBirdInfoMapper.selectPage(page, wrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageData.empty();
        }

        Map<String, List<String>> map = reportDiscoverPersonMapper.map(ReportDiscoverPersonReportTypeEnums.BIRD, page.getRecords().stream().map(ReportBirdInfo::getId).toList());
        return PageData.getInstance(page, page.getRecords().stream().map(reportBirdInfo -> {
            ReportBirdInfoVo vo = BeanUtil.copyProperties(reportBirdInfo, ReportBirdInfoVo.class);
            vo.setDiscoverUserIds(map.getOrDefault(reportBirdInfo.getId(), Collections.emptyList()));
            return vo;
        }).toList());
    }

    @Override
    public Map<Integer, Long> statisticsBirdRiskLevel(ReportBirdInfoQuery query) {
        LambdaQueryWrapper<ReportBirdInfo> wrapper = getWrapper(query);
        QueryWrapperUtils.buildDateRangeQuery(wrapper, query.getDiscoverStartTime(), query.getDiscoverEndTime(), ReportBirdInfo::getDiscoverTime);
        List<ReportBirdInfo> list = reportBirdInfoMapper.selectList(wrapper);
        return CollectionUtils.isEmpty(list) ? Collections.emptyMap() : list.stream().collect(Collectors.groupingBy(ReportBirdInfo::getBirdRiskLevel, Collectors.counting()));
    }

    private LambdaQueryWrapper<ReportBirdInfo> getWrapper(ReportBirdInfoQuery query) {
        return Wrappers.lambdaQuery(ReportBirdInfo.class)
                .eq(ReportBirdInfo::getProjectId, query.getProjectId())
                .eq(ReportBirdInfo::getIsDeleted, 0)
                .eq(Objects.nonNull(query.getHandleStatus()), ReportBirdInfo::getHandleStatus, query.getHandleStatus())
                .eq(StringUtils.isNotBlank(query.getRegionId()), ReportBirdInfo::getRegionId, query.getRegionId())
                .eq(StringUtils.isNotBlank(query.getBirdName()), ReportBirdInfo::getBirdName, query.getBirdName())
                .eq(Objects.nonNull(query.getBirdRiskLevel()), ReportBirdInfo::getBirdRiskLevel, query.getBirdRiskLevel())
                .like(StringUtils.isNotBlank(query.getBehavior()), ReportBirdInfo::getBehavior, query.getBehavior())
                .like(StringUtils.isNotBlank(query.getBehaviorReason()), ReportBirdInfo::getBehaviorReason, query.getBehaviorReason())
                .orderByDesc(ReportBirdInfo::getDiscoverTime);
    }

    @Override
    public ReportBirdInfoVo info(String id) {
        ReportBirdInfo info = reportBirdInfoMapper.selectOne(Wrappers.lambdaQuery(ReportBirdInfo.class)
                .eq(ReportBirdInfo::getId, id)
                .eq(ReportBirdInfo::getProjectId, SecurityContextHolder.getProjectId()));
        if (Objects.isNull(info)) {
            return null;
        }

        ReportBirdInfoVo vo = BeanUtil.copyProperties(info, ReportBirdInfoVo.class);
        vo.setDiscoverUserIds(reportDiscoverPersonMapper.list(ReportDiscoverPersonReportTypeEnums.BIRD, id));
        return vo;
    }
}
