package com.allin.silas.report.app.service;

import com.allin.silas.report.adapter.query.ReportDroneInfoQuery;
import com.allin.silas.report.adapter.vo.ReportDroneInfoVo;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
public interface ReportDroneInfoQueryService {
    /**
     * 分页查询
     */
    PageData<ReportDroneInfoVo> page(PageParam pageParam, ReportDroneInfoQuery query);
    /**
     * 详情
     */
    ReportDroneInfoVo info(String id);

    /**
     * 统计各无人机类型数量
     */
    Map<Integer, Long> statisticsDroneTypeCount(ReportDroneInfoQuery query);
}
