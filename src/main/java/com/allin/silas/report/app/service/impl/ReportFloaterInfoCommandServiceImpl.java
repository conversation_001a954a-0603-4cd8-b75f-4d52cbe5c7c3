package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.report.adapter.dto.AddReportFloaterInfoDto;
import com.allin.silas.report.app.entity.ReportFloaterInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.manager.ReportFloaterInfoManager;
import com.allin.silas.report.app.service.ReportFloaterInfoCommandService;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import com.allin.silas.report.infra.repository.ReportFloaterInfoMapper;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportFloaterInfoCommandServiceImpl implements ReportFloaterInfoCommandService {

    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportFloaterInfoMapper reportFloaterInfoMapper;

    private final ReportFloaterInfoManager reportFloaterInfoManager;

    public ReportFloaterInfoCommandServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                               ReportFloaterInfoMapper reportFloaterInfoMapper,
                                               ReportFloaterInfoManager reportFloaterInfoManager) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportFloaterInfoMapper = reportFloaterInfoMapper;
        this.reportFloaterInfoManager = reportFloaterInfoManager;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AddReportFloaterInfoDto dto) {
        if ((dto.getHandleStatus() == 1 || dto.getHandleStatus() == 2)
            && (Objects.isNull(dto.getEventInvasion()) || CollectionUtils.isEmpty(dto.getEventInvasion().getHandlePersons()))) {
            throw new ValidationFailureException("需要处置时处置人员不允许为空");
        }

        ReportFloaterInfo reportFloaterInfo = BeanUtil.copyProperties(dto, ReportFloaterInfo.class);
        reportFloaterInfoMapper.insert(reportFloaterInfo);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.FLOATER, reportFloaterInfo.getId(), dto.getDiscoverUserIds());

        // 推送入侵处置事件
        reportFloaterInfoManager.pushInvasionEvent(reportFloaterInfo, dto.getEventInvasion());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<AddReportFloaterInfoDto> dtoList) {
        List<ReportFloaterInfo> list = dtoList.stream().map(dto -> BeanUtil.copyProperties(dto, ReportFloaterInfo.class)).toList();
        reportFloaterInfoMapper.insert(list);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.FLOATER, list.stream().collect(Collectors.toMap(ReportFloaterInfo::getId, ReportFloaterInfo::getDiscoverUserIds)));

        // 推送入侵处置事件
        reportFloaterInfoManager.pushInvasionEvent(list.stream().filter(report -> report.getHandleStatus() != 0).collect(Collectors.toMap(report -> report, ReportFloaterInfo::getEventInvasion)));
    }

}
