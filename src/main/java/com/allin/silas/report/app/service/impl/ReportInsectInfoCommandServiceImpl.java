package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.report.adapter.dto.AddReportInsectInfoDto;
import com.allin.silas.report.app.entity.ReportInsectInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.service.ReportInsectInfoCommandService;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import com.allin.silas.report.infra.repository.ReportInsectInfoMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportInsectInfoCommandServiceImpl implements ReportInsectInfoCommandService {

    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportInsectInfoMapper reportInsectInfoMapper;

    public ReportInsectInfoCommandServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                              ReportInsectInfoMapper reportInsectInfoMapper) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportInsectInfoMapper = reportInsectInfoMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AddReportInsectInfoDto dto) {
        ReportInsectInfo reportInsectInfo = BeanUtil.copyProperties(dto, ReportInsectInfo.class);
        reportInsectInfoMapper.insert(reportInsectInfo);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.INSECT, reportInsectInfo.getId(), dto.getDiscoverUserIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<AddReportInsectInfoDto> dtoList) {
        List<ReportInsectInfo> list = dtoList.stream().map(dto -> BeanUtil.copyProperties(dto, ReportInsectInfo.class)).toList();
        reportInsectInfoMapper.insert(list);

        // 发现人员新增
        reportDiscoverPersonMapper.saveBatch(ReportDiscoverPersonReportTypeEnums.INSECT, list.stream().collect(Collectors.toMap(ReportInsectInfo::getId, ReportInsectInfo::getDiscoverUserIds)));
    }
}
