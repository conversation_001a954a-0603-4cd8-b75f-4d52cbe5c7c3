package com.allin.silas.report.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
public enum ReportInsectLevelEnums implements IEnums {
    SMALL_AMOUNT(0, "少量（1-100只）"),
    MEDIUM_AMOUNT(1, "中量（100-1000只）"),
    BIG_AMOUNT(2, "大量（＞1000只）"),
    ;

    private final Integer code;

    private final String desc;

    ReportInsectLevelEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
