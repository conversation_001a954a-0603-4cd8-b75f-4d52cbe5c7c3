package com.allin.silas.report.app.manager;

import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import com.allin.silas.report.app.entity.ReportBirdInfo;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
public interface ReportBirdInfoManager {
    /**
     * 推送入侵处置事件
     */
    void pushInvasionEvent(ReportBirdInfo reportBirdInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion);

    /**
     * 推送入侵处置事件
     */
    void pushInvasionEvent(Map<ReportBirdInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto> map);
}
