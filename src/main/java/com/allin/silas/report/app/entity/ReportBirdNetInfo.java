package com.allin.silas.report.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 信息上报-网捕
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "report_bird_net_info")
public class ReportBirdNetInfo {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 发现时间
     */
    @TableField(value = "discover_time")
    private LocalDateTime discoverTime;

    /**
     * 所在捕鸟网
     */
    @TableField(value = "bird_net_id")
    private String birdNetId;

    /**
     * 鸟种名称
     */
    @TableField(value = "bird_name")
    private String birdName;

    /**
     * 鸟种危险等级
     */
    @TableField(value = "bird_risk_level")
    private Integer birdRiskLevel;

    /**
     * 鸟种数量
     */
    @TableField(value = "bird_count")
    private Integer birdCount;

    /**
     * 附件
     */
    @TableField(value = "attachment")
    private String attachment;

    @TableField(value = "remarks")
    private String remarks;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除,0-否,1-是
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;

    /**
     * 发现人员
     */
    @TableField(exist = false)
    private List<String> discoverUserIds;
}