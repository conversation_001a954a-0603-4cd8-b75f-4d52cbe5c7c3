package com.allin.silas.report.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 1:气球（不包括系留气球、无人驾驶自由气球和探空气球等）2:风筝 3:孔明灯 4:无人机 5:塑料袋 6:焚烧产生的升空烟雾 7:其他
 *
 * <AUTHOR>
 * @since 2025/7/23
 **/
public enum ReportFloaterTypeEnums implements IEnums {
    BALLOON(1, "气球（不包括系留气球、无人驾驶自由气球和探空气球等）"),
    KITE(2, "风筝"),
    SKY_LANTERN(3, "孔明灯"),
    DRONE(4, "无人机"),
    PLASTIC_BAG(5, "塑料袋"),
    SMOKE(6, "焚烧产生的升空烟雾"),
    OTHER(7, "其他"),
    ;

    private final Integer code;

    private final String desc;

    ReportFloaterTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
