package com.allin.silas.report.app.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.util.ListUtils;
import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import com.allin.silas.map.client.MapRegionFacade;
import com.allin.silas.report.adapter.dto.AddReportDroneInfoDto;
import com.allin.silas.report.adapter.dto.excel.ErrorImportReportDroneInfoDto;
import com.allin.silas.report.adapter.dto.excel.ImportReportDroneInfoDto;
import com.allin.silas.report.app.enums.ReportDroneTypeEnums;
import com.allin.silas.report.app.enums.ReportHandleStatusEnums;
import com.allin.silas.report.app.service.ReportDroneInfoCommandService;
import com.allin.view.auth.api.UserInfoQueryApi;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.pojo.entity.SysUser;
import com.allin.view.base.execl.listener.AbstractImportListener;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import jakarta.validation.ConstraintViolation;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@Slf4j
public class ReportDroneInfoListener extends AbstractImportListener<ImportReportDroneInfoDto, ErrorImportReportDroneInfoDto> {

    private final ReportDroneInfoCommandService commandService;

    /**
     * 区域Map
     */
    private final Map<String, String> mapRegion;

    /**
     * 用户Map
     */
    private final Map<String, String> userMap;

    /**
     * 新增列表
     */
    private List<AddReportDroneInfoDto> addList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    public ReportDroneInfoListener(ReportDroneInfoCommandService commandService,
                                   MapRegionFacade mapRegionFacade,
                                   UserInfoQueryApi userInfoQueryApi) {
        this.commandService = commandService;
        // 填充区域Map
        mapRegion = mapRegionFacade.mapRegionIdByProjectId(SecurityContextHolder.getProjectId());
        // 填充用户Map
        List<SysUser> listResult = userInfoQueryApi.listByProjectId(SecurityContextHolder.getProjectId()).getData();
        userMap = CollectionUtils.isNotEmpty(listResult) ? listResult.stream().collect(Collectors.toMap(SysUser::getFullName, SysUser::getId)) : Collections.emptyMap();
    }

    @Override
    protected void invokeHandle(ImportReportDroneInfoDto data, AnalysisContext analysisContext) {
        if (!checkData(data)) {
            return;
        }

        // 发现区域
        String regionId = mapRegion.get(data.getRegionId());
        if (StringUtils.isBlank(regionId)) {
            addErrorData(data, "发现区域不存在");
            return;
        }

        // 发现人员
        List<String> userIds = Arrays.stream(data.getDiscoverUserIds().split(StrPool.COMMA))
                .map(userMap::get)
                .filter(StringUtils::isNotBlank)
                .toList();
        if (CollectionUtils.isEmpty(userIds)) {
            addErrorData(data, "发现人员不存在");
            return;
        }

        // 是否进入管制空域
        int isAirspaceRegulated = Objects.equals("是", data.getIsAirspaceRegulated()) ? 0 : 1;

        // 无人机类型
        Integer droneType = EnumUtil.getFieldBy(ReportDroneTypeEnums::getCode, ReportDroneTypeEnums::getDesc, data.getDroneType());
        if (Objects.isNull(droneType)) {
            addErrorData(data, "无人机类型值不正确");
            return;
        }

        // 是否需要处置
        Integer handleStatus = EnumUtil.getFieldBy(ReportHandleStatusEnums::getCode, ReportHandleStatusEnums::getDesc, data.getHandleStatus());
        if (Objects.isNull(handleStatus)) {
            addErrorData(data, "是否需要处置值不正确");
            return;
        }

        AddReportDroneInfoDto dto = AddReportDroneInfoDto.builder()
                .regionId(regionId)
                .longitude(data.getLongitude())
                .latitude(data.getLatitude())
                .height(data.getHeight())
                .isAirspaceRegulated(isAirspaceRegulated)
                .droneType(droneType)
                .description(data.getDescription())
                .handleStatus(handleStatus)
                .build();

        dto.setDiscoverTime(data.getDiscoverTime());
        dto.setDiscoverUserIds(userIds);

        // 处置人员
        List<String> handleUserIds = StringUtils.isNotBlank(data.getHandleUserIds()) ? Arrays.stream(data.getHandleUserIds().split(StrPool.COMMA))
                .map(userMap::get)
                .filter(StringUtils::isNotBlank)
                .toList() : Collections.emptyList();

        if ((handleStatus == 1 || handleStatus == 2) && CollectionUtils.isEmpty(handleUserIds)) {
            addErrorData(data, "需要处置时处置人员不允许为空");
            return;
        }

        // 是否影响航班
        int affectFlight = Objects.equals("未影响", data.getAffectFlight()) ? 0 : 1;

        // 是否通报空管
        int notifyAtc = Objects.equals("未通报", data.getNotifyAtc()) ? 0 : 1;

        // 是否通报公安机关
        int notifyPolice = Objects.equals("未通报", data.getNotifyPolice()) ? 0 : 1;

        dto.setEventInvasion(ReportPushEventInvasionDto.ReportHandleEventInvasionDto.builder()
                .handleTime(data.getHandleTime())
                .handlePersons(handleUserIds)
                .handleMethod(StrUtil.split(data.getHandleMethod(), StrPool.COMMA))
                .handleResult(StrUtil.split(data.getHandleResult(), StrPool.COMMA))
                .shotgunAmmoCount(data.getShotgunAmmoCount())
                .thunderAmmoCount(data.getThunderAmmoCount())
                .affectFlight(affectFlight)
                .notifyAtc(notifyAtc)
                .notifyPolice(notifyPolice)
                .build());

        addList.add(dto);

        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (addList.size() >= BATCH_COUNT) {
            defaultSaveData();
            // 存储完成清理 list
            addList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 参数验证
     */
    private boolean checkData(ImportReportDroneInfoDto data) {
        Set<ConstraintViolation<ImportReportDroneInfoDto>> violationSet = validator.validate(data);
        if (CollUtil.isEmpty(violationSet)) {
            return true;
        }

        addErrorData(data, violationSet
                .stream().map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; ")));
        return false;
    }

    @Override
    protected ErrorImportReportDroneInfoDto createErrorDataInstance() {
        return new ErrorImportReportDroneInfoDto();
    }

    @Override
    protected void saveData() {
        log.info("信息上报-无人机导入{}条数据，开始存储数据库！", addList.size());
        succTotal += addList.size();
        commandService.saveBatch(addList);
        log.info("存储数据库成功！");
    }
}
