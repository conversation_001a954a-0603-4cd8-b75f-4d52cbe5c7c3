package com.allin.silas.report.app.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.util.ListUtils;
import com.allin.silas.map.client.MapRegionFacade;
import com.allin.silas.report.adapter.dto.AddReportPlantInfoDto;
import com.allin.silas.report.adapter.dto.excel.ErrorImportReportPlantInfoDto;
import com.allin.silas.report.adapter.dto.excel.ImportReportPlantInfoDto;
import com.allin.silas.report.app.service.ReportPlantInfoCommandService;
import com.allin.view.auth.api.UserInfoQueryApi;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.pojo.entity.SysUser;
import com.allin.view.base.execl.listener.AbstractImportListener;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import jakarta.validation.ConstraintViolation;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@Slf4j
public class ReportPlantInfoListener extends AbstractImportListener<ImportReportPlantInfoDto, ErrorImportReportPlantInfoDto> {

    private final ReportPlantInfoCommandService commandService;

    /**
     * 区域Map
     */
    private final Map<String, String> mapRegion;

    /**
     * 用户Map
     */
    private final Map<String, String> userMap;


    /**
     * 新增列表
     */
    private List<AddReportPlantInfoDto> addList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    public ReportPlantInfoListener(ReportPlantInfoCommandService commandService,
                                   UserInfoQueryApi userInfoQueryApi,
                                   MapRegionFacade mapRegionFacade) {
        this.commandService = commandService;
        // 填充区域Map
        mapRegion = mapRegionFacade.mapRegionIdByProjectId(SecurityContextHolder.getProjectId());
        // 填充用户Map
        List<SysUser> listResult = userInfoQueryApi.listByProjectId(SecurityContextHolder.getProjectId()).getData();
        userMap = CollectionUtils.isNotEmpty(listResult) ? listResult.stream().collect(Collectors.toMap(SysUser::getFullName, SysUser::getId)) : Collections.emptyMap();
    }

    @Override
    protected void invokeHandle(ImportReportPlantInfoDto data, AnalysisContext analysisContext) {
        if (!checkData(data)) {
            return;
        }

        // 发现区域
        String regionId = mapRegion.get(data.getRegionId());
        if (StringUtils.isBlank(regionId)) {
            addErrorData(data, "发现区域不存在");
            return;
        }

        // 发现人员
        List<String> userIds = Arrays.stream(data.getDiscoverUserIds().split(StrPool.COMMA))
                .map(userMap::get)
                .filter(StringUtils::isNotBlank)
                .toList();
        if (CollectionUtils.isEmpty(userIds)) {
            addErrorData(data, "发现人员不存在");
            return;
        }


        AddReportPlantInfoDto dto = AddReportPlantInfoDto.builder()
                .regionId(regionId)
                .plantName(data.getPlantName())
                .plantAvgHeight(data.getPlantAvgHeight())
                .plantGrowthSpeed(data.getPlantGrowthSpeed())
                .distributionArea(data.getDistributionArea())
                .remarks(data.getRemarks())
                .build();

        dto.setDiscoverTime(data.getDiscoverTime());
        dto.setDiscoverUserIds(userIds);

        addList.add(dto);

        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (addList.size() >= BATCH_COUNT) {
            defaultSaveData();
            // 存储完成清理 list
            addList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 参数验证
     */
    private boolean checkData(ImportReportPlantInfoDto data) {
        Set<ConstraintViolation<ImportReportPlantInfoDto>> violationSet = validator.validate(data);
        if (CollUtil.isEmpty(violationSet)) {
            return true;
        }

        addErrorData(data, violationSet
                .stream().map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; ")));
        return false;
    }

    @Override
    protected ErrorImportReportPlantInfoDto createErrorDataInstance() {
        return new ErrorImportReportPlantInfoDto();
    }

    @Override
    protected void saveData() {
        log.info("信息上报-植物导入{}条数据，开始存储数据库！", addList.size());
        succTotal += addList.size();
        commandService.saveBatch(addList);
        log.info("存储数据库成功！");
    }
}
