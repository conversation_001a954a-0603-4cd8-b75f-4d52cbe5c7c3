package com.allin.silas.report.app.service;

import com.allin.silas.report.adapter.query.ReportBirdNetInfoQuery;
import com.allin.silas.report.adapter.vo.ReportBirdNetInfoVo;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
public interface ReportBirdNetInfoQueryService {
    /**
     * 分页查询
     */
    PageData<ReportBirdNetInfoVo> page(PageParam pageParam, ReportBirdNetInfoQuery query);

    /**
     * 详情
     */
    ReportBirdNetInfoVo info(String id);
}
