package com.allin.silas.report.app.manager.impl;

import com.allin.silas.event.client.EventInvasionFacade;
import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import com.allin.silas.event.client.enums.EventInvasionTypeEnums;
import com.allin.silas.report.app.entity.ReportDroneInfo;
import com.allin.silas.report.app.manager.ReportDroneInfoManager;
import com.allin.view.base.exception.service.ValidationFailureException;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
@Service
public class ReportDroneInfoManagerImpl implements ReportDroneInfoManager {

    private final EventInvasionFacade eventInvasionFacade;

    public ReportDroneInfoManagerImpl(EventInvasionFacade eventInvasionFacade) {
        this.eventInvasionFacade = eventInvasionFacade;
    }

    @Override
    public void pushInvasionEvent(ReportDroneInfo reportDroneInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion) {
        if (reportDroneInfo.getHandleStatus() == 0) {
            return;
        }

        boolean isOk = eventInvasionFacade.reportPushEvent(buildReportPushEventInvasion(reportDroneInfo, eventInvasion));
        if (!isOk) {
            // 推送失败
            throw new ValidationFailureException("入侵处置事件推送失败");
        }
    }

    @Override
    public void pushInvasionEvent(Map<ReportDroneInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto> map) {
        map.forEach((reportDroneInfo, eventInvasion) -> {
            boolean isOk = eventInvasionFacade.reportPushEvent(buildReportPushEventInvasion(reportDroneInfo, eventInvasion));
            if (!isOk) {
                // 推送失败
                throw new ValidationFailureException("入侵处置事件推送失败");
            }
        });
    }

    private ReportPushEventInvasionDto buildReportPushEventInvasion(ReportDroneInfo reportDroneInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion) {
        return ReportPushEventInvasionDto.builder()
                .sourceId(reportDroneInfo.getId())
                .eventType(EventInvasionTypeEnums.UAV.getCode())
                .discoverTime(reportDroneInfo.getDiscoverTime())
                .discoverRegionId(reportDroneInfo.getRegionId())
                .discoverLongitude(reportDroneInfo.getLongitude())
                .discoverLatitude(reportDroneInfo.getLatitude())
                .altitude(reportDroneInfo.getHeight())
                .dangerLevel(5)
                .handleEventInvasionDto(eventInvasion)
                .build();
    }

}
