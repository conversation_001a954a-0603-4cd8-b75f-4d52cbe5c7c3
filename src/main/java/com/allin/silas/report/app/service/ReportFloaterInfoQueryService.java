package com.allin.silas.report.app.service;

import com.allin.silas.report.adapter.query.ReportFloaterInfoQuery;
import com.allin.silas.report.adapter.vo.ReportFloaterInfoVo;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
public interface ReportFloaterInfoQueryService {
    /**
     * 分页查询
     */
    PageData<ReportFloaterInfoVo> page(PageParam pageParam, ReportFloaterInfoQuery query);

    /**
     * 详情
     */
    ReportFloaterInfoVo info(String id);
}
