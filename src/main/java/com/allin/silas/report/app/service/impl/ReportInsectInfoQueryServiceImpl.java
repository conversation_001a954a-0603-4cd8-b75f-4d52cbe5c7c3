package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.common.util.QueryWrapperUtils;
import com.allin.silas.report.adapter.query.ReportInsectInfoQuery;
import com.allin.silas.report.adapter.vo.ReportInsectInfoVo;
import com.allin.silas.report.app.entity.ReportInsectInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.service.ReportInsectInfoQueryService;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import com.allin.silas.report.infra.repository.ReportInsectInfoMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportInsectInfoQueryServiceImpl implements ReportInsectInfoQueryService {

    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportInsectInfoMapper reportInsectInfoMapper;

    public ReportInsectInfoQueryServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                            ReportInsectInfoMapper reportInsectInfoMapper) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportInsectInfoMapper = reportInsectInfoMapper;
    }

    @Override
    public PageData<ReportInsectInfoVo> page(PageParam pageParam, ReportInsectInfoQuery query) {
        final Page<ReportInsectInfo> page = pageParam.toPage();

        LambdaQueryWrapper<ReportInsectInfo> wrapper = Wrappers.lambdaQuery(ReportInsectInfo.class)
                .eq(ReportInsectInfo::getProjectId, query.getProjectId())
                .eq(ReportInsectInfo::getIsDeleted, 0)
                .eq(StringUtils.isNotBlank(query.getRegionId()), ReportInsectInfo::getRegionId, query.getRegionId())
                .eq(StringUtils.isNotBlank(query.getInsectName()), ReportInsectInfo::getInsectName, query.getInsectName())
                .eq(Objects.nonNull(query.getInsectLevel()), ReportInsectInfo::getInsectLevel, query.getInsectLevel())
                .orderByDesc(ReportInsectInfo::getDiscoverTime);

        QueryWrapperUtils.buildDateRangeQuery(wrapper, query.getDiscoverStartTime(), query.getDiscoverEndTime(), ReportInsectInfo::getDiscoverTime);
        reportInsectInfoMapper.selectPage(page, wrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageData.empty();
        }

        Map<String, List<String>> map = reportDiscoverPersonMapper.map(ReportDiscoverPersonReportTypeEnums.INSECT, page.getRecords().stream().map(ReportInsectInfo::getId).toList());
        return PageData.getInstance(page, page.getRecords().stream().map(reportBirdInfo -> {
            ReportInsectInfoVo vo = BeanUtil.copyProperties(reportBirdInfo, ReportInsectInfoVo.class);
            vo.setDiscoverUserIds(map.getOrDefault(reportBirdInfo.getId(), Collections.emptyList()));
            return vo;
        }).toList());
    }

    @Override
    public ReportInsectInfoVo info(String id) {
        ReportInsectInfo info = reportInsectInfoMapper.selectOne(Wrappers.lambdaQuery(ReportInsectInfo.class)
                .eq(ReportInsectInfo::getId, id)
                .eq(ReportInsectInfo::getProjectId, SecurityContextHolder.getProjectId()));
        if (Objects.isNull(info)) {
            return null;
        }

        ReportInsectInfoVo vo = BeanUtil.copyProperties(info, ReportInsectInfoVo.class);
        vo.setDiscoverUserIds(reportDiscoverPersonMapper.list(ReportDiscoverPersonReportTypeEnums.INSECT, id));
        return vo;
    }
}
