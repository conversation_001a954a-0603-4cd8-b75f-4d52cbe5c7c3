package com.allin.silas.report.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.common.util.QueryWrapperUtils;
import com.allin.silas.report.adapter.query.ReportPlantInfoQuery;
import com.allin.silas.report.adapter.vo.ReportPlantInfoVo;
import com.allin.silas.report.app.entity.ReportPlantInfo;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.app.service.ReportPlantInfoQueryService;
import com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper;
import com.allin.silas.report.infra.repository.ReportPlantInfoMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Service
public class ReportPlantInfoQueryServiceImpl implements ReportPlantInfoQueryService {
    private final ReportDiscoverPersonMapper reportDiscoverPersonMapper;

    private final ReportPlantInfoMapper reportPlantInfoMapper;

    public ReportPlantInfoQueryServiceImpl(ReportDiscoverPersonMapper reportDiscoverPersonMapper,
                                           ReportPlantInfoMapper reportPlantInfoMapper) {
        this.reportDiscoverPersonMapper = reportDiscoverPersonMapper;
        this.reportPlantInfoMapper = reportPlantInfoMapper;
    }

    @Override
    public PageData<ReportPlantInfoVo> page(PageParam pageParam, ReportPlantInfoQuery query) {
        final Page<ReportPlantInfo> page = pageParam.toPage();

        LambdaQueryWrapper<ReportPlantInfo> wrapper = Wrappers.lambdaQuery(ReportPlantInfo.class)
                .eq(ReportPlantInfo::getProjectId, query.getProjectId())
                .eq(ReportPlantInfo::getIsDeleted, 0)
                .eq(StringUtils.isNotBlank(query.getRegionId()), ReportPlantInfo::getRegionId, query.getRegionId())
                .eq(StringUtils.isNotBlank(query.getPlantName()), ReportPlantInfo::getPlantName, query.getPlantName())
                .orderByDesc(ReportPlantInfo::getDiscoverTime);

        QueryWrapperUtils.buildDateRangeQuery(wrapper, query.getDiscoverStartTime(), query.getDiscoverEndTime(), ReportPlantInfo::getDiscoverTime);
        reportPlantInfoMapper.selectPage(page, wrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageData.empty();
        }

        Map<String, List<String>> map = reportDiscoverPersonMapper.map(ReportDiscoverPersonReportTypeEnums.PLANT, page.getRecords().stream().map(ReportPlantInfo::getId).toList());
        return PageData.getInstance(page, page.getRecords().stream().map(reportBirdInfo -> {
            ReportPlantInfoVo vo = BeanUtil.copyProperties(reportBirdInfo, ReportPlantInfoVo.class);
            vo.setDiscoverUserIds(map.getOrDefault(reportBirdInfo.getId(), Collections.emptyList()));
            return vo;
        }).toList());
    }

    @Override
    public ReportPlantInfoVo info(String id) {
        ReportPlantInfo info = reportPlantInfoMapper.selectOne(Wrappers.lambdaQuery(ReportPlantInfo.class)
                .eq(ReportPlantInfo::getId, id)
                .eq(ReportPlantInfo::getProjectId, SecurityContextHolder.getProjectId()));
        if (Objects.isNull(info)) {
            return null;
        }

        ReportPlantInfoVo vo = BeanUtil.copyProperties(info, ReportPlantInfoVo.class);
        vo.setDiscoverUserIds(reportDiscoverPersonMapper.list(ReportDiscoverPersonReportTypeEnums.PLANT, id));
        return vo;
    }
}
