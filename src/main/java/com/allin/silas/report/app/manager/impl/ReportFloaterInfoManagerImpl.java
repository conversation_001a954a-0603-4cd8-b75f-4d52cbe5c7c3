package com.allin.silas.report.app.manager.impl;

import com.allin.silas.event.client.EventInvasionFacade;
import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import com.allin.silas.event.client.enums.EventInvasionTypeEnums;
import com.allin.silas.report.app.entity.ReportFloaterInfo;
import com.allin.silas.report.app.manager.ReportFloaterInfoManager;
import com.allin.view.base.exception.service.ValidationFailureException;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
@Service
public class ReportFloaterInfoManagerImpl implements ReportFloaterInfoManager {

    private final EventInvasionFacade eventInvasionFacade;

    public ReportFloaterInfoManagerImpl(EventInvasionFacade eventInvasionFacade) {
        this.eventInvasionFacade = eventInvasionFacade;
    }

    @Override
    public void pushInvasionEvent(ReportFloaterInfo reportFloaterInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion) {
        if (reportFloaterInfo.getHandleStatus() == 0) {
            return;
        }

        boolean isOk = eventInvasionFacade.reportPushEvent(buildReportPushEventInvasion(reportFloaterInfo, eventInvasion));
        if (!isOk) {
            // 推送失败
            throw new ValidationFailureException("入侵处置事件推送失败");
        }
    }

    @Override
    public void pushInvasionEvent(Map<ReportFloaterInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto> map) {
        map.forEach((reportFloaterInfo, eventInvasion) -> {
            boolean isOk = eventInvasionFacade.reportPushEvent(buildReportPushEventInvasion(reportFloaterInfo, eventInvasion));
            if (!isOk) {
                // 推送失败
                throw new ValidationFailureException("入侵处置事件推送失败");
            }
        });
    }

    private ReportPushEventInvasionDto buildReportPushEventInvasion(ReportFloaterInfo reportFloaterInfo, ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion) {
        return ReportPushEventInvasionDto.builder()
                .sourceId(reportFloaterInfo.getId())
                .eventType(EventInvasionTypeEnums.AIR_OBJECT.getCode())
                .discoverTime(reportFloaterInfo.getDiscoverTime())
                .discoverRegionId(reportFloaterInfo.getRegionId())
                .discoverLongitude(reportFloaterInfo.getLongitude())
                .discoverLatitude(reportFloaterInfo.getLatitude())
                .altitude(reportFloaterInfo.getHeight())
                .dangerLevel(5)
                .handleEventInvasionDto(eventInvasion)
                .build();
    }
}
