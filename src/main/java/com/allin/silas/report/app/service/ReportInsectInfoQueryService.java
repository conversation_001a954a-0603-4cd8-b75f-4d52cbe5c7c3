package com.allin.silas.report.app.service;

import com.allin.silas.report.adapter.query.ReportInsectInfoQuery;
import com.allin.silas.report.adapter.vo.ReportInsectInfoVo;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
public interface ReportInsectInfoQueryService {
    /**
     * 分页查询
     */
    PageData<ReportInsectInfoVo> page(PageParam pageParam, ReportInsectInfoQuery query);

    /**
     * 详情
     */
    ReportInsectInfoVo info(String id);
}
