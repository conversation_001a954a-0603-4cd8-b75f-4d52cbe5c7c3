package com.allin.silas.report.app.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.EnumUtil;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.util.ListUtils;
import com.allin.silas.map.client.MapRegionFacade;
import com.allin.silas.report.adapter.dto.AddReportInsectInfoDto;
import com.allin.silas.report.adapter.dto.excel.ErrorImportReportInsectInfoDto;
import com.allin.silas.report.adapter.dto.excel.ImportReportInsectInfoDto;
import com.allin.silas.report.app.enums.ReportInsectLevelEnums;
import com.allin.silas.report.app.service.ReportInsectInfoCommandService;
import com.allin.view.auth.api.UserInfoQueryApi;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.pojo.entity.SysUser;
import com.allin.view.base.execl.listener.AbstractImportListener;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import jakarta.validation.ConstraintViolation;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@Slf4j
public class ReportInsectInfoListener extends AbstractImportListener<ImportReportInsectInfoDto, ErrorImportReportInsectInfoDto> {

    private final ReportInsectInfoCommandService commandService;

    /**
     * 区域Map
     */
    private final Map<String, String> mapRegion;

    /**
     * 用户Map
     */
    private final Map<String, String> userMap;

    /**
     * 新增列表
     */
    private List<AddReportInsectInfoDto> addList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    public ReportInsectInfoListener(ReportInsectInfoCommandService commandService,
                                    UserInfoQueryApi userInfoQueryApi,
                                    MapRegionFacade mapRegionFacade) {
        this.commandService = commandService;
        // 填充区域Map
        mapRegion = mapRegionFacade.mapRegionIdByProjectId(SecurityContextHolder.getProjectId());
        // 填充用户Map
        List<SysUser> listResult = userInfoQueryApi.listByProjectId(SecurityContextHolder.getProjectId()).getData();
        userMap = CollectionUtils.isNotEmpty(listResult) ? listResult.stream().collect(Collectors.toMap(SysUser::getFullName, SysUser::getId)) : Collections.emptyMap();
    }

    @Override
    protected void invokeHandle(ImportReportInsectInfoDto data, AnalysisContext analysisContext) {
        if (!checkData(data)) {
            return;
        }

        // 发现区域
        String regionId = mapRegion.get(data.getRegionId());
        if (StringUtils.isBlank(regionId)) {
            addErrorData(data, "发现区域不存在");
            return;
        }

        // 发现人员
        List<String> userIds = Arrays.stream(data.getDiscoverUserIds().split(StrPool.COMMA))
                .map(userMap::get)
                .filter(StringUtils::isNotBlank)
                .toList();
        if (CollectionUtils.isEmpty(userIds)) {
            addErrorData(data, "发现人员不存在");
            return;
        }

        // 虫类量级
        Integer insectLevel = EnumUtil.getFieldBy(ReportInsectLevelEnums::getCode, ReportInsectLevelEnums::getDesc, data.getInsectLevel());
        if (Objects.isNull(insectLevel)) {
            addErrorData(data, "虫类量级值不正确");
            return;
        }

        AddReportInsectInfoDto dto = AddReportInsectInfoDto.builder()
                .regionId(regionId)
                .insectName(data.getInsectName())
                .insectLevel(insectLevel)
                .insectCount(data.getInsectCount())
                .distributionArea(data.getDistributionArea())
                .remarks(data.getRemarks())
                .build();

        dto.setDiscoverTime(data.getDiscoverTime());
        dto.setDiscoverUserIds(userIds);

        addList.add(dto);

        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (addList.size() >= BATCH_COUNT) {
            defaultSaveData();
            // 存储完成清理 list
            addList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 参数验证
     */
    private boolean checkData(ImportReportInsectInfoDto data) {
        Set<ConstraintViolation<ImportReportInsectInfoDto>> violationSet = validator.validate(data);
        if (CollUtil.isEmpty(violationSet)) {
            return true;
        }

        addErrorData(data, violationSet
                .stream().map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; ")));
        return false;
    }

    @Override
    protected ErrorImportReportInsectInfoDto createErrorDataInstance() {
        return new ErrorImportReportInsectInfoDto();
    }

    @Override
    protected void saveData() {
        log.info("信息上报-虫情导入{}条数据，开始存储数据库！", addList.size());
        succTotal += addList.size();
        commandService.saveBatch(addList);
        log.info("存储数据库成功！");
    }
}
