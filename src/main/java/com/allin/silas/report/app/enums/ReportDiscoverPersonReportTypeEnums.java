package com.allin.silas.report.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 信息上报发现人员报告类型
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
public enum ReportDiscoverPersonReportTypeEnums implements IEnums {

    BIRD(0, "鸟情"),
    INSECT(1, "虫情"),
    PLANT(2, "植物"),
    BIRD_NET(3, "网捕"),
    DRONE(4, "无人机"),
    FLOATER(5, "空飘物"),
    ;

    private final Integer code;

    private final String desc;

    ReportDiscoverPersonReportTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
