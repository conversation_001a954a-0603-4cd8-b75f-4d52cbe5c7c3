package com.allin.silas.report.adapter.controller;

import cn.hutool.extra.spring.SpringUtil;
import cn.idev.excel.EasyExcel;
import com.allin.silas.kn.client.KnBirdInfoDangerLevelFacade;
import com.allin.silas.map.client.MapRegionFacade;
import com.allin.silas.report.adapter.dto.AddReportBirdInfoDto;
import com.allin.silas.report.adapter.dto.excel.ImportReportBirdInfoDto;
import com.allin.silas.report.adapter.query.ReportBirdInfoQuery;
import com.allin.silas.report.adapter.vo.ReportBirdInfoVo;
import com.allin.silas.report.app.listener.ReportBirdInfoListener;
import com.allin.silas.report.app.service.ReportBirdInfoCommandService;
import com.allin.silas.report.app.service.ReportBirdInfoQueryService;
import com.allin.view.auth.api.UserInfoQueryApi;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import com.allin.view.file.pojo.vo.ImportResultVo;
import com.allin.view.file.util.ImportExcelUtils;
import com.allin.view.log.annotation.Log;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Map;

/**
 * 作业管理/信息上报/鸟情
 *
 * <AUTHOR>
 * @since 2025/7/5
 **/
@RestController
@RequestMapping("/report/bird_info")
@Validated
public class ReportBirdInfoController {

    private final ReportBirdInfoCommandService reportBirdInfoCommandService;

    private final ReportBirdInfoQueryService reportBirdInfoQueryService;

    private final MapRegionFacade mapRegionFacade;

    private final KnBirdInfoDangerLevelFacade knBirdInfoDangerLevelFacade;

    private final UserInfoQueryApi userInfoQueryApi;

    public ReportBirdInfoController(ReportBirdInfoCommandService reportBirdInfoCommandService,
                                    ReportBirdInfoQueryService reportBirdInfoQueryService,
                                    MapRegionFacade mapRegionFacade,
                                    KnBirdInfoDangerLevelFacade knBirdInfoDangerLevelFacade) {
        this.reportBirdInfoCommandService = reportBirdInfoCommandService;
        this.reportBirdInfoQueryService = reportBirdInfoQueryService;
        this.mapRegionFacade = mapRegionFacade;
        this.knBirdInfoDangerLevelFacade = knBirdInfoDangerLevelFacade;
        this.userInfoQueryApi = SpringUtil.getBean(UserInfoQueryApi.class);
    }

    /**
     * 分页查询
     */
    @GetMapping
    public Result<PageData<ReportBirdInfoVo>> page(PageParam pageParam, @Valid ReportBirdInfoQuery query) {
        return Result.ok(reportBirdInfoQueryService.page(pageParam, query));
    }

    /**
     * 统计各危险等级的鸟情数量
     */
    @GetMapping("/statistics_bird_risk_level")
    public Result<Map<Integer, Long>> statisticsBirdRiskLevel(@Valid ReportBirdInfoQuery query) {
        return Result.ok(reportBirdInfoQueryService.statisticsBirdRiskLevel(query));
    }

    /**
     * 详情
     */
    @GetMapping("/{id}")
    public Result<ReportBirdInfoVo> info(@PathVariable String id) {
        return Result.ok(reportBirdInfoQueryService.info(id));
    }

    /**
     * 新增
     */
    @Log(title = "信息上报", operDesc = "新增鸟情")
    @PostMapping
    public Result<String> save(@RequestBody @Valid AddReportBirdInfoDto dto) {
        reportBirdInfoCommandService.save(dto);
        return Result.ok();
    }

    /**
     * 导出
     */
    @PostMapping("/export/excel")
    public void exportExcel(@Validated @RequestBody ReportBirdInfoQuery query, HttpServletResponse response) {
        FastExcelOperUtils.exportXlsx(response, reportBirdInfoQueryService.page(new PageParam(-1, -1), query).getRecords(), ReportBirdInfoVo.class, "信息上报鸟情信息");
    }

    /**
     * 导入
     */
    @Log(title = "信息上报", operDesc = "导入鸟情")
    @PostMapping("/import")
    public Result<ImportResultVo> importData(@NotNull(message = "文件不能为空") MultipartFile file) throws IOException {
        final ReportBirdInfoListener importListener = new ReportBirdInfoListener(reportBirdInfoCommandService, knBirdInfoDangerLevelFacade, userInfoQueryApi, mapRegionFacade);
        EasyExcel.read(file.getInputStream(), ImportReportBirdInfoDto.class, importListener).sheet().doRead();
        return Result.ok(ImportExcelUtils.getImportResultVo(importListener));
    }

}
