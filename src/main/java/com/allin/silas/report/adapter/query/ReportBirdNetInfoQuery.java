package com.allin.silas.report.adapter.query;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportBirdNetInfoQuery extends ReportBaseQuery {
    /**
     * 所在捕鸟网
     */
    private String birdNetId;

    /**
     * 鸟种名称
     */
    private String birdName;

    /**
     * 鸟种危险等级
     */
    private Integer birdRiskLevel;
}
