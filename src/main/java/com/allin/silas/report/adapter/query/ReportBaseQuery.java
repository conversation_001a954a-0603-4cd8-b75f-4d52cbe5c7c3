package com.allin.silas.report.adapter.query;

import com.allin.view.auth.context.SecurityContextHolder;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
public class ReportBaseQuery {
    /**
     * 发现开始时间
     */
    private LocalDateTime discoverStartTime;

    /**
     * 发现结束时间
     */
    private LocalDateTime discoverEndTime;

    /**
     * 项目id
     */
    @JsonIgnore
    private String projectId;

    public String getProjectId() {
        return SecurityContextHolder.getProjectId();
    }
}
