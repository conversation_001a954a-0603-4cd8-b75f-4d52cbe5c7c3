package com.allin.silas.report.adapter.query;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportFloaterInfoQuery extends ReportBaseQuery {
    /**
     * 发现区域
     */
    private String regionId;

    /**
     * 种类 1:气球（不包括系留气球、无人驾驶自由气球和探空气球等）2:风筝 3:孔明灯 4:无人机 5:塑料袋 6:焚烧产生的升空烟雾 7:其他
     */
    @Min(1)
    @Max(7)
    private Integer floaterType;

    /**
     * 简要描述
     */
    private String description;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @Min(0)
    @Max(2)
    private Integer handleStatus;
}
