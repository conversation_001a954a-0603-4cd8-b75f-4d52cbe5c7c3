package com.allin.silas.report.adapter.dto.excel;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.HeadFontStyle;
import com.allin.view.base.execl.listener.IErrorData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.poi.ss.usermodel.Font;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ErrorImportReportFloaterInfoDto extends ImportReportFloaterInfoDto implements IErrorData {
    /**
     * 错误原因
     * 字符串的头背景设置成红色
     */
    @HeadFontStyle(fontHeightInPoints = 12, color = Font.COLOR_RED)
    @ExcelProperty("错误原因")
    private String errorMsg;
}
