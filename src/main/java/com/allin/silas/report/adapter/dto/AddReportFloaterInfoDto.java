package com.allin.silas.report.adapter.dto;

import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddReportFloaterInfoDto extends ReportBaseDto {
    /**
     * 发现区域
     */
    @NotNull(message = "发现区域不能为空")
    private String regionId;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空")
    private Double longitude;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    private Double latitude;

    /**
     * 升空高度(米)
     */
    @NotNull(message = "升空高度(米)不能为空")
    private Integer height;

    /**
     * 种类 1:气球（不包括系留气球、无人驾驶自由气球和探空气球等）2:风筝 3:孔明灯 4:无人机 5:塑料袋 6:焚烧产生的升空烟雾 7:其他
     */
    @NotNull(message = "种类不能为空")
    @Min(1)
    @Max(7)
    private Integer floaterType;

    /**
     * 空飘物外观
     */
    private String floaterAppearance;

    /**
     * 可能来源
     */
    @NotBlank(message = "可能来源不能为空")
    private String source;

    /**
     * 简要描述
     */
    @NotBlank(message = "简要描述不能为空")
    private String description;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @NotNull(message = "是否需要处置状态不能为空")
    @Min(0)
    @Max(2)
    private Integer handleStatus;

    /**
     * 入侵处置事件
     */
    private ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion;
}
