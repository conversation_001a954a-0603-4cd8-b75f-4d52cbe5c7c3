package com.allin.silas.report.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddReportBirdNetInfoDto extends ReportBaseDto {
    /**
     * 所在捕鸟网
     */
    @NotBlank(message = "所在捕鸟网不能为空")
    private String birdNetId;

    /**
     * 鸟种名称
     */
    @NotBlank(message = "鸟种名称不能为空")
    private String birdName;

    /**
     * 鸟种危险等级
     */
    @NotNull(message = "鸟种危险等级不能为空")
    private Integer birdRiskLevel;

    /**
     * 鸟种数量
     */
    @NotNull(message = "鸟种数量不能为空")
    private Integer birdCount;

    /**
     * 备注
     */
    private String remarks;
}
