package com.allin.silas.report.adapter.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.allin.silas.map.client.annotation.MapRegionIdToName;
import com.allin.silas.map.client.excel.MapRegionIdToNameConverter;
import com.allin.view.auth.excel.converter.UserConverter;
import com.allin.view.auth.serializer.annotation.UserIdToFullName;
import com.allin.view.base.enums.serializer.IEnumsChangeDesc;
import com.allin.view.base.execl.converter.IEnumsToStrConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportBirdInfoVo extends ReportBaseVo {
    /**
     * 发现区域
     */
    @ExcelProperty(value = "发现区域", converter = MapRegionIdToNameConverter.class)
    @MapRegionIdToName(key = "regionName")
    private String regionId;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Double longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Double latitude;

    /**
     * 飞行高度(米)
     */
    @ExcelProperty(value = "飞行高度(米)")
    private Integer height;

    /**
     * 鸟种名称
     */
    @ExcelProperty(value = "鸟种名称")
    private String birdName;

    /**
     * 鸟种危险等级
     */
    @ExcelProperty(value = "鸟种危险等级")
    private Integer birdRiskLevel;

    /**
     * 鸟种数量
     */
    @ExcelProperty(value = "鸟种数量")
    private Integer birdCount;

    /**
     * 飞行路线
     */
    @ExcelProperty(value = "飞行路线")
    private String flightRoute;

    /**
     * 鸟类行为
     */
    @ExcelProperty(value = "鸟类行为")
    private String behavior;

    /**
     * 行为原因
     */
    @ExcelProperty(value = "行为原因")
    private String behaviorReason;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @ExcelProperty(value = "是否需要处置状态", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "handleStatusDesc", replace = {"0_不需要处置", "1_待处置", "2_已处置"})
    private Integer handleStatus;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", converter = UserConverter.class)
    @UserIdToFullName(key = "createdByFullName")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createdTime;
}
