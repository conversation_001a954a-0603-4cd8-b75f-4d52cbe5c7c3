package com.allin.silas.report.adapter.controller;

import cn.hutool.extra.spring.SpringUtil;
import cn.idev.excel.EasyExcel;
import com.allin.silas.map.client.MapRegionFacade;
import com.allin.silas.report.adapter.dto.AddReportPlantInfoDto;
import com.allin.silas.report.adapter.dto.excel.ImportReportPlantInfoDto;
import com.allin.silas.report.adapter.query.ReportPlantInfoQuery;
import com.allin.silas.report.adapter.vo.ReportPlantInfoVo;
import com.allin.silas.report.app.listener.ReportPlantInfoListener;
import com.allin.silas.report.app.service.ReportPlantInfoCommandService;
import com.allin.silas.report.app.service.ReportPlantInfoQueryService;
import com.allin.view.auth.api.UserInfoQueryApi;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import com.allin.view.file.pojo.vo.ImportResultVo;
import com.allin.view.file.util.ImportExcelUtils;
import com.allin.view.log.annotation.Log;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.IOException;

/**
 * 作业管理/信息上报/植物
 *
 * <AUTHOR>
 * @since 2025/7/5
 **/
@RestController
@RequestMapping("/report/plant_info")
@Validated
public class ReportPlantInfoController {

    private final ReportPlantInfoCommandService reportPlantInfoCommandService;

    private final ReportPlantInfoQueryService reportPlantInfoQueryService;

    private final UserInfoQueryApi userInfoQueryApi;

    private final MapRegionFacade mapRegionFacade;

    public ReportPlantInfoController(ReportPlantInfoCommandService reportPlantInfoCommandService,
                                     ReportPlantInfoQueryService reportPlantInfoQueryService,
                                     MapRegionFacade mapRegionFacade) {
        this.reportPlantInfoCommandService = reportPlantInfoCommandService;
        this.reportPlantInfoQueryService = reportPlantInfoQueryService;
        this.userInfoQueryApi = SpringUtil.getBean(UserInfoQueryApi.class);
        this.mapRegionFacade = mapRegionFacade;
    }

    /**
     * 分页查询
     */
    @GetMapping
    public Result<PageData<ReportPlantInfoVo>> page(PageParam pageParam, @Valid ReportPlantInfoQuery query) {
        return Result.ok(reportPlantInfoQueryService.page(pageParam, query));
    }

    /**
     * 详情
     */
    @GetMapping("/{id}")
    public Result<ReportPlantInfoVo> info(@PathVariable String id) {
        return Result.ok(reportPlantInfoQueryService.info(id));
    }

    /**
     * 新增
     */
    @Log(title = "信息上报", operDesc = "新增植物")
    @PostMapping
    public Result<String> save(@RequestBody @Valid AddReportPlantInfoDto dto) {
        reportPlantInfoCommandService.save(dto);
        return Result.ok();
    }

    /**
     * 导出
     */
    @PostMapping("/export/excel")
    public void exportExcel(@Validated @RequestBody ReportPlantInfoQuery query, HttpServletResponse response) {
        FastExcelOperUtils.exportXlsx(response, reportPlantInfoQueryService.page(new PageParam(-1, -1), query).getRecords(), ReportPlantInfoVo.class, "信息上报植物信息");
    }

    /**
     * 导入
     */
    @Log(title = "信息上报", operDesc = "导入植物")
    @PostMapping("/import")
    public Result<ImportResultVo> importData(@NotNull(message = "文件不能为空") MultipartFile file) throws IOException {
        final ReportPlantInfoListener importListener = new ReportPlantInfoListener(reportPlantInfoCommandService, userInfoQueryApi, mapRegionFacade);
        EasyExcel.read(file.getInputStream(), ImportReportPlantInfoDto.class, importListener).sheet().doRead();
        return Result.ok(ImportExcelUtils.getImportResultVo(importListener));
    }


}
