package com.allin.silas.report.adapter.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.allin.silas.map.client.annotation.MapRegionIdToName;
import com.allin.silas.map.client.excel.MapRegionIdToNameConverter;
import com.allin.view.auth.excel.converter.UserConverter;
import com.allin.view.auth.serializer.annotation.UserIdToFullName;
import com.allin.view.base.enums.serializer.IEnumsChangeDesc;
import com.allin.view.base.execl.converter.IEnumsToStrConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportInsectInfoVo extends ReportBaseVo {
    /**
     * 发现区域
     */
    @ExcelProperty(value = "发现区域", converter = MapRegionIdToNameConverter.class)
    @MapRegionIdToName(key = "regionName")
    private String regionId;

    /**
     * 虫类名称
     */
    @ExcelProperty(value = "虫类名称")
    private String insectName;

    /**
     * 虫类量级 0:少量（1-100只）1:中量（100-1000只）2:大量（＞1000只）
     */
    @ExcelProperty(value = "虫类量级", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "insectLevelDesc", replace = {"0_少量(1-100只)","1_中量(100-1000只)","2_大量(>1000只)"})
    private Integer insectLevel;

    /**
     * 虫种数量
     */
    @ExcelProperty(value = "虫种数量")
    private Integer insectCount;

    /**
     * 分布面积(平方米)
     */
    @ExcelProperty(value = "分布面积(平方米)")
    private BigDecimal distributionArea;

    /**
     * 分布密度
     */
    @ExcelProperty(value = "分布密度")
    private BigDecimal distributionDensity;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", converter = UserConverter.class)
    @UserIdToFullName(key = "createdByFullName")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    public BigDecimal getDistributionDensity() {
        if (insectCount == 0 || distributionArea.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(insectCount).divide(distributionArea, 2, RoundingMode.DOWN);
    }
}
