package com.allin.silas.report.adapter.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddReportInsectInfoDto extends ReportBaseDto {
    /**
     * 发现区域
     */
    @NotNull(message = "发现区域不能为空")
    private String regionId;

    /**
     * 虫类名称
     */
    @NotBlank(message = "虫类名称不能为空")
    private String insectName;

    /**
     * 虫类量级 0:少量（1-100只）1:中量（100-1000只）2:大量（＞1000只）
     */
    @NotNull(message = "虫类量级不能为空")
    @Min(0)
    @Max(2)
    private Integer insectLevel;

    /**
     * 虫种数量
     */
    @NotNull(message = "虫种数量不能为空")
    private Integer insectCount;

    /**
     * 分布面积(平方米)
     */
    @NotNull(message = "分布面积(平方米)不能为空")
    private BigDecimal distributionArea;

    /**
     * 备注
     */
    private String remarks;
}
