package com.allin.silas.report.adapter.query;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportInsectInfoQuery extends ReportBaseQuery {
    /**
     * 发现区域
     */
    private String regionId;

    /**
     * 虫类名称
     */
    private String insectName;

    /**
     * 虫类量级 0:少量（1-100只）1:中量（100-1000只）2:大量（＞1000只）
     */
    @Min(0)
    @Max(2)
    private Integer insectLevel;
}
