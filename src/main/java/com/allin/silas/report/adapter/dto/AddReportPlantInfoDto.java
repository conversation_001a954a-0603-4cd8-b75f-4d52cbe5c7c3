package com.allin.silas.report.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddReportPlantInfoDto extends ReportBaseDto {
    /**
     * 发现区域
     */
    @NotNull(message = "发现区域不能为空")
    private String regionId;

    /**
     * 植物名称
     */
    @NotBlank(message = "植物名称不能为空")
    private String plantName;

    /**
     * 平均高度(厘米)
     */
    @NotNull(message = "平均高度(厘米)不能为空")
    private Integer plantAvgHeight;

    /**
     * 生长速度(厘米/天)
     */
    private Integer plantGrowthSpeed;

    /**
     * 分布面积(平方米)
     */
    @NotNull(message = "分布面积(平方米)不能为空")
    private BigDecimal distributionArea;

    /**
     * 备注
     */
    private String remarks;
}
