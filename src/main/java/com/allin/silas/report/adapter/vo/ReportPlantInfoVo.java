package com.allin.silas.report.adapter.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.allin.silas.map.client.annotation.MapRegionIdToName;
import com.allin.silas.map.client.excel.MapRegionIdToNameConverter;
import com.allin.view.auth.excel.converter.UserConverter;
import com.allin.view.auth.serializer.annotation.UserIdToFullName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportPlantInfoVo extends ReportBaseVo {
    /**
     * 发现区域
     */
    @ExcelProperty(value = "发现区域", converter = MapRegionIdToNameConverter.class)
    @MapRegionIdToName(key = "regionName")
    private String regionId;

    /**
     * 植物名称
     */
    @ExcelProperty(value = "植物名称")
    private String plantName;

    /**
     * 平均高度(厘米)
     */
    @ExcelProperty(value = "平均高度(厘米)")
    private Integer plantAvgHeight;

    /**
     * 生长速度(厘米/天)
     */
    @ExcelProperty(value = "生长速度(厘米/天)")
    private Integer plantGrowthSpeed;

    /**
     * 分布面积(平方米)
     */
    @ExcelProperty(value = "分布面积(平方米)")
    private BigDecimal distributionArea;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", converter = UserConverter.class)
    @UserIdToFullName(key = "createdByFullName")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createdTime;
}
