package com.allin.silas.report.adapter.query;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportBirdInfoQuery extends ReportBaseQuery {
    /**
     * 发现区域
     */
    private String regionId;

    /**
     * 鸟种名称
     */
    private String birdName;

    /**
     * 鸟种危险等级
     */
    private Integer birdRiskLevel;

    /**
     * 鸟类行为
     */
    private String behavior;

    /**
     * 行为原因
     */
    private String behaviorReason;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @Min(0)
    @Max(2)
    private Integer handleStatus;
}
