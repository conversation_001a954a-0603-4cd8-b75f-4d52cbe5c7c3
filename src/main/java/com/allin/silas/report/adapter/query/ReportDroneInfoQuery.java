package com.allin.silas.report.adapter.query;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportDroneInfoQuery extends ReportBaseQuery {
    /**
     * 发现区域
     */
    private String regionId;

    /**
     * 是否进入管制空域 0:否 1:是
     */
    @Min(0)
    @Max(1)
    private Integer isAirspaceRegulated;

    /**
     * 简要描述
     */
    private String description;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @Min(0)
    @Max(2)
    private Integer handleStatus;

    /**
     * 无人机类型 0:微型 1:轻型 2:小型 3:中型 4:大型
     */
    @Min(0)
    @Max(4)
    private Integer droneType;
}
