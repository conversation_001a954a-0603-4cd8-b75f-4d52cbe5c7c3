package com.allin.silas.report.adapter.dto.excel;

import cn.idev.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ImportReportBirdNetInfoDto extends ImportReportBaseDto {
    /**
     * 所在捕鸟网
     */
    @ExcelProperty(value = "*所在捕鸟网")
    @NotBlank(message = "所在捕鸟网不能为空")
    private String birdNetId;

    /**
     * 鸟种名称
     */
    @ExcelProperty(value = "*鸟种名称")
    @NotBlank(message = "鸟种名称不能为空")
    private String birdName;

    /**
     * 鸟种数量
     */
    @ExcelProperty(value = "*鸟种数量")
    @NotNull(message = "鸟种数量不能为空")
    private Integer birdCount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;
}
