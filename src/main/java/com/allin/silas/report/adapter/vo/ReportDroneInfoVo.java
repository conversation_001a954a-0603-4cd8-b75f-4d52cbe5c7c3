package com.allin.silas.report.adapter.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.allin.silas.map.client.annotation.MapRegionIdToName;
import com.allin.silas.map.client.excel.MapRegionIdToNameConverter;
import com.allin.view.auth.excel.converter.UserConverter;
import com.allin.view.auth.serializer.annotation.UserIdToFullName;
import com.allin.view.base.enums.serializer.IEnumsChangeDesc;
import com.allin.view.base.execl.converter.IEnumsToStrConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportDroneInfoVo extends ReportBaseVo {
    /**
     * 发现区域
     */
    @ExcelProperty(value = "发现区域", converter = MapRegionIdToNameConverter.class)
    @MapRegionIdToName(key = "regionName")
    private String regionId;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Double longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Double latitude;

    /**
     * 飞行高度(真高)
     */
    @ExcelProperty(value = "飞行高度(真高)")
    private Integer height;

    /**
     * 是否进入管制空域 0:否 1:是
     */
    @ExcelProperty(value = "是否进入管制空域", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "isAirspaceRegulatedDesc", replace = {"0_否", "1_是"})
    private Integer isAirspaceRegulated;

    /**
     * 无人机类型 0:微型 1:轻型 2:小型 3:中型 4:大型
     */
    @ExcelProperty(value = "无人机类型", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "droneTypeDesc", replace = {"0_微型", "1_轻型", "2_小型", "3_中型", "4_大型"})
    private Integer droneType;

    /**
     * 简要描述
     */
    @ExcelProperty(value = "简要描述")
    private String description;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @ExcelProperty(value = "是否需要处置状态", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "handleStatusDesc", replace = {"0_不需要处置", "1_待处置","2_已处置"})
    private Integer handleStatus;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", converter = UserConverter.class)
    @UserIdToFullName(key = "createdByFullName")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createdTime;
}
