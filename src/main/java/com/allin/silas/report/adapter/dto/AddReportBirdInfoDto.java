package com.allin.silas.report.adapter.dto;

import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddReportBirdInfoDto extends ReportBaseDto {
    /**
     * 发现区域id
     */
    @NotNull(message = "发现区域不能为空")
    private String regionId;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 飞行高度(米)
     */
    @NotNull(message = "飞行高度不能为空")
    private Integer height;

    /**
     * 鸟种名称
     */
    @NotBlank(message = "鸟种名称不能为空")
    private String birdName;

    /**
     * 鸟种危险等级
     */
    @NotNull(message = "鸟种危险等级不能为空")
    private Integer birdRiskLevel;

    /**
     * 鸟种数量
     */
    @NotNull(message = "鸟种数量不能为空")
    private Integer birdCount;

    /**
     * 飞行路线
     */
    private String flightRoute;

    /**
     * 鸟类行为
     */
    @NotBlank(message = "鸟类行为不能为空")
    private String behavior;

    /**
     * 行为原因
     */
    @NotBlank(message = "行为原因不能为空")
    private String behaviorReason;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @NotNull(message = "是否需要处置状态不能为空")
    @Min(0)
    @Max(2)
    private Integer handleStatus;

    /**
     * 入侵处置事件
     */
    private ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion;
}
