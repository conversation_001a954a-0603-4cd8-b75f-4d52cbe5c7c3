package com.allin.silas.report.adapter.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 信息上报共有属性
 *
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
public class ReportBaseDto {
    /**
     * 发现时间
     */
    @NotNull(message = "发现时间不能为空")
    private LocalDateTime discoverTime;

    /**
     * 发现人员
     */
    @NotNull(message = "发现人员不能为空")
    @Size(min = 1, message = "发现人员不能为空")
    private List<String> discoverUserIds;

    /**
     * 附件
     */
    private String attachment;
}