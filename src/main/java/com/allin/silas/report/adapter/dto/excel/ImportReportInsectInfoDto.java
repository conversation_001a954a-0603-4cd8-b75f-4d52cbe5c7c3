package com.allin.silas.report.adapter.dto.excel;

import cn.idev.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ImportReportInsectInfoDto extends ImportReportBaseDto {
    /**
     * 发现区域
     */
    @ExcelProperty(value = "*发现区域")
    @NotBlank(message = "发现区域不能为空")
    private String regionId;

    /**
     * 虫类名称
     */
    @ExcelProperty(value = "*虫类名称")
    @NotBlank(message = "虫类名称不能为空")
    private String insectName;

    /**
     * 虫类量级 0:少量（1-100只）1:中量（100-1000只）2:大量（＞1000只）
     */
    @ExcelProperty(value = "*虫类量级")
    @NotBlank(message = "虫类量级不能为空")
    private String insectLevel;

    /**
     * 虫种数量
     */
    @ExcelProperty(value = "*虫种数量")
    @NotNull(message = "虫种数量不能为空")
    private Integer insectCount;

    /**
     * 分布面积(平方米)
     */
    @ExcelProperty(value = "*分布面积(平方米)")
    @NotNull(message = "分布面积(平方米)不能为空")
    private BigDecimal distributionArea;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;
}
