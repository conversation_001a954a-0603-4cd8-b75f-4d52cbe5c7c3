package com.allin.silas.report.adapter.controller;

import cn.hutool.extra.spring.SpringUtil;
import cn.idev.excel.EasyExcel;
import com.allin.silas.kn.client.KnBirdInfoDangerLevelFacade;
import com.allin.silas.map.client.MapBirdNetFacade;
import com.allin.silas.report.adapter.dto.AddReportBirdNetInfoDto;
import com.allin.silas.report.adapter.dto.excel.ImportReportBirdNetInfoDto;
import com.allin.silas.report.adapter.query.ReportBirdNetInfoQuery;
import com.allin.silas.report.adapter.vo.ReportBirdNetInfoVo;
import com.allin.silas.report.app.listener.ReportBirdNetInfoListener;
import com.allin.silas.report.app.service.ReportBirdNetInfoCommandService;
import com.allin.silas.report.app.service.ReportBirdNetInfoQueryService;
import com.allin.view.auth.api.UserInfoQueryApi;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import com.allin.view.file.pojo.vo.ImportResultVo;
import com.allin.view.file.util.ImportExcelUtils;
import com.allin.view.log.annotation.Log;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.IOException;

/**
 * 作业管理/信息上报/网捕
 *
 * <AUTHOR>
 * @since 2025/7/5
 **/
@RestController
@RequestMapping("/report/bird_net_info")
@Validated
public class ReportBirdNetInfoController {

    private final ReportBirdNetInfoQueryService reportBirdNetInfoQueryService;

    private final ReportBirdNetInfoCommandService reportBirdNetInfoCommandService;

    private final MapBirdNetFacade mapBirdNetFacade;

    private final UserInfoQueryApi userInfoQueryApi;

    private final KnBirdInfoDangerLevelFacade knBirdInfoDangerLevelFacade;

    public ReportBirdNetInfoController(ReportBirdNetInfoQueryService reportBirdNetInfoQueryService,
                                       ReportBirdNetInfoCommandService reportBirdNetInfoCommandService,
                                       MapBirdNetFacade mapBirdNetFacade,
                                       KnBirdInfoDangerLevelFacade knBirdInfoDangerLevelFacade) {
        this.reportBirdNetInfoQueryService = reportBirdNetInfoQueryService;
        this.reportBirdNetInfoCommandService = reportBirdNetInfoCommandService;
        this.mapBirdNetFacade = mapBirdNetFacade;
        this.knBirdInfoDangerLevelFacade = knBirdInfoDangerLevelFacade;
        this.userInfoQueryApi = SpringUtil.getBean(UserInfoQueryApi.class);
    }

    /**
     * 分页查询
     */
    @GetMapping
    public Result<PageData<ReportBirdNetInfoVo>> page(PageParam pageParam, @Valid ReportBirdNetInfoQuery query) {
        return Result.ok(reportBirdNetInfoQueryService.page(pageParam, query));
    }

    /**
     * 详情
     */
    @GetMapping("/{id}")
    public Result<ReportBirdNetInfoVo> info(@PathVariable String id) {
        return Result.ok(reportBirdNetInfoQueryService.info(id));
    }

    /**
     * 新增
     */
    @Log(title = "信息上报", operDesc = "新增网捕")
    @PostMapping
    public Result<String> save(@RequestBody @Valid AddReportBirdNetInfoDto dto) {
        reportBirdNetInfoCommandService.save(dto);
        return Result.ok();
    }

    /**
     * 导出
     */
    @PostMapping("/export/excel")
    public void exportExcel(@Validated @RequestBody ReportBirdNetInfoQuery query, HttpServletResponse response) {
        FastExcelOperUtils.exportXlsx(response, reportBirdNetInfoQueryService.page(new PageParam(-1, -1), query).getRecords(), ReportBirdNetInfoVo.class, "信息上报网捕信息");
    }

    /**
     * 导入
     */
    @Log(title = "信息上报", operDesc = "导入网捕")
    @PostMapping("/import")
    public Result<ImportResultVo> importData(@NotNull(message = "文件不能为空") MultipartFile file) throws IOException {
        final ReportBirdNetInfoListener importListener = new ReportBirdNetInfoListener(reportBirdNetInfoCommandService, mapBirdNetFacade, userInfoQueryApi, knBirdInfoDangerLevelFacade);
        EasyExcel.read(file.getInputStream(), ImportReportBirdNetInfoDto.class, importListener).sheet().doRead();
        return Result.ok(ImportExcelUtils.getImportResultVo(importListener));
    }
}
