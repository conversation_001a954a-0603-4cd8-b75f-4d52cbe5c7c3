package com.allin.silas.report.adapter.dto.excel;

import cn.idev.excel.annotation.ExcelProperty;
import com.allin.view.base.execl.DefaultExportStyle;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ImportReportBaseDto extends DefaultExportStyle {
    /**
     * 发现时间
     */
    @ExcelProperty(value = "*发现时间")
    @NotNull(message = "发现时间不能为空")
    private LocalDateTime discoverTime;

    /**
     * 发现人员
     */
    @ExcelProperty(value = "*发现人员（多选，用“,”分隔）")
    @NotNull(message = "发现人员不能为空")
    private String discoverUserIds;
}
