package com.allin.silas.report.adapter.dto.excel;

import cn.idev.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ImportReportPlantInfoDto extends ImportReportBaseDto {
    /**
     * 发现区域
     */
    @ExcelProperty(value = "*发现区域")
    @NotBlank(message = "发现区域不能为空")
    private String regionId;

    /**
     * 植物名称
     */
    @ExcelProperty(value = "*植物名称")
    @NotBlank(message = "植物名称不能为空")
    private String plantName;

    /**
     * 平均高度(厘米)
     */
    @ExcelProperty(value = "*平均高度(厘米)")
    @NotNull(message = "平均高度(厘米)不能为空")
    private Integer plantAvgHeight;

    /**
     * 生长速度(厘米/天)
     */
    @ExcelProperty(value = "生长速度(厘米/天)")
    private Integer plantGrowthSpeed;

    /**
     * 分布面积(平方米)
     */
    @ExcelProperty(value = "*分布面积(平方米)")
    @NotNull(message = "分布面积(平方米)不能为空")
    private BigDecimal distributionArea;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;
}
