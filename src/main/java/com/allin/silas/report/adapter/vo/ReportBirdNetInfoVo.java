package com.allin.silas.report.adapter.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.allin.silas.map.client.annotation.MapBirdNetIdToName;
import com.allin.silas.map.client.excel.MapBirdNetIdToNameConverter;
import com.allin.view.auth.excel.converter.UserConverter;
import com.allin.view.auth.serializer.annotation.UserIdToFullName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportBirdNetInfoVo extends ReportBaseVo {
    /**
     * 所在捕鸟网
     */
    @ExcelProperty(value = "所在捕鸟网", converter = MapBirdNetIdToNameConverter.class)
    @MapBirdNetIdToName(key = "birdNetName")
    private String birdNetId;

    /**
     * 鸟种名称
     */
    @ExcelProperty(value = "鸟种名称")
    private String birdName;

    /**
     * 鸟种危险等级
     */
    @ExcelProperty(value = "鸟种危险等级")
    private Integer birdRiskLevel;

    /**
     * 鸟种数量
     */
    @ExcelProperty(value = "鸟种数量")
    private Integer birdCount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", converter = UserConverter.class)
    @UserIdToFullName(key = "createdByFullName")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createdTime;
}
