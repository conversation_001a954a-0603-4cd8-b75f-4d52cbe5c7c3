package com.allin.silas.report.adapter.dto.excel;

import cn.idev.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ImportReportFloaterInfoDto extends ImportReportBaseDto {
    /**
     * 发现区域
     */
    @ExcelProperty(value = "*发现区域")
    @NotBlank(message = "发现区域不能为空")
    private String regionId;

    /**
     * 经度
     */
    @ExcelProperty(value = "*经度")
    @NotNull(message = "经度不能为空")
    private Double longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "*纬度")
    @NotNull(message = "纬度不能为空")
    private Double latitude;

    /**
     * 升空高度(米)
     */
    @ExcelProperty(value = "*升空高度(米)")
    @NotNull(message = "升空高度(米)不能为空")
    private Integer height;

    /**
     * 种类 1:气球（不包括系留气球、无人驾驶自由气球和探空气球等）2:风筝 3:孔明灯 4:无人机 5:塑料袋 6:焚烧产生的升空烟雾 7:其他
     */
    @ExcelProperty(value = "*种类")
    @NotBlank(message = "种类不能为空")
    private String floaterType;

    /**
     * 空飘物外观
     */
    @ExcelProperty(value = "空飘物外观")
    private String floaterAppearance;

    /**
     * 可能来源
     */
    @ExcelProperty(value = "*可能来源")
    @NotBlank(message = "可能来源不能为空")
    private String source;

    /**
     * 简要描述
     */
    @ExcelProperty(value = "*简要描述")
    @NotBlank(message = "简要描述不能为空")
    private String description;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @ExcelProperty(value = "*是否需要处置状态")
    @NotBlank(message = "是否需要处置状态不能为空")
    private String handleStatus;

    /**
     * 处置时间
     */
    @ExcelProperty(value = "处置时间")
    private LocalDateTime handleTime;

    /**
     * 处置人员
     */
    @ExcelProperty(value = "处置人员（多选，用“,”分隔）")
    private String handleUserIds;

    /**
     * 处置措施
     */
    @ExcelProperty(value = "处置措施")
    private String handleMethod;

    /**
     * 猎枪耗弹数量（仅在措施为猎枪时填写）
     */
    @ExcelProperty(value = "猎枪耗弹数量")
    private Integer shotgunAmmoCount;

    /**
     * 钝雷弹耗弹数量（仅在措施为钝雷弹时填写）
     */
    @ExcelProperty(value = "钝雷弹耗弹数量")
    private Integer thunderAmmoCount;

    /**
     * 处置效果
     */
    @ExcelProperty(value = "处置效果")
    private String handleResult;

    /**
     * 是否影响航班：0未影响、1已影响
     */
    @ExcelProperty(value = "是否影响航班")
    private String affectFlight;

    /**
     * 是否通报空管：0未通报、1已通报
     */
    @ExcelProperty(value = "是否通报空管")
    private String notifyAtc;

    /**
     * 是否通报公安机关：0未通报、1已通报
     */
    @ExcelProperty(value = "是否通报公安机关")
    private String notifyPolice;
}
