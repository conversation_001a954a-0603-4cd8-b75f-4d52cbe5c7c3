package com.allin.silas.report.adapter.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.allin.view.auth.excel.converter.UserConverter;
import com.allin.view.auth.serializer.annotation.UserIdToFullName;
import com.allin.view.base.execl.DefaultExportStyle;
import com.allin.view.file.serializer.FileIdToFileInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/5
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportBaseVo extends DefaultExportStyle {
    /**
     * id
     */
    @ExcelIgnore
    private String id;

    /**
     * 发现时间
     */
    @ExcelProperty(value = "发现时间")
    private LocalDateTime discoverTime;

    /**
     * 发现人员
     */
    @ExcelProperty(value = "发现人员", converter = UserConverter.class)
    @UserIdToFullName(key = "discoverUserIdFullNames")
    private List<String> discoverUserIds;

    /**
     * 附件
     */
    @ExcelIgnore
    @FileIdToFileInfo(key = "attachmentInfo")
    private String attachment;
}
