package com.allin.silas.report.adapter.controller;

import cn.hutool.extra.spring.SpringUtil;
import cn.idev.excel.EasyExcel;
import com.allin.silas.map.client.MapRegionFacade;
import com.allin.silas.report.adapter.dto.AddReportFloaterInfoDto;
import com.allin.silas.report.adapter.dto.excel.ImportReportFloaterInfoDto;
import com.allin.silas.report.adapter.query.ReportFloaterInfoQuery;
import com.allin.silas.report.adapter.vo.ReportFloaterInfoVo;
import com.allin.silas.report.app.listener.ReportFloaterInfoListener;
import com.allin.silas.report.app.service.ReportFloaterInfoCommandService;
import com.allin.silas.report.app.service.ReportFloaterInfoQueryService;
import com.allin.view.auth.api.UserInfoQueryApi;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import com.allin.view.file.pojo.vo.ImportResultVo;
import com.allin.view.file.util.ImportExcelUtils;
import com.allin.view.log.annotation.Log;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.IOException;

/**
 * 作业管理/信息上报/空飘物
 *
 * <AUTHOR>
 * @since 2025/7/7
 **/
@RestController
@RequestMapping("report/floater_info")
public class ReportFloaterInfoController {

    private final ReportFloaterInfoQueryService reportFloaterInfoQueryService;

    private final ReportFloaterInfoCommandService reportFloaterInfoCommandService;

    private final MapRegionFacade mapRegionFacade;

    private final UserInfoQueryApi userInfoQueryApi;

    public ReportFloaterInfoController(ReportFloaterInfoQueryService reportFloaterInfoQueryService,
                                       ReportFloaterInfoCommandService reportFloaterInfoCommandService,
                                       MapRegionFacade mapRegionFacade) {
        this.reportFloaterInfoQueryService = reportFloaterInfoQueryService;
        this.reportFloaterInfoCommandService = reportFloaterInfoCommandService;
        this.mapRegionFacade = mapRegionFacade;
        this.userInfoQueryApi = SpringUtil.getBean(UserInfoQueryApi.class);
    }

    /**
     * 分页查询
     */
    @GetMapping
    public Result<PageData<ReportFloaterInfoVo>> page(PageParam pageParam, @Valid ReportFloaterInfoQuery query) {
        return Result.ok(reportFloaterInfoQueryService.page(pageParam, query));
    }

    /**
     * 详情
     */
    @GetMapping("/{id}")
    public Result<ReportFloaterInfoVo> info(@PathVariable String id) {
        return Result.ok(reportFloaterInfoQueryService.info(id));
    }

    /**
     * 新增
     */
    @Log(title = "信息上报", operDesc = "新增空飘物")
    @PostMapping
    public Result<String> save(@RequestBody @Valid AddReportFloaterInfoDto dto) {
        reportFloaterInfoCommandService.save(dto);
        return Result.ok();
    }

    /**
     * 导出
     */
    @PostMapping("/export/excel")
    public void exportExcel(@Validated @RequestBody ReportFloaterInfoQuery query, HttpServletResponse response) {
        FastExcelOperUtils.exportXlsx(response, reportFloaterInfoQueryService.page(new PageParam(-1, -1), query).getRecords(), ReportFloaterInfoVo.class, "信息上报空飘物信息");
    }

    /**
     * 导入
     */
    @Log(title = "信息上报", operDesc = "导入空飘物")
    @PostMapping("/import")
    public Result<ImportResultVo> importData(@NotNull(message = "文件不能为空") MultipartFile file) throws IOException {
        final ReportFloaterInfoListener importListener = new ReportFloaterInfoListener(reportFloaterInfoCommandService, mapRegionFacade, userInfoQueryApi);
        EasyExcel.read(file.getInputStream(), ImportReportFloaterInfoDto.class, importListener).sheet().doRead();
        return Result.ok(ImportExcelUtils.getImportResultVo(importListener));
    }
}
