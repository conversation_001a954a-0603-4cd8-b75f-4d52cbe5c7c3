package com.allin.silas.report.adapter.controller;

import cn.hutool.extra.spring.SpringUtil;
import cn.idev.excel.EasyExcel;
import com.allin.silas.map.client.MapRegionFacade;
import com.allin.silas.report.adapter.dto.AddReportInsectInfoDto;
import com.allin.silas.report.adapter.dto.excel.ImportReportInsectInfoDto;
import com.allin.silas.report.adapter.query.ReportInsectInfoQuery;
import com.allin.silas.report.adapter.vo.ReportInsectInfoVo;
import com.allin.silas.report.app.listener.ReportInsectInfoListener;
import com.allin.silas.report.app.service.ReportInsectInfoCommandService;
import com.allin.silas.report.app.service.ReportInsectInfoQueryService;
import com.allin.view.auth.api.UserInfoQueryApi;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import com.allin.view.file.pojo.vo.ImportResultVo;
import com.allin.view.file.util.ImportExcelUtils;
import com.allin.view.log.annotation.Log;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.IOException;

/**
 * 作业管理/信息上报/虫情
 *
 * <AUTHOR>
 * @since 2025/7/5
 **/
@RestController
@RequestMapping("/report/insect_info")
@Validated
public class ReportInsectInfoController {

    private final ReportInsectInfoCommandService reportInsectInfoCommandService;

    private final ReportInsectInfoQueryService reportInsectInfoQueryService;

    private final UserInfoQueryApi userInfoQueryApi;

    private final MapRegionFacade mapRegionFacade;

    public ReportInsectInfoController(ReportInsectInfoCommandService reportInsectInfoCommandService,
                                      ReportInsectInfoQueryService reportInsectInfoQueryService,
                                      MapRegionFacade mapRegionFacade) {
        this.reportInsectInfoCommandService = reportInsectInfoCommandService;
        this.reportInsectInfoQueryService = reportInsectInfoQueryService;
        this.userInfoQueryApi = SpringUtil.getBean(UserInfoQueryApi.class);
        this.mapRegionFacade = mapRegionFacade;
    }

    /**
     * 分页查询
     */
    @GetMapping
    public Result<PageData<ReportInsectInfoVo>> page(PageParam pageParam, @Valid ReportInsectInfoQuery query) {
        return Result.ok(reportInsectInfoQueryService.page(pageParam, query));
    }

    /**
     * 详情
     */
    @GetMapping("/{id}")
    public Result<ReportInsectInfoVo> info(@PathVariable String id) {
        return Result.ok(reportInsectInfoQueryService.info(id));
    }

    /**
     * 新增
     */
    @Log(title = "信息上报", operDesc = "新增虫情")
    @PostMapping
    public Result<String> save(@RequestBody @Valid AddReportInsectInfoDto dto) {
        reportInsectInfoCommandService.save(dto);
        return Result.ok();
    }

    /**
     * 导出
     */
    @PostMapping("/export/excel")
    public void exportExcel(@Validated @RequestBody ReportInsectInfoQuery query, HttpServletResponse response) {
        FastExcelOperUtils.exportXlsx(response, reportInsectInfoQueryService.page(new PageParam(-1, -1), query).getRecords(), ReportInsectInfoVo.class, "信息上报虫情信息");
    }

    /**
     * 导入
     */
    @Log(title = "信息上报", operDesc = "导入虫情")
    @PostMapping("/import")
    public Result<ImportResultVo> importData(@NotNull(message = "文件不能为空") MultipartFile file) throws IOException {
        final ReportInsectInfoListener importListener = new ReportInsectInfoListener(reportInsectInfoCommandService, userInfoQueryApi, mapRegionFacade);
        EasyExcel.read(file.getInputStream(), ImportReportInsectInfoDto.class, importListener).sheet().doRead();
        return Result.ok(ImportExcelUtils.getImportResultVo(importListener));
    }

}
