package com.allin.silas.report.adapter.controller;

import cn.hutool.extra.spring.SpringUtil;
import cn.idev.excel.EasyExcel;
import com.allin.silas.map.client.MapRegionFacade;
import com.allin.silas.report.adapter.dto.AddReportDroneInfoDto;
import com.allin.silas.report.adapter.dto.excel.ImportReportDroneInfoDto;
import com.allin.silas.report.adapter.query.ReportDroneInfoQuery;
import com.allin.silas.report.adapter.vo.ReportDroneInfoVo;
import com.allin.silas.report.app.listener.ReportDroneInfoListener;
import com.allin.silas.report.app.service.ReportDroneInfoCommandService;
import com.allin.silas.report.app.service.ReportDroneInfoQueryService;
import com.allin.view.auth.api.UserInfoQueryApi;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import com.allin.view.file.pojo.vo.ImportResultVo;
import com.allin.view.file.util.ImportExcelUtils;
import com.allin.view.log.annotation.Log;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Map;

/**
 * 作业管理/信息上报/无人机
 *
 * <AUTHOR>
 * @since 2025/7/7
 **/
@RestController
@RequestMapping("report/drone_info")
public class ReportDroneInfoController {

    private final ReportDroneInfoQueryService reportDroneInfoQueryService;

    private final ReportDroneInfoCommandService reportDroneInfoCommandService;

    private final MapRegionFacade mapRegionFacade;

    private final UserInfoQueryApi userInfoQueryApi;

    public ReportDroneInfoController(ReportDroneInfoQueryService reportDroneInfoQueryService,
                                     ReportDroneInfoCommandService reportDroneInfoCommandService,
                                     MapRegionFacade mapRegionFacade) {
        this.reportDroneInfoQueryService = reportDroneInfoQueryService;
        this.reportDroneInfoCommandService = reportDroneInfoCommandService;
        this.mapRegionFacade = mapRegionFacade;
        this.userInfoQueryApi = SpringUtil.getBean(UserInfoQueryApi.class);
    }

    /**
     * 分页查询
     */
    @GetMapping
    public Result<PageData<ReportDroneInfoVo>> page(PageParam pageParam, @Valid ReportDroneInfoQuery query) {
        return Result.ok(reportDroneInfoQueryService.page(pageParam, query));
    }

    /**
     * 详情
     */
    @GetMapping("/{id}")
    public Result<ReportDroneInfoVo> info(@PathVariable String id) {
        return Result.ok(reportDroneInfoQueryService.info(id));
    }

    /**
     * 统计各无人机类型数量
     */
    @GetMapping("/statistics_drone_type_count")
    public Result<Map<Integer, Long>> statisticsDroneTypeCount(@Valid ReportDroneInfoQuery query) {
        return Result.ok(reportDroneInfoQueryService.statisticsDroneTypeCount(query));
    }

    /**
     * 新增
     */
    @Log(title = "信息上报", operDesc = "新增无人机")
    @PostMapping
    public Result<String> save(@RequestBody @Valid AddReportDroneInfoDto dto) {
        reportDroneInfoCommandService.save(dto);
        return Result.ok();
    }

    /**
     * 导出
     */
    @PostMapping("/export/excel")
    public void exportExcel(@Validated @RequestBody ReportDroneInfoQuery query, HttpServletResponse response) {
        FastExcelOperUtils.exportXlsx(response, reportDroneInfoQueryService.page(new PageParam(-1, -1), query).getRecords(), ReportDroneInfoVo.class, "信息上报无人机信息");
    }

    /**
     * 导入
     */
    @Log(title = "信息上报", operDesc = "导入无人机")
    @PostMapping("/import")
    public Result<ImportResultVo> importData(@NotNull(message = "文件不能为空") MultipartFile file) throws IOException {
        final ReportDroneInfoListener importListener = new ReportDroneInfoListener(reportDroneInfoCommandService, mapRegionFacade, userInfoQueryApi);
        EasyExcel.read(file.getInputStream(), ImportReportDroneInfoDto.class, importListener).sheet().doRead();
        return Result.ok(ImportExcelUtils.getImportResultVo(importListener));
    }
}
