package com.allin.silas.report.adapter.dto;

import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddReportDroneInfoDto extends ReportBaseDto {
    /**
     * 发现区域
     */
    @NotNull(message = "发现区域不能为空")
    private String regionId;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空")
    private Double longitude;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    private Double latitude;

    /**
     * 飞行高度(真高)
     */
    @NotNull(message = "飞行高度(真高)不能为空")
    private Integer height;

    /**
     * 是否进入管制空域 0:否 1:是
     */
    @NotNull(message = "是否进入管制空域不能为空")
    @Min(0)
    @Max(1)
    private Integer isAirspaceRegulated;

    /**
     * 无人机类型 0:微型 1:轻型 2:小型 3:中型 4:大型
     */
    @NotNull(message = "无人机类型不能为空")
    @Min(0)
    @Max(4)
    private Integer droneType;

    /**
     * 简要描述
     */
    @NotBlank(message = "简要描述不能为空")
    private String description;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @NotNull(message = "是否需要处置状态不能为空")
    @Min(0)
    @Max(2)
    private Integer handleStatus;

    /**
     * 入侵处置事件
     */
    private ReportPushEventInvasionDto.ReportHandleEventInvasionDto eventInvasion;
}
