package com.allin.silas.report.adapter.dto.excel;

import cn.idev.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/23
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ImportReportBirdInfoDto extends ImportReportBaseDto {
    /**
     * 发现区域
     */
    @ExcelProperty(value = "*发现区域")
    @NotBlank(message = "发现区域不能为空")
    private String regionId;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Double longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Double latitude;

    /**
     * 飞行高度(米)
     */
    @ExcelProperty(value = "*飞行高度(米)")
    @NotNull(message = "飞行高度不能为空")
    private Integer height;

    /**
     * 鸟种名称
     */
    @ExcelProperty(value = "*鸟种名称")
    @NotBlank(message = "鸟种名称不能为空")
    private String birdName;

    /**
     * 鸟种数量
     */
    @ExcelProperty(value = "*鸟种数量")
    @NotNull(message = "鸟种数量不能为空")
    private Integer birdCount;

    /**
     * 飞行路线
     */
    @ExcelProperty(value = "飞行路线")
    private String flightRoute;

    /**
     * 鸟类行为
     */
    @ExcelProperty(value = "*鸟类行为")
    @NotBlank(message = "鸟类行为不能为空")
    private String behavior;

    /**
     * 行为原因
     */
    @ExcelProperty(value = "*行为原因")
    @NotBlank(message = "行为原因不能为空")
    private String behaviorReason;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @NotBlank(message = "是否需要处置状态不能为空")
    @ExcelProperty(value = "*是否需要处置状态")
    private String handleStatus;

    /**
     * 处置时间
     */
    @ExcelProperty(value = "处置时间")
    private LocalDateTime handleTime;

    /**
     * 处置人员
     */
    @ExcelProperty(value = "处置人员（多选，用“,”分隔）")
    private String handleUserIds;

    /**
     * 处置措施
     */
    @ExcelProperty(value = "处置措施")
    private String handleMethod;

    /**
     * 猎枪耗弹数量（仅在措施为猎枪时填写）
     */
    @ExcelProperty(value = "猎枪耗弹数量")
    private Integer shotgunAmmoCount;

    /**
     * 钝雷弹耗弹数量（仅在措施为钝雷弹时填写）
     */
    @ExcelProperty(value = "钝雷弹耗弹数量")
    private Integer thunderAmmoCount;

    /**
     * 处置效果
     */
    @ExcelProperty(value = "处置效果")
    private String handleResult;

    /**
     * 是否影响航班：0未影响、1已影响
     */
    @ExcelProperty(value = "是否影响航班")
    private String affectFlight;

    /**
     * 是否通报空管：0未通报、1已通报
     */
    @ExcelProperty(value = "是否通报空管")
    private String notifyAtc;

    /**
     * 是否通报公安机关：0未通报、1已通报
     */
    @ExcelProperty(value = "是否通报公安机关")
    private String notifyPolice;
}
