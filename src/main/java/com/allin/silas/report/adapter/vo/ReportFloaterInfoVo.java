package com.allin.silas.report.adapter.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.allin.silas.map.client.annotation.MapRegionIdToName;
import com.allin.silas.map.client.excel.MapRegionIdToNameConverter;
import com.allin.view.auth.excel.converter.UserConverter;
import com.allin.view.auth.serializer.annotation.UserIdToFullName;
import com.allin.view.base.enums.serializer.IEnumsChangeDesc;
import com.allin.view.base.execl.converter.IEnumsToStrConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/7
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportFloaterInfoVo extends ReportBaseVo {
    /**
     * 发现区域
     */
    @ExcelProperty(value = "发现区域", converter = MapRegionIdToNameConverter.class)
    @MapRegionIdToName(key = "regionName")
    private String regionId;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Double longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Double latitude;

    /**
     * 升空高度(米)
     */
    @ExcelProperty(value = "升空高度(米)")
    private Integer height;

    /**
     * 种类 1:气球（不包括系留气球、无人驾驶自由气球和探空气球等）2:风筝 3:孔明灯 4:无人机 5:塑料袋 6:焚烧产生的升空烟雾 7:其他
     */
    @ExcelProperty(value = "种类", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "floaterTypeDesc", replace = {"1_气球（不包括系留气球、无人驾驶自由气球和探空气球等）", "2_风筝", "3_孔明灯", "4_无人机", "5_塑料袋", "6_焚烧产生的升空烟雾", "7_其他"})
    private Integer floaterType;

    /**
     * 空飘物外观
     */
    @ExcelProperty(value = "空飘物外观")
    private String floaterAppearance;

    /**
     * 可能来源
     */
    @ExcelProperty(value = "可能来源")
    private String source;

    /**
     * 简要描述
     */
    @ExcelProperty(value = "简要描述")
    private String description;

    /**
     * 是否需要处置状态 0:不需要处置, 1:待处置 2:已处置
     */
    @ExcelProperty(value = "是否需要处置状态", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "handleStatusDesc", replace = {"0_不需要处置", "1_待处置", "2_已处置"})
    private Integer handleStatus;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", converter = UserConverter.class)
    @UserIdToFullName(key = "createdByFullName")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createdTime;
}
