package com.allin.silas.report.infra.repository;

import com.allin.silas.report.app.entity.ReportDiscoverPerson;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper
public interface ReportDiscoverPersonMapper extends BaseMapper<ReportDiscoverPerson> {

    /**
     * 批量新增
     */
    default void saveBatch(ReportDiscoverPersonReportTypeEnums enums, String reportId, List<String> discoverUserIds) {
        insert(buildReportDiscoverPersonList(enums, reportId, discoverUserIds));
    }

    /**
     * 批量新增
     */
    default void saveBatch(ReportDiscoverPersonReportTypeEnums enums, Map<String, List<String>> discoverUserIdMap) {
        List<ReportDiscoverPerson> list = new ArrayList<>();
        discoverUserIdMap.forEach((reportId, discoverUserIds) ->
                list.addAll(buildReportDiscoverPersonList(enums, reportId, discoverUserIds)));

        insert(list);
    }

    /**
     * 获取发现人员列表
     */
    default List<String> list(ReportDiscoverPersonReportTypeEnums enums, String reportId) {
        return selectList(new LambdaQueryWrapper<ReportDiscoverPerson>()
                .eq(ReportDiscoverPerson::getReportType, enums.getCode())
                .eq(ReportDiscoverPerson::getReportId, reportId))
                .stream()
                .map(ReportDiscoverPerson::getDiscoverUserId)
                .toList();
    }

    default Map<String, List<String>> map(ReportDiscoverPersonReportTypeEnums enums, List<String> reportIds) {
        return selectList(new LambdaQueryWrapper<ReportDiscoverPerson>()
                .eq(ReportDiscoverPerson::getReportType, enums.getCode())
                .in(ReportDiscoverPerson::getReportId, reportIds))
                .stream()
                .collect(Collectors.groupingBy(ReportDiscoverPerson::getReportId,
                        Collectors.mapping(ReportDiscoverPerson::getDiscoverUserId, Collectors.toList())));
    }

    private List<ReportDiscoverPerson> buildReportDiscoverPersonList(ReportDiscoverPersonReportTypeEnums enums, String reportId, List<String> discoverUserIds) {
        return discoverUserIds.stream().map(discoverUserId -> ReportDiscoverPerson.builder()
                .reportType(enums.getCode())
                .reportId(reportId)
                .discoverUserId(discoverUserId)
                .build()).toList();
    }
}