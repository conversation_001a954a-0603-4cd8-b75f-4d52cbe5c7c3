package com.allin.silas.radar.adapter.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 删除雷达目标信息
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
@Data
public class DelRadarTargetDto {

    /**
     * 日期, yyyy-MM-dd
     */
    @NotNull
    private LocalDate date;

    /**
     * 批次号
     */
    @NotEmpty
    private List<String> batchNumbers;
}
