package com.allin.silas.radar.adapter.dto;

import com.allin.silas.radar.app.enums.RadarDetectAndMarkTypeEnums;
import com.allin.view.base.enums.validator.IEnumValid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 标记雷达目标信息
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RadarTargetMarkDto {

    /**
     * 日期, yyyy-MM-dd
     */
    @NotNull
    private LocalDate date;

    /**
     * 后端生成的批次号
     */
    @NotBlank
    private String backendBatchNumber;

    /**
     * 标记类型,无人机/空飘物/鸟类/鸟群/飞机/违建/人/船/车辆
     *
     * @see RadarDetectAndMarkTypeEnums#code
     */
    @NotNull
    @IEnumValid(target = RadarDetectAndMarkTypeEnums.class)
    private String markType;

}
