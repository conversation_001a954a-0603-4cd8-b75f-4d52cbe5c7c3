package com.allin.silas.radar.adapter.query;

import com.allin.silas.radar.app.enums.RadarDetectAndMarkTypeEnums;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.enums.validator.IEnumValid;
import com.allin.view.base.execl.DefaultExportStyle;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 合并的雷达目标信息表
 */
@Data
public class RadarTargetMergedInfoPageQuery extends DefaultExportStyle {

    /**
     * 开始时间, yyyy-MM-dd HH:mm:ss
     */
    @NotNull
    private LocalDateTime startTime;

    /**
     * 结束时间, yyyy-MM-dd HH:mm:ss
     */
    @NotNull
    private LocalDateTime endTime;

    /**
     * 识别类型
     */
    private List<@IEnumValid(target = RadarDetectAndMarkTypeEnums.class) String> detectTypes;

    /**
     * 最小高度
     */
    private Double minHeight;

    /**
     * 最大高度
     */
    private Double maxHeight;

    /**
     * 离设备最小距离
     */
    private Double minDistance;

    /**
     * 离设备最大距离
     */
    private Double maxDistance;

    /**
     * 离跑道最小距离
     */
    private Double minRunwayDistance;

    /**
     * 离跑道最大距离
     */
    private Double maxRunwayDistance;

    /**
     * 最小方位角
     */
    private Double minAzimuth;

    /**
     * 最大方位角
     */
    private Double maxAzimuth;

    /**
     * 关联光电目标,0未关联,1已关联
     */
    @Range(min = 0, max = 1)
    private Integer isRelatedVisualTarget;

    /**
     * 推送状态,0未推送,1已推送
     */
    @Range(min = 0, max = 1)
    private Integer pushStatus;

    /**
     * 危险等级
     */
    private List<@Range(min = 1, max = 5) Integer> dangerLevels;

    /**
     * 项目编号
     */
    @JsonIgnore
    private String projectId;

    /**
     * 关联表查询结束时间（用于优化JOIN性能）
     */
    @JsonIgnore
    private LocalDateTime relationEndTime;

    /**
     * 校验参数
     */
    public void validateAndInit() {
        setProjectId(SecurityContextHolder.getProjectId());
        // 获取查询结束日期的次日00:00:00（即结束日期的23:59:59之后）
        relationEndTime = endTime.plusHours(1);
    }
}