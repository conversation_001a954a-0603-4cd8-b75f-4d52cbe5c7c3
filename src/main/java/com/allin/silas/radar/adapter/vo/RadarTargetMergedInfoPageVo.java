package com.allin.silas.radar.adapter.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.format.DateTimeFormat;
import com.allin.view.base.execl.DefaultExportStyle;
import com.allin.view.base.execl.converter.IEnumsConverter;
import com.allin.view.base.execl.converter.IEnumsToStrConverter;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 雷达目标合并信息表
 */
@Data
public class RadarTargetMergedInfoPageVo extends DefaultExportStyle {

    /**
     * 后端生成的目标批次号
     */
    @ExcelProperty("批次号")
    private String backendBatchNumber;

    /**
     * 识别类型: 鸟群/鸟类/无人机/空飘物/违建/飞机/车辆/人/船
     */
    @ExcelProperty("识别类型")
    private String detectType;

    /**
     * 标记类型: 鸟群/鸟类/无人机/空飘物/违建/飞机/车辆/人/船
     */
    @ExcelProperty("标记类型")
    private String markType;

    /**
     * 危险等级: 1-5
     */
    @ExcelProperty("危险等级")
    private Integer dangerLevel;

    /**
     * 碰撞可能性
     */
    @ExcelProperty("碰撞可能性")
    private Integer collisionPossibility;

    /**
     * 开始时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    /**
     * 最小目标置信度
     */
    @ExcelProperty("最小置信度")
    private Double minConfidenceLevel;

    /**
     * 最大目标置信度
     */
    @ExcelProperty("最大置信度")
    private Double maxConfidenceLevel;

    /**
     * 最小目标高度(米)
     */
    @ExcelProperty("最小高度(米)")
    private Double minHeight;

    /**
     * 最大目标高度(米)
     */
    @ExcelProperty("最大高度(米)")
    private Double maxHeight;

    /**
     * 最小距离设备(米)
     */
    @ExcelProperty("最小距离设备(米)")
    private Double minDistance;

    /**
     * 最大距离设备(米)
     */
    @ExcelProperty("最大距离设备(米)")
    private Double maxDistance;

    /**
     * 最小距离跑道(米)
     */
    @ExcelProperty("最小距离跑道(米)")
    private Double minRunwayDistance;

    /**
     * 最大距离跑道(米)
     */
    @ExcelProperty("最大距离跑道(米)")
    private Double maxRunwayDistance;

    /**
     * 最小方位角
     */
    @ExcelProperty("最小方位角")
    private Double minAzimuth;

    /**
     * 最大方位角
     */
    @ExcelProperty("最大方位角")
    private Double maxAzimuth;

    /**
     * 最小俯仰角
     */
    @ExcelProperty("最小俯仰角")
    private Double minPitch;

    /**
     * 最大俯仰角
     */
    @ExcelProperty("最大俯仰角")
    private Double maxPitch;

    /**
     * 最小目标回波幅度
     */
    @ExcelProperty("最小目标回波幅度")
    private Double minEchoAmplitude;

    /**
     * 最大目标回波幅度
     */
    @ExcelProperty("最大目标回波幅度")
    private Double maxEchoAmplitude;

    /**
     * 最小速度(m/s)
     */
    @ExcelProperty("最小速度(m/s)")
    private Double minSpeed;

    /**
     * 最大速度(m/s)
     */
    @ExcelProperty("最大速度(m/s)")
    private Double maxSpeed;

    /**
     * 最小信噪比(dB)
     */
    @ExcelProperty("最小信噪比(dB)")
    private Double minSnr;

    /**
     * 最大信噪比(dB)
     */
    @ExcelProperty("最大信噪比(dB)")
    private Double maxSnr;

    /**
     * 最小雷达截面积
     */
    @ExcelProperty("最小雷达截面积")
    private Double minRcs;

    /**
     * 最大雷达截面积
     */
    @ExcelProperty("最大雷达截面积")
    private Double maxRcs;

    /**
     * 推送状态,0未推送,1已推送
     */
    @IEnumsConverter(replace = {"0_未推送", "1_已推送"})
    @ExcelProperty(value = "推送状态", converter = IEnumsToStrConverter.class)
    private Integer pushStatus;

    /**
     * 可视目标批次号
     */
    @ExcelProperty("可视目标批次号")
    private String visualBatchNumber;

    /**
     * 可视设备编号
     */
    @ExcelProperty("可视设备编号")
    private String visualDevNum;
}