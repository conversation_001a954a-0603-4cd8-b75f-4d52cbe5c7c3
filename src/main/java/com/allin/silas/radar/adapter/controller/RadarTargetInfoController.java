package com.allin.silas.radar.adapter.controller;

import com.allin.silas.radar.adapter.dto.DelRadarTargetDto;
import com.allin.silas.radar.adapter.dto.RadarTargetMarkDto;
import com.allin.silas.radar.adapter.query.RadarTargetMergedInfoPageQuery;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoExportVo;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoPageVo;
import com.allin.silas.radar.app.service.RadarTargetInfoCommandService;
import com.allin.silas.radar.app.service.RadarTargetInfoQueryService;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 探测识别/雷达目标
 *
 * <AUTHOR>
 * @since 2025/7/12
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/radar/target")
public class RadarTargetInfoController {

    private final RadarTargetInfoQueryService infoQueryService;

    private final RadarTargetInfoCommandService infoCommandService;

    public RadarTargetInfoController(RadarTargetInfoQueryService infoQueryService,
                                     RadarTargetInfoCommandService infoCommandService) {
        this.infoQueryService = infoQueryService;
        this.infoCommandService = infoCommandService;
    }

    /**
     * 分页查询雷达目标
     */
    @GetMapping("/page")
    public Result<PageData<RadarTargetMergedInfoPageVo>> page(PageParam pageParam,
                                                              @Validated RadarTargetMergedInfoPageQuery query) {
        query.validateAndInit();
        return Result.ok(PageData.getInstance(infoQueryService.page(pageParam, query)));
    }

    /**
     * 导出雷达目标
     */
    @PostMapping("/export/excel")
    public void exportExcel(@Validated @RequestBody RadarTargetMergedInfoPageQuery query,
                            HttpServletResponse response) {
        query.validateAndInit();
        final List<RadarTargetMergedInfoExportVo> exportList = infoQueryService.list(query);
        FastExcelOperUtils.exportXlsx(response, exportList, RadarTargetMergedInfoExportVo.class, "雷达目标信息");
    }

    /**
     * 标记雷达目标类型
     */
    @PutMapping("/mark")
    public Result<Void> mark(@RequestBody @Validated RadarTargetMarkDto markDto) {
        return infoCommandService.mark(markDto) ? Result.ok() : Result.fail();
    }

    /**
     * 删除雷达目标
     */
    @DeleteMapping
    public Result<Void> del(@Validated @RequestBody DelRadarTargetDto delRadarTargetDto) {
        infoCommandService.del(delRadarTargetDto);
        return Result.ok();
    }
}
