package com.allin.silas.radar.app.manager;

import com.allin.silas.radar.adapter.dto.RadarTargetMarkDto;
import com.allin.silas.radar.adapter.query.RadarTargetMergedInfoPageQuery;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoExportVo;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoPageVo;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 雷达目标合并信息通用逻辑层接口
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
public interface RadarTargetMergedInfoManager {

    /**
     * 分页查询
     */
    Page<RadarTargetMergedInfoPageVo> page(PageParam pageParam, RadarTargetMergedInfoPageQuery query);

    /**
     * 列表查询
     */
    List<RadarTargetMergedInfoExportVo> list(RadarTargetMergedInfoPageQuery query);

    /**
     * 标记雷达目标类型
     */
    boolean mark(RadarTargetMarkDto markDto);
}
