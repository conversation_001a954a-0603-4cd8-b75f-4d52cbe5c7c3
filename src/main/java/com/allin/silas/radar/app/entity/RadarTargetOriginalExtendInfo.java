package com.allin.silas.radar.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 雷达目标扩展信息表
 */
@Data
@TableName(value = "radar_target_original_extend_info")
public class RadarTargetOriginalExtendInfo {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 雷达设备编号
     */
    @TableField(value = "dev_num")
    private String devNum;

    /**
     * 雷达设备类型
     */
    @TableField(value = "dev_type")
    private String devType;

    /**
     * 后端生成的目标批次号
     */
    @TableField(value = "backend_batch_number")
    private String backendBatchNumber;

    /**
     * 扩展数据
     */
    @TableField(value = "extend_data")
    private String extendData;
}