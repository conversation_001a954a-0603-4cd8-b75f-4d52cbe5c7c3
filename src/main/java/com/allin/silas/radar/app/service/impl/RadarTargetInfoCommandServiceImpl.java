package com.allin.silas.radar.app.service.impl;

import com.allin.silas.common.mybatis.config.MybatisDynamicTableNameHandler;
import com.allin.silas.radar.adapter.dto.DelRadarTargetDto;
import com.allin.silas.radar.adapter.dto.RadarTargetMarkDto;
import com.allin.silas.radar.app.entity.RadarTargetMergedInfo;
import com.allin.silas.radar.app.entity.RadarTargetOriginalInfo;
import com.allin.silas.radar.app.manager.RadarTargetMergedInfoManager;
import com.allin.silas.radar.app.service.RadarTargetInfoCommandService;
import com.allin.silas.radar.infra.repository.RadarTargetMergedInfoMapper;
import com.allin.silas.radar.infra.repository.RadarTargetOriginalInfoMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2025/7/12
 */
@Service
public class RadarTargetInfoCommandServiceImpl implements RadarTargetInfoCommandService {

    private final RadarTargetMergedInfoMapper mergedInfoMapper;

    private final RadarTargetOriginalInfoMapper originalInfoMapper;

    private final RadarTargetMergedInfoManager mergedInfoManager;

    public RadarTargetInfoCommandServiceImpl(RadarTargetMergedInfoMapper mergedInfoMapper,
                                             RadarTargetOriginalInfoMapper originalInfoMapper,
                                             RadarTargetMergedInfoManager mergedInfoManager) {
        this.mergedInfoMapper = mergedInfoMapper;
        this.originalInfoMapper = originalInfoMapper;
        this.mergedInfoManager = mergedInfoManager;
    }


    @Override
    public boolean mark(RadarTargetMarkDto markDto) {
        return mergedInfoManager.mark(markDto);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void del(DelRadarTargetDto delRadarTargetDto) {
        // 设置表名
        MybatisDynamicTableNameHandler.setSuffix(delRadarTargetDto.getDate());
        mergedInfoMapper.delete(Wrappers.lambdaQuery(RadarTargetMergedInfo.class)
                .in(RadarTargetMergedInfo::getBackendBatchNumber, delRadarTargetDto.getBatchNumbers()));
        originalInfoMapper.delete(Wrappers.lambdaQuery(RadarTargetOriginalInfo.class)
                .in(RadarTargetOriginalInfo::getBackendBatchNumber, delRadarTargetDto.getBatchNumbers()));
    }
}
