package com.allin.silas.radar.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 雷达目标和可视目标关联表
 */
@Data
@TableName(value = "radar_visual_target_relation")
public class RadarVisualTargetRelation {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 可视目标批次号
     */
    @TableField(value = "visual_batch_number")
    private String visualBatchNumber;

    /**
     * 可视设备编号
     */
    @TableField(value = "visual_dev_num")
    private String visualDevNum;

    /**
     * 雷达目标批次号
     */
    @TableField(value = "radar_batch_number")
    private String radarBatchNumber;

    /**
     * 雷达设备编号
     */
    @TableField(value = "radar_dev_num")
    private String radarDevNum;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 是否删除,0-否,1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}