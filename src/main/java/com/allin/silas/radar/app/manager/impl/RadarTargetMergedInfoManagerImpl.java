package com.allin.silas.radar.app.manager.impl;

import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.radar.adapter.dto.RadarTargetMarkDto;
import com.allin.silas.radar.adapter.query.RadarTargetMergedInfoPageQuery;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoExportVo;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoPageVo;
import com.allin.silas.radar.app.manager.RadarTargetMergedInfoManager;
import com.allin.silas.radar.infra.repository.RadarTargetMergedInfoMapper;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 雷达目标合并信息通用逻辑层接口实现
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
@Service
public class RadarTargetMergedInfoManagerImpl implements RadarTargetMergedInfoManager {

    private final RadarTargetMergedInfoMapper infoMapper;

    public RadarTargetMergedInfoManagerImpl(RadarTargetMergedInfoMapper infoMapper) {
        this.infoMapper = infoMapper;
    }

    @Override
    public Page<RadarTargetMergedInfoPageVo> page(PageParam pageParam, RadarTargetMergedInfoPageQuery query) {
        final String tableName = TableNameUtils.getRadarTargetMergedInfo(query.getStartTime());
        final Page<RadarTargetMergedInfoPageVo> page = pageParam.toPage();
        infoMapper.page(page, tableName, query);
        return page;
    }

    @Override
    public List<RadarTargetMergedInfoExportVo> list(RadarTargetMergedInfoPageQuery query) {
        final String tableName = TableNameUtils.getRadarTargetMergedInfo(query.getStartTime());
        return infoMapper.list(tableName, query);
    }

    @Override
    public synchronized boolean mark(RadarTargetMarkDto markDto) {
        final String tableName = TableNameUtils.getVisualTargetMergedInfo(markDto.getDate());
        return infoMapper.updateMark(tableName, markDto) > 0;
    }
}
