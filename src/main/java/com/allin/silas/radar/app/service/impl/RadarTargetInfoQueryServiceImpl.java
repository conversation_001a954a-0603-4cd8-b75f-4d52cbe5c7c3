package com.allin.silas.radar.app.service.impl;

import com.allin.silas.radar.adapter.query.RadarTargetMergedInfoPageQuery;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoExportVo;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoPageVo;
import com.allin.silas.radar.app.manager.RadarTargetMergedInfoManager;
import com.allin.silas.radar.app.service.RadarTargetInfoQueryService;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/12
 */
@Service
public class RadarTargetInfoQueryServiceImpl implements RadarTargetInfoQueryService {

    private final RadarTargetMergedInfoManager radarTargetMergedInfoManager;

    public RadarTargetInfoQueryServiceImpl(RadarTargetMergedInfoManager radarTargetMergedInfoManager) {
        this.radarTargetMergedInfoManager = radarTargetMergedInfoManager;
    }

    @Override
    public Page<RadarTargetMergedInfoPageVo> page(PageParam pageParam, RadarTargetMergedInfoPageQuery query) {
        return radarTargetMergedInfoManager.page(pageParam, query);
    }

    @Override
    public List<RadarTargetMergedInfoExportVo> list(RadarTargetMergedInfoPageQuery query) {
        return radarTargetMergedInfoManager.list(query);
    }
}
