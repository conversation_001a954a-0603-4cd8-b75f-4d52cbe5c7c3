package com.allin.silas.radar.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 雷达目标合并信息表
 */
@Data
@TableName(value = "radar_target_merged_info")
public class RadarTargetMergedInfo {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 雷达设备编号
     */
    @TableField(value = "dev_num")
    private String devNum;

    /**
     * 雷达设备类型
     */
    @TableField(value = "dev_type")
    private String devType;

    /**
     * 后端生成的目标批次号
     */
    @TableField(value = "backend_batch_number")
    private String backendBatchNumber;

    /**
     * 识别类型: 鸟群/鸟类/无人机/空飘物/违建/飞机/车辆/人/船
     */
    @TableField(value = "detect_type")
    private String detectType;

    /**
     * 危险等级: 1-5
     */
    @TableField(value = "danger_level")
    private Integer dangerLevel;

    /**
     * 碰撞可能性
     */
    @TableField(value = "collision_possibility")
    private Integer collisionPossibility;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    private LocalDateTime endTime;

    /**
     * 最小目标置信度
     */
    @TableField(value = "min_confidence_level")
    private Double minConfidenceLevel;

    /**
     * 最大目标置信度
     */
    @TableField(value = "max_confidence_level")
    private Double maxConfidenceLevel;

    /**
     * 标记类型: 鸟群/鸟类/无人机/空飘物/违建/飞机/车辆/人/船
     */
    @TableField(value = "mark_type")
    private String markType;

    /**
     * 最小目标高度
     */
    @TableField(value = "min_height")
    private Double minHeight;

    /**
     * 最大目标高度
     */
    @TableField(value = "max_height")
    private Double maxHeight;

    /**
     * 最小距离设备
     */
    @TableField(value = "min_distance")
    private Double minDistance;

    /**
     * 最大距离设备
     */
    @TableField(value = "max_distance")
    private Double maxDistance;

    /**
     * 最小距离跑道
     */
    @TableField(value = "min_runway_distance")
    private Double minRunwayDistance;

    /**
     * 最大距离跑道
     */
    @TableField(value = "max_runway_distance")
    private Double maxRunwayDistance;

    /**
     * 最小方位角
     */
    @TableField(value = "min_azimuth")
    private Double minAzimuth;

    /**
     * 最大方位角
     */
    @TableField(value = "max_azimuth")
    private Double maxAzimuth;

    /**
     * 最小俯仰角
     */
    @TableField(value = "min_pitch")
    private Double minPitch;

    /**
     * 最大俯仰角
     */
    @TableField(value = "max_pitch")
    private Double maxPitch;

    /**
     * 最小目标回波幅度
     */
    @TableField(value = "min_echo_amplitude")
    private Double minEchoAmplitude;

    /**
     * 最大目标回波幅度
     */
    @TableField(value = "max_echo_amplitude")
    private Double maxEchoAmplitude;

    /**
     * 最小速度
     */
    @TableField(value = "min_speed")
    private Double minSpeed;

    /**
     * 最大速度
     */
    @TableField(value = "max_speed")
    private Double maxSpeed;

    /**
     * 最小信噪比(dB)
     */
    @TableField(value = "min_snr")
    private Double minSnr;

    /**
     * 最大信噪比(dB)
     */
    @TableField(value = "max_snr")
    private Double maxSnr;

    /**
     * 最小雷达截面积
     */
    @TableField(value = "min_rcs")
    private Double minRcs;

    /**
     * 最大雷达截面积
     */
    @TableField(value = "max_rcs")
    private Double maxRcs;

    /**
     * 开始经度
     */
    @TableField(value = "start_longitude")
    private Double startLongitude;

    /**
     * 开始纬度
     */
    @TableField(value = "start_latitude")
    private Double startLatitude;

    /**
     * 结束经度(认定一个目标的经纬度)
     */
    @TableField(value = "end_longitude")
    private Double endLongitude;

    /**
     * 结束纬度(认定一个目标的经纬度)
     */
    @TableField(value = "end_latitude")
    private Double endLatitude;

    /**
     * 推送状态: 0-未推送 1-已推送
     */
    @TableField(value = "push_status")
    private Integer pushStatus;

    /**
     * 合并记录总数
     */
    @TableField(value = "merged_count")
    private Integer mergedCount;

    /**
     * 合并时间
     */
    @TableField(value = "merged_time")
    private LocalDateTime mergedTime;

    /**
     * 合并状态: 0-未合并 1-已合并
     */
    @TableField(value = "merged_status")
    private Integer mergedStatus;

    /**
     * 是否删除: 0-否 1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}