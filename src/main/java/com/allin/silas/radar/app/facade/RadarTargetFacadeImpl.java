package com.allin.silas.radar.app.facade;

import com.allin.silas.radar.adapter.dto.RadarTargetMarkDto;
import com.allin.silas.radar.app.entity.RadarVisualTargetRelation;
import com.allin.silas.radar.app.enums.RadarDetectAndMarkTypeEnums;
import com.allin.silas.radar.app.manager.RadarTargetMergedInfoManager;
import com.allin.silas.radar.client.RadarTargetFacade;
import com.allin.silas.radar.infra.repository.RadarVisualTargetRelationMapper;
import com.allin.view.base.enums.base.IEnums;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2025/7/14
 */
@Slf4j
@Service
public class RadarTargetFacadeImpl implements RadarTargetFacade {

    private final RadarVisualTargetRelationMapper relationMapper;

    private final RadarTargetMergedInfoManager radarTargetMergedInfoManager;

    public RadarTargetFacadeImpl(RadarVisualTargetRelationMapper relationMapper,
                                 RadarTargetMergedInfoManager radarTargetMergedInfoManager) {
        this.relationMapper = relationMapper;
        this.radarTargetMergedInfoManager = radarTargetMergedInfoManager;
    }

    private String isRelationVisual(String visualBatchNumber) {
        final RadarVisualTargetRelation radarVisualTargetRelation = relationMapper.selectOne(Wrappers
                .lambdaQuery(RadarVisualTargetRelation.class)
                .eq(RadarVisualTargetRelation::getVisualBatchNumber, visualBatchNumber));
        if (radarVisualTargetRelation == null) {
            return null;
        }
        return radarVisualTargetRelation.getRadarBatchNumber();
    }

    @Override
    public boolean syncVisualTargetType(String visualBatchNumber, LocalDate date, String visualTargetType) {
        final String radarBatchNumber = isRelationVisual(visualBatchNumber);
        if (radarBatchNumber == null) {
            return true;
        }
        if (visualTargetType.contains("鸟") && !RadarDetectAndMarkTypeEnums.BIRDS.compare(visualTargetType)) {
            visualTargetType = RadarDetectAndMarkTypeEnums.BIRD.getCode();
        }
        // 不属于雷达的标记类型不做同步处理
        if (!IEnums.contains(RadarDetectAndMarkTypeEnums.class, visualTargetType)) {
            log.warn("不支持的雷达标记类型: {}", visualTargetType);
            return true;
        }
        return radarTargetMergedInfoManager.mark(new RadarTargetMarkDto(date, radarBatchNumber, visualTargetType));
    }
}
