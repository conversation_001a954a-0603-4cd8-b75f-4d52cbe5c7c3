package com.allin.silas.radar.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 雷达目标原始信息表
 */
@Data
@TableName(value = "radar_target_original_info")
public class RadarTargetOriginalInfo {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 雷达设备编号
     */
    @TableField(value = "dev_num")
    private String devNum;

    /**
     * 雷达设备类型
     */
    @TableField(value = "dev_type")
    private String devType;

    /**
     * 后端生成的目标批次号
     */
    @TableField(value = "backend_batch_number")
    private String backendBatchNumber;

    /**
     * 前端设备发送的目标批次号
     */
    @TableField(value = "frontend_batch_number")
    private String frontendBatchNumber;

    /**
     * 识别类型: 鸟群/鸟类/无人机/空飘物/违建/飞机/车辆/人/船
     */
    @TableField(value = "detect_type")
    private String detectType;

    /**
     * 危险等级: 1-5
     */
    @TableField(value = "danger_level")
    private Integer dangerLevel;

    /**
     * 设备运行模式: 1-待机模式 2-周扫模式 3-扇扫模式
     */
    @TableField(value = "dev_run_model")
    private Integer devRunModel;

    /**
     * 目标置信度
     */
    @TableField(value = "confidence_level")
    private Double confidenceLevel;

    /**
     * 目标高度(米)
     */
    @TableField(value = "height")
    private Double height;

    /**
     * 方位角
     */
    @TableField(value = "azimuth")
    private Double azimuth;

    /**
     * 俯仰角
     */
    @TableField(value = "pitch")
    private Double pitch;

    /**
     * 目标回波幅度
     */
    @TableField(value = "echo_amplitude")
    private Double echoAmplitude;

    /**
     * 速度
     */
    @TableField(value = "speed")
    private Double speed;

    /**
     * 信噪比(dB)
     */
    @TableField(value = "snr")
    private Double snr;

    /**
     * 雷达截面积
     */
    @TableField(value = "rcs")
    private Double rcs;

    /**
     * 经度
     */
    @TableField(value = "longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @TableField(value = "latitude")
    private Double latitude;

    /**
     * 距离设备(米)
     */
    @TableField(value = "distance")
    private Double distance;

    /**
     * 距离跑道(米)
     */
    @TableField(value = "runway_distance")
    private Double runwayDistance;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 是否删除: 0-否 1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}