package com.allin.silas.radar.app.service;

import com.allin.silas.radar.adapter.query.RadarTargetMergedInfoPageQuery;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoExportVo;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoPageVo;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 雷达目标信息查询服务接口
 *
 * <AUTHOR>
 * @since 2025/7/12
 */
public interface RadarTargetInfoQueryService {

    /**
     * 分页查询
     */
    Page<RadarTargetMergedInfoPageVo> page(PageParam pageParam, RadarTargetMergedInfoPageQuery query);

    /**
     * 列表查询
     */
    List<RadarTargetMergedInfoExportVo> list(RadarTargetMergedInfoPageQuery query);
}
