package com.allin.silas.radar.app.service;

import com.allin.silas.radar.adapter.dto.DelRadarTargetDto;
import com.allin.silas.radar.adapter.dto.RadarTargetMarkDto;

/**
 * 雷达目标信息命令服务接口
 *
 * <AUTHOR>
 * @since 2025/7/12
 */
public interface RadarTargetInfoCommandService {

    /**
     * 标记雷达目标类型
     */
    boolean mark(RadarTargetMarkDto markDto);

    /**
     * 删除雷达目标
     */
    void del(DelRadarTargetDto delRadarTargetDto);
}
