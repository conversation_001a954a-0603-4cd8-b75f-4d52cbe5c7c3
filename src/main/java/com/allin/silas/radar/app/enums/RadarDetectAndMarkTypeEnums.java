package com.allin.silas.radar.app.enums;

import com.allin.view.base.enums.base.IEnums;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 雷达识别和标记类型枚举
 * 鸟群：5级
 * 鸟类：4级
 * 无人机：5级
 * 空飘物：5级
 * 违建：3级
 * 飞机：2级
 * 车辆：1级
 * 人：1级
 * 船：1级
 */
@Slf4j
public enum RadarDetectAndMarkTypeEnums implements IEnums {

    BIRDS("鸟群", 5),
    UAV("无人机", 5),
    BIRD("鸟类", 4),
    FLOAT_OBJECT("空飘物", 5),
    BACKGROUND("违建", 3),
    AIRCRAFT("飞机", 2),
    VEHICLE("车辆", 1),
    PERSON("人", 1),
    SHIP("船", 1);

    final String code;

    @Getter
    final Integer dangerLevel;

    RadarDetectAndMarkTypeEnums(String code, Integer dangerLevel) {
        this.code = code;
        this.dangerLevel = dangerLevel;
    }

    @Override
    public String getCode() {
        return code;
    }
}
