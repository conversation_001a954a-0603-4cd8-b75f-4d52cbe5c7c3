package com.allin.silas.radar.client;

import java.time.LocalDate;

/**
 * 雷达目标门面服务
 * <p>
 * 对外统一封装调用能力
 *
 * <AUTHOR>
 * @since 2025/6/24
 */
public interface RadarTargetFacade {

    /**
     * 同步可视目标类型
     *
     * @param visualBatchNumber 可视目标批次号
     * @param date              日期
     * @param visualTargetType  可视目标类型
     */
    boolean syncVisualTargetType(String visualBatchNumber, LocalDate date, String visualTargetType);
}
