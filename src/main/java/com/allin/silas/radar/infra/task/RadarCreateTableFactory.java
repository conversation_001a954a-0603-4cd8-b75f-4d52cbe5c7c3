package com.allin.silas.radar.infra.task;

import cn.hutool.core.io.IoUtil;
import com.allin.silas.common.infra.repository.CommonTableMapper;
import com.allin.silas.common.mybatis.config.DataBaseConfig;
import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.view.base.exception.service.ValidationFailureException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;

@Slf4j
@Component
public class RadarCreateTableFactory {

    private final CommonTableMapper commonTableMapper;

    private final JdbcTemplate jdbcTemplate;

    private final SqlSessionFactory sqlSessionFactory;

    public RadarCreateTableFactory(CommonTableMapper commonTableMapper,
                                   JdbcTemplate jdbcTemplate,
                                   SqlSessionFactory sqlSessionFactory) {
        this.commonTableMapper = commonTableMapper;
        this.jdbcTemplate = jdbcTemplate;
        this.sqlSessionFactory = sqlSessionFactory;
    }

    /**
     * 获取当前数据库id
     */
    private String getCurrentDatabaseId() {
        return sqlSessionFactory.getConfiguration().getDatabaseId();
    }

    /**
     * 合并信息表建表语句
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createRadarTargetMergedInfoTable(LocalDate date) {
        final String tableName = TableNameUtils.getRadarTargetMergedInfo(date);
        // 判断表是否已经存在
        if (commonTableMapper.existsTable(tableName) > 0) {
            return false;
        }
        final String currentDatabaseId = getCurrentDatabaseId();
        try {
            String sql = switch (currentDatabaseId) {
                case DataBaseConfig.POSTGRE_SQL -> {
                    // 读取类型映射配置文件
                    ClassPathResource detectTypeFile = new ClassPathResource("/db/ddl/postgresql/radar_target_merged_info.sql");
                    final String ddl = IoUtil.readUtf8(detectTypeFile.getInputStream());
                    yield ddl.replace("radar_target_merged_info", tableName);
                }
                default -> throw new ValidationFailureException("不支持的数据库类型");
            };
            jdbcTemplate.execute(sql);
        } catch (Exception e) {
            log.error("创建表执行失败", e);
            return false;
        }
        return true;
    }

    /**
     * 原始信息表建表语句
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createRadarTargetOriginalInfoTable(LocalDate date) {
        final String tableName = TableNameUtils.getRadarTargetOriginalInfo(date);
        // 判断表是否已经存在
        if (commonTableMapper.existsTable(tableName) > 0) {
            return false;
        }
        final String currentDatabaseId = getCurrentDatabaseId();
        try {
            String sql = switch (currentDatabaseId) {
                case DataBaseConfig.POSTGRE_SQL -> {
                    // 读取类型映射配置文件
                    ClassPathResource detectTypeFile = new ClassPathResource("/db/ddl/postgresql/radar_target_original_info.sql");
                    final String ddl = IoUtil.readUtf8(detectTypeFile.getInputStream());
                    yield ddl.replace("radar_target_original_info", tableName);
                }
                default -> throw new ValidationFailureException("不支持的数据库类型");
            };
            jdbcTemplate.execute(sql);
        } catch (Exception e) {
            log.error("创建表执行失败", e);
            return false;
        }
        return true;
    }

    /**
     * 扩展信息表建表语句
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createRadarTargetOriginalExtendInfoTable(LocalDate date) {
        final String tableName = TableNameUtils.getRadarTargetOriginalExtendInfo(date);
        // 判断表是否已经存在
        if (commonTableMapper.existsTable(tableName) > 0) {
            return false;
        }
        final String currentDatabaseId = getCurrentDatabaseId();
        try {
            String sql = switch (currentDatabaseId) {
                case DataBaseConfig.POSTGRE_SQL -> {
                    // 读取类型映射配置文件
                    ClassPathResource detectTypeFile = new ClassPathResource("/db/ddl/postgresql/radar_target_original_extend_info.sql");
                    final String ddl = IoUtil.readUtf8(detectTypeFile.getInputStream());
                    yield ddl.replace("radar_target_original_extend_info", tableName);
                }
                default -> throw new ValidationFailureException("不支持的数据库类型");
            };
            jdbcTemplate.execute(sql);
        } catch (Exception e) {
            log.error("创建表执行失败", e);
            return false;
        }
        return true;
    }

}