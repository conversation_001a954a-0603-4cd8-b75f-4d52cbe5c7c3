package com.allin.silas.radar.infra.repository;

import com.allin.silas.radar.adapter.dto.RadarTargetMarkDto;
import com.allin.silas.radar.adapter.query.RadarTargetMergedInfoPageQuery;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoExportVo;
import com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoPageVo;
import com.allin.silas.radar.app.entity.RadarTargetMergedInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RadarTargetMergedInfoMapper extends BaseMapper<RadarTargetMergedInfo> {
    /**
     * 分页查询
     */
    Page<RadarTargetMergedInfoPageVo> page(Page<RadarTargetMergedInfoPageVo> page,
                                           @Param("tableName") String tableName,
                                           @Param("query") RadarTargetMergedInfoPageQuery query);

    /**
     * 列表查询
     */
    List<RadarTargetMergedInfoExportVo> list(@Param("tableName") String tableName,
                                             @Param("query") RadarTargetMergedInfoPageQuery query);

    /**
     * 修改标记类型
     */
    Integer updateMark(@Param("tableName") String tableName,
                       @Param("mark") RadarTargetMarkDto markDto);
}