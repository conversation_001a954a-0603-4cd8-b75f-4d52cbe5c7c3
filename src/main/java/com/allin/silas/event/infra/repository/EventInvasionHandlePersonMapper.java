package com.allin.silas.event.infra.repository;

import com.allin.silas.event.app.entity.EventInvasionHandlePerson;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface EventInvasionHandlePersonMapper extends BaseMapper<EventInvasionHandlePerson> {

    /**
     * 根据事件id列表查询处置人员
     */
    List<EventInvasionHandlePerson> listByEventInvasionId(List<String> eventInvasionId);
}