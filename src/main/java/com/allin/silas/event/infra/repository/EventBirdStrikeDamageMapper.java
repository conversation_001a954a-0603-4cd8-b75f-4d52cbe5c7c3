package com.allin.silas.event.infra.repository;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.event.adapter.dto.AddEventBirdStrikeDamageDto;
import com.allin.silas.event.app.entity.EventBirdStrikeDamage;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-07-25 16:04:15
 */
@Mapper
public interface EventBirdStrikeDamageMapper extends BaseMapper<EventBirdStrikeDamage> {

    default void save(List<AddEventBirdStrikeDamageDto> dtoList, String eventBirdStrikeId) {
        List<EventBirdStrikeDamage> list = dtoList.stream().map(dto -> {
            EventBirdStrikeDamage eventBirdStrikeDamage = BeanUtil.copyProperties(dto, EventBirdStrikeDamage.class);
            eventBirdStrikeDamage.setEventBirdStrikeId(eventBirdStrikeId);
            return eventBirdStrikeDamage;
        }).toList();

        insert(list);
    }

    default List<EventBirdStrikeDamage> info(String eventBirdStrikeId) {
        return selectList(Wrappers.lambdaQuery(EventBirdStrikeDamage.class)
                .eq(EventBirdStrikeDamage::getEventBirdStrikeId, eventBirdStrikeId));
    }

    default Map<String, List<EventBirdStrikeDamage>> mapInfo(List<String> eventBirdStrikeIds) {
        List<EventBirdStrikeDamage> eventBirdStrikeDamageList = selectList(new LambdaQueryWrapper<EventBirdStrikeDamage>()
                .in(EventBirdStrikeDamage::getEventBirdStrikeId, eventBirdStrikeIds));
        return CollectionUtils.isNotEmpty(eventBirdStrikeDamageList) ? eventBirdStrikeDamageList.stream()
                .collect(Collectors.groupingBy(EventBirdStrikeDamage::getEventBirdStrikeId, Collectors.toList())) : Collections.emptyMap();
    }
}

