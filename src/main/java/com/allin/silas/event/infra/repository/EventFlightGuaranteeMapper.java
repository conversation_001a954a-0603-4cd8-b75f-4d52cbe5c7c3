package com.allin.silas.event.infra.repository;

import com.allin.silas.event.adapter.query.EventFlightGuaranteeQuery;
import com.allin.silas.event.adapter.vo.EventFlightGuaranteePageVo;
import com.allin.silas.event.app.entity.EventFlightGuarantee;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EventFlightGuaranteeMapper extends BaseMapper<EventFlightGuarantee> {
    /**
     * 分页查询
     */
    Page<EventFlightGuaranteePageVo> page(Page<EventFlightGuaranteePageVo> page,
                                          @Param("tableName") String visualTargetMergedInfoTableName,
                                          @Param("query") EventFlightGuaranteeQuery query);
}