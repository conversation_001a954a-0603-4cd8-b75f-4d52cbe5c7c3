package com.allin.silas.event.infra.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;

/**
 * 定时建表
 *
 * <AUTHOR>
 * @since 2025/7/28
 */
@Slf4j
@Component
public class EventCreateTableTask {

    private final EventCreateTableFactory createTableFactory;

    public EventCreateTableTask(EventCreateTableFactory createTableFactory) {
        this.createTableFactory = createTableFactory;
    }


    /**
     * 每隔1小时尝试创建后一天的表，另外判断当天的表存不存在，不存在也创建
     */
    @Scheduled(cron = "0 0 */1 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void createTable() {
        final LocalDate tomorrow = LocalDate.now().plusDays(1);
        final LocalDate today = LocalDate.now();

        // 创建今天的合并信息表
        if (createTableFactory.createAdsBTable(today)) {
            log.info("创建今天的AdsB信息表成功，日期：{}", today);
        }
        // 创建明天的合并信息表
        if (createTableFactory.createAdsBTable(tomorrow)) {
            log.info("创建明天的AdsB合并信息表成功，日期：{}", tomorrow);
        }
    }
}
