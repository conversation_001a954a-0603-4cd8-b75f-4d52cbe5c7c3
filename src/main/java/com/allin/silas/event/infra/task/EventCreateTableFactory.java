package com.allin.silas.event.infra.task;

import cn.hutool.core.io.IoUtil;
import com.allin.silas.common.infra.repository.CommonTableMapper;
import com.allin.silas.common.mybatis.config.DataBaseConfig;
import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.view.base.exception.service.ValidationFailureException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;

/**
 * 事件模块建表工厂
 *
 * <AUTHOR>
 * @since 2025/7/28
 */
@Slf4j
@Component
public class EventCreateTableFactory {

    private final CommonTableMapper commonTableMapper;

    private final JdbcTemplate jdbcTemplate;

    private final SqlSessionFactory sqlSessionFactory;

    public EventCreateTableFactory(CommonTableMapper commonTableMapper,
                                   JdbcTemplate jdbcTemplate,
                                   SqlSessionFactory sqlSessionFactory) {
        this.commonTableMapper = commonTableMapper;
        this.jdbcTemplate = jdbcTemplate;
        this.sqlSessionFactory = sqlSessionFactory;
    }

    /**
     * 获取当前数据库id
     */
    private String getCurrentDatabaseId() {
        return sqlSessionFactory.getConfiguration().getDatabaseId();
    }

    /**
     * AdsB信息表建表语句
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createAdsBTable(LocalDate date) {
        final String tableName = TableNameUtils.getAdsBInfo(date);
        // 判断表是否已经存在
        if (commonTableMapper.existsTable(tableName) > 0) {
            return false;
        }
        final String currentDatabaseId = getCurrentDatabaseId();
        try {
            String sql = switch (currentDatabaseId) {
                case DataBaseConfig.POSTGRE_SQL -> {
                    // 读取类型映射配置文件
                    ClassPathResource detectTypeFile = new ClassPathResource("/db/ddl/postgresql/event_ads_b.sql");
                    final String ddl = IoUtil.readUtf8(detectTypeFile.getInputStream());
                    yield ddl.replace("event_ads_b", tableName);
                }
                default -> throw new ValidationFailureException("不支持的数据库类型");
            };
            jdbcTemplate.execute(sql);
        } catch (Exception e) {
            log.error("创建表执行失败", e);
            return false;
        }
        return true;
    }
}