package com.allin.silas.event.infra.task;

import com.allin.silas.common.infra.repository.CommonTableMapper;
import com.allin.silas.common.util.database.TableNameUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;

/**
 * 定时删表
 *
 * <AUTHOR>
 * @since 2025/7/28
 */
@Slf4j
@Component
public class EventDropTableTask {

    private final CommonTableMapper commonTableMapper;

    public EventDropTableTask(CommonTableMapper commonTableMapper) {
        this.commonTableMapper = commonTableMapper;
    }


    /**
     * 每隔2小时尝试删除3个月之前的表
     */
    @Scheduled(cron = "0 0 */2 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void createTable() {
        final LocalDate threeMonthsAgo = LocalDate.now().minusMonths(3);
        String tableName = TableNameUtils.getAdsBInfo(threeMonthsAgo);
        commonTableMapper.dropTable(tableName);
    }
}
