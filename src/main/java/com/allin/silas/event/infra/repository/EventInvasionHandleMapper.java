package com.allin.silas.event.infra.repository;

import com.allin.silas.event.adapter.query.EventInvasionPageQuery;
import com.allin.silas.event.adapter.vo.EventInvasionPageVo;
import com.allin.silas.event.app.entity.EventInvasionHandle;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface EventInvasionHandleMapper extends BaseMapper<EventInvasionHandle> {

    /**
     * 分页查询
     */
    Page<EventInvasionPageVo> page(Page<EventInvasionPageVo> page,
                                   @Param("query") EventInvasionPageQuery query);

    /**
     * 分页查询与指定用户有关的处置事件
     *
     * @param createdBy 创建人用户id
     */
    Page<EventInvasionPageVo> pageByUserId(Page<EventInvasionPageVo> page,
                                           @Param("query") EventInvasionPageQuery query,
                                           @Param("createdBy") String createdBy);

    /**
     * 统计危险等级分布
     */
    List<Map<String, Object>> statisticsDangerLevel(@Param("query") EventInvasionPageQuery query);

    /**
     * 统计处置状态分布
     */
    List<Map<String, Object>> statisticsHandleStatus(@Param("query") EventInvasionPageQuery query);

    /**
     * 统计危险等级分布（按用户ID过滤）
     */
    List<Map<String, Object>> statisticsDangerLevelByUserId(@Param("query") EventInvasionPageQuery query,
                                                            @Param("createdBy") String createdBy);

    /**
     * 统计处置状态分布（按用户ID过滤）
     */
    List<Map<String, Object>> statisticsHandleStatusByUserId(@Param("query") EventInvasionPageQuery query,
                                                             @Param("createdBy") String createdBy);

    /**
     * 待用户处置的事件数量
     */
    Integer unhandled(@Param("userId") String userId,
                      @Param("projectId") String projectId);
}