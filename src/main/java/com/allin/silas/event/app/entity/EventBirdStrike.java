package com.allin.silas.event.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 鸟击事件
 *
 * <AUTHOR>
 */
@Data
@TableName("event_bird_strike")
public class EventBirdStrike {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 事件名称
     */
    @TableField("event_name")
    private String eventName;

    /**
     * 发生时间
     */
    @TableField("occurrence_time")
    private LocalDateTime occurrenceTime;

    /**
     * 事发地点
     */
    @TableField("occurrence_location")
    private String occurrenceLocation;

    /**
     * 航空运输企业
     */
    @TableField("airline_company")
    private String airlineCompany;

    /**
     * 航空器型号
     */
    @TableField("aircraft_model")
    private String aircraftModel;

    /**
     * 航空器注册号
     */
    @TableField("aircraft_registration")
    private String aircraftRegistration;

    /**
     * 航班号
     */
    @TableField("flight_number")
    private String flightNumber;

    /**
     * 接报时间
     */
    @TableField("report_time")
    private LocalDateTime reportTime;

    /**
     * 发生时段 1夜晚 2黄昏 3黎明 4白天
     */
    @TableField("occurrence_period")
    private Integer occurrencePeriod;

    /**
     * 起飞机场
     */
    @TableField("departure_airport")
    private String departureAirport;

    /**
     * 着陆机场
     */
    @TableField("landing_airport")
    private String landingAirport;

    /**
     * 撞击残留物
     */
    @TableField("impact_residue")
    private String impactResidue;

    /**
     * 是否已采集残留物或样本 1是 0否
     */
    @TableField("residue_collected")
    private Integer residueCollected;

    /**
     * 是否送检 1是 0否
     */
    @TableField("residue_sent_for_testing")
    private Integer residueSentForTesting;

    /**
     * 鉴定机构名称
     */
    @TableField("testing_agency")
    private String testingAgency;

    /**
     * 对飞行的影响 1无 2中断起飞 3预防性着陆 4发动机停车 5航班延误 6航班取消 7换机
     */
    @TableField("flight_impact")
    private Integer flightImpact;

    /**
     * 离地高度
     */
    @TableField("altitude")
    private String altitude;

    /**
     * 指示空速
     */
    @TableField("indicated_airspeed")
    private String indicatedAirspeed;

    /**
     * 飞行阶段 1停靠 2滑行 3起飞滑跑 4爬升 5航路飞行 6下降 7进近 8着陆滑跑 9不详
     */
    @TableField("flight_phase")
    private Integer flightPhase;

    /**
     * 天空情况 1多云 2少云 3无云
     */
    @TableField("sky_condition")
    private Integer skyCondition;

    /**
     * 降水情况 1雾 2雨 3雪 4无
     */
    @TableField("precipitation")
    private Integer precipitation;

    /**
     * 撞击物种
     */
    @TableField("impact_species")
    private String impactSpecies;

    /**
     * 物种体型 1大 2中 3小 4无
     */
    @TableField("species_size")
    private Integer speciesSize;

    /**
     * 看到数量
     */
    @TableField("observed_quantity")
    private Integer observedQuantity;

    /**
     * 击中数量
     */
    @TableField("actual_impact_quantity")
    private Integer actualImpactQuantity;

    /**
     * 修理或替换的损失
     */
    @TableField("repair_cost")
    private String repairCost;

    /**
     * 其他估算损失
     */
    @TableField("other_estimated_loss")
    private String otherEstimatedLoss;

    /**
     * 飞行机组被告知鸟类或其他活动 1是 0否
     */
    @TableField("crew_informed")
    private Integer crewInformed;

    /**
     * 飞行机组通报空管部门 1是 0否
     */
    @TableField("atc_notified")
    private Integer atcNotified;

    /**
     * 鸟击航空器事件 1是 0否
     */
    @TableField("is_bird_strike_event")
    private Integer isBirdStrikeEvent;

    /**
     * 航空器起降方向
     */
    @TableField("runway_direction")
    private String runwayDirection;

    /**
     * 后续跑道检查情况
     */
    @TableField("runway_check_status")
    private String runwayCheckStatus;

    /**
     * 是否机场责任范围 1是 0否
     */
    @TableField("airport_responsibility")
    private Integer airportResponsibility;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 附件
     */
    @TableField("attachment")
    private String attachment;

    /**
     * 项目ID
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除 0否 1是
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 停场时间
     */
    @TableField("downtime_hours")
    private Integer downtimeHours;
}

