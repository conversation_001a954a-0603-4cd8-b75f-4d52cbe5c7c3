package com.allin.silas.event.app.manager.impl;

import com.allin.silas.event.app.entity.EventInvasionHandlePerson;
import com.allin.silas.event.app.manager.EventInvasionHandlePersonManager;
import com.allin.silas.event.infra.repository.EventInvasionHandlePersonMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 入侵事件处置人员通用逻辑层实现
 *
 * <AUTHOR>
 * @since 2025/7/8
 */
@Service
public class EventInvasionHandlePersonManagerImpl implements EventInvasionHandlePersonManager {

    private final EventInvasionHandlePersonMapper handlePersonMapper;

    public EventInvasionHandlePersonManagerImpl(EventInvasionHandlePersonMapper handlePersonMapper) {
        this.handlePersonMapper = handlePersonMapper;
    }

    @Override
    public boolean edit(String eventInvasionId, List<EventInvasionHandlePerson> handlePersons) {
        int count = handlePersonMapper.delete(Wrappers.lambdaQuery(EventInvasionHandlePerson.class)
                .eq(EventInvasionHandlePerson::getEventInvasionId, eventInvasionId));
        count += handlePersonMapper.insert(handlePersons).size();
        return count > 0;
    }
}
