package com.allin.silas.event.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 入侵事件处置信息表
 */
@Data
@Accessors(chain = true)
@TableName(value = "event_invasion_handle")
public class EventInvasionHandle {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 入侵事件ID
     */
    @TableField(value = "event_invasion_id")
    private String eventInvasionId;

    /**
     * 处置时间
     */
    @TableField(value = "handle_time")
    private LocalDateTime handleTime;

    /**
     * 处置措施
     */
    @TableField(value = "handle_method")
    private String handleMethod;

    /**
     * 猎枪耗弹数量（仅在措施为猎枪时填写）
     */
    @TableField(value = "shotgun_ammo_count")
    private Integer shotgunAmmoCount;

    /**
     * 钝雷弹耗弹数量（仅在措施为钝雷弹时填写）
     */
    @TableField(value = "thunder_ammo_count")
    private Integer thunderAmmoCount;

    /**
     * 处置效果
     */
    @TableField(value = "handle_result")
    private String handleResult;

    /**
     * 是否影响航班：0未影响、1已影响
     */
    @TableField(value = "affect_flight")
    private Integer affectFlight;

    /**
     * 是否通报空管：0未通报、1已通报
     */
    @TableField(value = "notify_atc")
    private Integer notifyAtc;

    /**
     * 是否通报公安机关：0未通报、1已通报
     */
    @TableField(value = "notify_police")
    private Integer notifyPolice;

    /**
     * 附件id
     */
    @TableField(value = "attachment_id")
    private String attachmentId;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}