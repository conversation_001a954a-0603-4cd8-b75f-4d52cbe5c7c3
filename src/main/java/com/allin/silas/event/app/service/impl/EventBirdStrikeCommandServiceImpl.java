package com.allin.silas.event.app.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.event.adapter.dto.AddEventBirdStrikeDto;
import com.allin.silas.event.app.entity.EventBirdStrike;
import com.allin.silas.event.app.service.EventBirdStrikeCommandService;
import com.allin.silas.event.infra.repository.EventBirdStrikeDamageMapper;
import com.allin.silas.event.infra.repository.EventBirdStrikeMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @since 2025-07-25 13:48:04
 **/
@Service
public class EventBirdStrikeCommandServiceImpl implements EventBirdStrikeCommandService {

    private final EventBirdStrikeMapper eventBirdStrikeMapper;

    private final EventBirdStrikeDamageMapper eventBirdStrikeDamageMapper;

    public EventBirdStrikeCommandServiceImpl(EventBirdStrikeMapper eventBirdStrikeMapper,
                                             EventBirdStrikeDamageMapper eventBirdStrikeDamageMapper) {
        this.eventBirdStrikeMapper = eventBirdStrikeMapper;
        this.eventBirdStrikeDamageMapper = eventBirdStrikeDamageMapper;
    }

    /**
     * 新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AddEventBirdStrikeDto dto){
        EventBirdStrike eventBirdStrike = BeanUtil.copyProperties(dto, EventBirdStrike.class);
        eventBirdStrikeMapper.insert(eventBirdStrike);
        // 新增损伤信息
        eventBirdStrikeDamageMapper.save(dto.getEventBirdStrikeDamages(), eventBirdStrike.getId());
    }

    /**
     * 删除
     */
    @Override
    public void delete(String id) {
        eventBirdStrikeMapper.update(Wrappers.lambdaUpdate(EventBirdStrike.class)
                .eq(EventBirdStrike::getId, id)
                .set(EventBirdStrike::getIsDeleted, 1)
                .set(EventBirdStrike::getUpdatedBy, SecurityContextHolder.getUserId())
                .set(EventBirdStrike::getUpdatedTime, LocalDateTime.now()));
    }
}

