package com.allin.silas.event.app.facade;

import com.allin.silas.event.app.entity.EventInvasion;
import com.allin.silas.event.app.entity.EventInvasionHandle;
import com.allin.silas.event.client.EventInvasionFacade;
import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import com.allin.silas.event.client.dto.TargetAutoPushEventInvasionDto;
import com.allin.silas.event.client.dto.TargetManualPushEventInvasionDto;
import com.allin.silas.event.client.enums.EventInvasionTypeEnums;
import com.allin.silas.event.infra.repository.EventInvasionHandleMapper;
import com.allin.silas.event.infra.repository.EventInvasionHandlePersonMapper;
import com.allin.silas.event.infra.repository.EventInvasionMapper;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 入侵处置事件服务门面
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
@Service
public class EventInvasionFacadeImpl implements EventInvasionFacade {

    private final EventInvasionMapper eventInvasionMapper;

    private final EventInvasionHandleMapper eventInvasionHandleMapper;

    private final EventInvasionHandlePersonMapper eventInvasionHandlePersonMapper;

    public EventInvasionFacadeImpl(EventInvasionMapper eventInvasionMapper,
                                   EventInvasionHandleMapper eventInvasionHandleMapper,
                                   EventInvasionHandlePersonMapper eventInvasionHandlePersonMapper) {
        this.eventInvasionMapper = eventInvasionMapper;
        this.eventInvasionHandleMapper = eventInvasionHandleMapper;
        this.eventInvasionHandlePersonMapper = eventInvasionHandlePersonMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean targetAutoPushEvent(@Validated TargetAutoPushEventInvasionDto dto) {
        final boolean exists = eventInvasionMapper.exists(Wrappers.lambdaQuery(EventInvasion.class)
                .eq(EventInvasion::getSourceId, dto.getSourceId())
                .eq(EventInvasion::getSource, 0));
        if (exists) {
            throw new ValidationFailureException("请勿重复推送目标检测事件");
        }
        final EventInvasion eventInvasion = new EventInvasion();
        BeanUtils.copyProperties(dto, eventInvasion);
        // 默认为目标检测
        eventInvasion.setSource(0);
        // 默认为自动推送
        eventInvasion.setPushType(0);
        eventInvasion.setHandleStatus(0);
        // 空飘物和无人机默认为5
        if (EventInvasionTypeEnums.AIR_OBJECT.compare(eventInvasion.getEventType())
            || EventInvasionTypeEnums.UAV.compare(eventInvasion.getEventType())) {
            eventInvasion.setDangerLevel(5);
        }
        // 新增入侵事件
        return eventInvasionMapper.insert(eventInvasion) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean targetManualPushEvent(@Validated TargetManualPushEventInvasionDto dto) {
        final boolean exists = eventInvasionMapper.exists(Wrappers.lambdaQuery(EventInvasion.class)
                .eq(EventInvasion::getSourceId, dto.getSourceId())
                .eq(EventInvasion::getSource, 0));
        if (exists) {
            throw new ValidationFailureException("请勿重复推送目标检测事件");
        }
        final EventInvasion eventInvasion = new EventInvasion();
        BeanUtils.copyProperties(dto, eventInvasion);
        switch (dto.getEventType()) {
            case "空飘物" -> eventInvasion.setEventType(1);
            case "无人机" -> eventInvasion.setEventType(2);
            default -> {
                // 如果为鸟推送到事件则鸟情
                if (dto.getEventType().contains("鸟")) {
                    eventInvasion.setEventType(0);
                } else {
                    throw new ValidationFailureException("事件类型错误");
                }
            }
        }
        // 默认为目标检测
        eventInvasion.setSource(0);
        // 默认为自动推送
        eventInvasion.setPushType(1);
        // 手动推送默认为已处置
        eventInvasion.setHandleStatus(1);
        // 空飘物和无人机默认为5
        if (EventInvasionTypeEnums.AIR_OBJECT.compare(eventInvasion.getEventType())
            || EventInvasionTypeEnums.UAV.compare(eventInvasion.getEventType())) {
            eventInvasion.setDangerLevel(5);
        }

        final var handleEventInvasionDto = dto.getHandleEventInvasionDto();
        // 新增入侵事件
        if (eventInvasionMapper.insert(eventInvasion) > 0) {
            final EventInvasionHandle eventInvasionHandle = handleEventInvasionDto
                    .toEventInvasionHandle(eventInvasion.getId());
            eventInvasionHandleMapper.insert(eventInvasionHandle);
            eventInvasionHandlePersonMapper.insert(handleEventInvasionDto.toPersons(eventInvasion.getId()));
            return true;
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean reportPushEvent(ReportPushEventInvasionDto dto) {
        final boolean exists = eventInvasionMapper.exists(Wrappers.lambdaQuery(EventInvasion.class)
                .eq(EventInvasion::getSourceId, dto.getSourceId())
                .eq(EventInvasion::getSource, 1));
        if (exists) {
            throw new ValidationFailureException("请勿重复上报入侵事件");
        }
        final EventInvasion eventInvasion = new EventInvasion();
        BeanUtils.copyProperties(dto, eventInvasion);
        // 默认为信息上报
        eventInvasion.setSource(1);
        // 默认为自动推送
        eventInvasion.setPushType(1);
        final var handleEventInvasionDto = dto.getHandleEventInvasionDto();
        // 判断是否已经填写处置信息
        if (handleEventInvasionDto.getHandleTime() != null) {
            eventInvasion.setHandleStatus(1);
        } else {
            eventInvasion.setHandleStatus(0);
        }
        // 空飘物和无人机默认为5
        if (EventInvasionTypeEnums.AIR_OBJECT.compare(eventInvasion.getEventType())
            || EventInvasionTypeEnums.UAV.compare(eventInvasion.getEventType())) {
            eventInvasion.setDangerLevel(5);
        }
        // 新增入侵事件
        if (eventInvasionMapper.insert(eventInvasion) > 0) {
            // 已经填写处置信息
            if (eventInvasion.getHandleStatus() == 1) {
                final EventInvasionHandle eventInvasionHandle = handleEventInvasionDto.toEventInvasionHandle(eventInvasion.getId());
                eventInvasionHandleMapper.insert(eventInvasionHandle);
            }
            eventInvasionHandlePersonMapper.insert(handleEventInvasionDto
                    .toPersons(eventInvasion.getId()));
            return true;
        }
        return false;
    }
}
