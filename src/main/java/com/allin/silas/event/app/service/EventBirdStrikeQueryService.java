package com.allin.silas.event.app.service;


import com.allin.silas.event.adapter.query.EventBirdStrikeQuery;
import com.allin.silas.event.adapter.vo.EventBirdStrikeVo;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;

/**
 * <AUTHOR>
 * @since 2025-07-25 13:48:04
 **/
public interface EventBirdStrikeQueryService {
    /**
     * 分页查询
     */
    PageData<EventBirdStrikeVo> page(PageParam pageParam, EventBirdStrikeQuery query);

    /**
     * 详情
     */
    EventBirdStrikeVo info(String id);
}

