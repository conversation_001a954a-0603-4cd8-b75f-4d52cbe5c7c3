package com.allin.silas.event.app.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.EnumUtil;
import com.allin.silas.common.util.QueryWrapperUtils;
import com.allin.silas.event.adapter.query.EventBirdStrikeQuery;
import com.allin.silas.event.adapter.vo.EventBirdStrikeVo;
import com.allin.silas.event.app.entity.EventBirdStrike;
import com.allin.silas.event.app.entity.EventBirdStrikeDamage;
import com.allin.silas.event.app.enums.EventBirdStrikeDamageImpactPartEnums;
import com.allin.silas.event.app.enums.EventBirdStrikeDamageLevelEnums;
import com.allin.silas.event.app.service.EventBirdStrikeQueryService;
import com.allin.silas.event.infra.repository.EventBirdStrikeDamageMapper;
import com.allin.silas.event.infra.repository.EventBirdStrikeMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-07-25 13:48:04
 **/
@Service
public class EventBirdStrikeQueryServiceImpl implements EventBirdStrikeQueryService {

    private final EventBirdStrikeMapper eventBirdStrikeMapper;

    private final EventBirdStrikeDamageMapper eventBirdStrikeDamageMapper;

    public EventBirdStrikeQueryServiceImpl(EventBirdStrikeMapper eventBirdStrikeMapper,
                                           EventBirdStrikeDamageMapper eventBirdStrikeDamageMapper) {
        this.eventBirdStrikeMapper = eventBirdStrikeMapper;
        this.eventBirdStrikeDamageMapper = eventBirdStrikeDamageMapper;
    }

    @Override
    public PageData<EventBirdStrikeVo> page(PageParam pageParam, EventBirdStrikeQuery query) {
        final Page<EventBirdStrike> page = pageParam.toPage();
        LambdaQueryWrapper<EventBirdStrike> wrapper = getEventBirdStrikeLambdaQueryWrapper(query);
        QueryWrapperUtils.buildDateRangeQuery(wrapper, query.getOccurrenceStartTime(), query.getOccurrenceEndTime(), EventBirdStrike::getOccurrenceTime);
        eventBirdStrikeMapper.selectPage(page, wrapper);

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageData.empty();
        }

        List<EventBirdStrikeVo> list = page.getRecords().stream().map(entity -> BeanUtil.copyProperties(entity, EventBirdStrikeVo.class)).toList();
        // 获取损伤信息
        Map<String, List<EventBirdStrikeDamage>> damageMap = eventBirdStrikeDamageMapper.mapInfo(list.stream().map(EventBirdStrikeVo::getId).toList());
        list.stream()
                .filter(vo -> damageMap.containsKey(vo.getId()))
                .forEach(vo -> {
                    List<EventBirdStrikeDamage> eventBirdStrikeDamageList = damageMap.get(vo.getId());
                    vo.setEventBirdStrikeDamageList(eventBirdStrikeDamageList);
                    // 多条损伤组成一起
                    vo.setEventBirdStrikeDamageListDesc(eventBirdStrikeDamageList.stream().map(damage -> {
                        String impactPart = EnumUtil.getFieldBy(EventBirdStrikeDamageImpactPartEnums::getDesc, EventBirdStrikeDamageImpactPartEnums::getCode, damage.getImpactPart());
                        String damageLevel = EnumUtil.getFieldBy(EventBirdStrikeDamageLevelEnums::getDesc, EventBirdStrikeDamageLevelEnums::getCode, damage.getDamageLevel());
                        return impactPart + StrPool.COLON + damageLevel + ";";
                    }).collect(Collectors.joining()));
                });

        return PageData.getInstance(page, list);
    }

    private LambdaQueryWrapper<EventBirdStrike> getEventBirdStrikeLambdaQueryWrapper(EventBirdStrikeQuery query) {
        return Wrappers.lambdaQuery(EventBirdStrike.class)
                .eq(EventBirdStrike::getProjectId, SecurityContextHolder.getProjectId())
                .eq(EventBirdStrike::getIsDeleted, 0)
                .eq(StringUtils.isNotBlank(query.getFlightNumber()), EventBirdStrike::getFlightNumber, query.getFlightNumber())
                .eq(Objects.nonNull(query.getOccurrencePeriod()), EventBirdStrike::getOccurrencePeriod, query.getOccurrencePeriod())
                .like(StringUtils.isNotBlank(query.getEventName()), EventBirdStrike::getEventName, query.getEventName())
                .in(CollectionUtils.isNotEmpty(query.getAirlineCompany()), EventBirdStrike::getAirlineCompany, query.getAirlineCompany())
                .orderByDesc(EventBirdStrike::getOccurrenceTime);
    }

    @Override
    public EventBirdStrikeVo info(String id) {
        EventBirdStrike eventBirdStrike = eventBirdStrikeMapper.selectOne(Wrappers.lambdaQuery(EventBirdStrike.class)
                .eq(EventBirdStrike::getId, id)
                .eq(EventBirdStrike::getProjectId, SecurityContextHolder.getProjectId())
                .eq(EventBirdStrike::getIsDeleted, 0));
        if (Objects.isNull(eventBirdStrike)) {
            return null;
        }

        EventBirdStrikeVo eventBirdStrikeVo = BeanUtil.copyProperties(eventBirdStrike, EventBirdStrikeVo.class);
        eventBirdStrikeVo.setEventBirdStrikeDamageList(eventBirdStrikeDamageMapper.info(eventBirdStrikeVo.getId()));
        return eventBirdStrikeVo;
    }
}

