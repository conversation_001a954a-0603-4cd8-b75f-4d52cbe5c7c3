package com.allin.silas.event.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * <AUTHOR>
 * @since 2025/7/25
 **/
public enum EventBirdStrikeDamageImpactPartEnums implements IEnums {
    NO_RESIDUE_FOUND(1, "未发现鸟级残留物"),
    RADOME(2, "雷达罩"),
    WINDSHIELD(3, "风挡"),
    NOSE(4, "机头"),
    PROPELLER(5, "螺旋桨"),
    ENGINE_1(6, "发动机1#"),
    ENGINE_2(7, "发动机2#"),
    ENGINE_3(8, "发动机3#"),
    ENGINE_4(9, "发动机4#"),
    WING_ROTOR(10, "机翼/旋翼"),
    FUSELAGE(11, "机身"),
    LANDING_GEAR(12, "起落架"),
    TAIL(13, "机尾"),
    LIGHT(14, "灯"),
    TIRE_BURST(15, "轮胎（爆胎）"),
    TIRE_DELAMINATION(16, "轮胎（脱层）"),
    TIRE_PUNCTURE(17, "轮胎（扎破）"),
    HORIZONTAL_STABILIZER(18, "水平安定面"),
    OTHER(19, "其他");

    private final Integer code;

    private final String desc;

    EventBirdStrikeDamageImpactPartEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
