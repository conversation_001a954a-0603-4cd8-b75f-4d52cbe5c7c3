package com.allin.silas.event.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ADS-B 飞机信息表
 */
@Data
@TableName(value = "event_ads_b")
public class EventAdsB {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id")
    private String projectId;

    /**
     * ICAO地址
     */
    @TableField(value = "icao_address")
    private String icaoAddress;

    /**
     * 航班号
     */
    @TableField(value = "flight_number")
    private String flightNumber;

    /**
     * 飞机呼号
     */
    @TableField(value = "callsign")
    private String callsign;

    /**
     * 经度
     */
    @TableField(value = "lon")
    private Double lon;

    /**
     * 纬度
     */
    @TableField(value = "lat")
    private Double lat;

    /**
     * 海拔高度(毫米)
     */
    @TableField(value = "altitude_mm")
    private Integer altitudeMm;

    /**
     * 水平速度,(厘米/秒)
     */
    @TableField(value = "hor_velocity_cms")
    private Integer horVelocityCms;

    /**
     * 垂直速度,(厘米/秒)
     */
    @TableField(value = "ver_velocity_cms")
    private Integer verVelocityCms;

    /**
     * 发射器类型（3通常指的是民航客机）
     */
    @TableField(value = "emitter_type")
    private Integer emitterType;

    /**
     * 磁航向
     */
    @TableField(value = "mag_heading")
    private Integer magHeading;

    /**
     * 紧急状态
     */
    @TableField(value = "emergency_status")
    private Integer emergencyStatus;

    /**
     * 航向角
     */
    @TableField(value = "heading_de2")
    private Integer headingDe2;

    /**
     * 空地状态, 0通常表示在空中，1表示在地面
     */
    @TableField(value = "air_ground_state")
    private Integer airGroundState;

    /**
     * 状态,0-降落,1-起飞,2-其他
     */
    @TableField(value = "ads_b_status")
    private Integer asdBStatus;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private LocalDateTime createdTime;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}