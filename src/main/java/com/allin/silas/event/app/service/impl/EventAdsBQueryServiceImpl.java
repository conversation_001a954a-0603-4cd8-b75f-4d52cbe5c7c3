package com.allin.silas.event.app.service.impl;

import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.event.adapter.query.EventAdsBQuery;
import com.allin.silas.event.adapter.vo.EventAdsBPageVo;
import com.allin.silas.event.app.service.EventAdsBQueryService;
import com.allin.silas.event.infra.repository.EventAdsBMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * AdsB查询服务实现
 *
 * <AUTHOR>
 * @since 2025/7/28
 */
@Service
public class EventAdsBQueryServiceImpl implements EventAdsBQueryService {

    private final EventAdsBMapper eventAdsBMapper;

    public EventAdsBQueryServiceImpl(EventAdsBMapper eventAdsBMapper) {
        this.eventAdsBMapper = eventAdsBMapper;
    }

    @Override
    public Page<EventAdsBPageVo> page(PageParam pageParam, EventAdsBQuery query) {
        String tableName = TableNameUtils.getAdsBInfo(query.getStartTime().toLocalDate());
        final Page<EventAdsBPageVo> page = pageParam.toPage();
        return eventAdsBMapper.page(page, tableName, query);
    }


    @Override
    public Page<EventAdsBPageVo> pageByCallsign(PageParam pageParam, LocalDate date, String callsign) {
        String tableName = TableNameUtils.getAdsBInfo(date);
        String projectId = SecurityContextHolder.getProjectId();
        final Page<EventAdsBPageVo> page = pageParam.toPage();
        return eventAdsBMapper.pageByCallsign(page, tableName, projectId, callsign);
    }

    @Override
    public List<EventAdsBPageVo> listByCallsign(LocalDate date, String callsign) {
        String tableName = TableNameUtils.getAdsBInfo(date);
        String projectId = SecurityContextHolder.getProjectId();
        final Page<EventAdsBPageVo> page = Page.of(-1, -1);
        return eventAdsBMapper.pageByCallsign(page, tableName, projectId, callsign).getRecords();
    }
}
