package com.allin.silas.event.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 鸟击事件损伤信息表
 *
 * <AUTHOR>
 */
@Data
@TableName("event_bird_strike_damage")
public class EventBirdStrikeDamage {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 鸟击事件id
     */
    @TableField("event_bird_strike_id")
    private String eventBirdStrikeId;

    /**
     * 被击或损伤部位
     */
    @TableField("impact_part")
    private Integer impactPart;

    /**
     * 损伤程度 1超标 2未超标 3无损伤
     */
    @TableField("damage_level")
    private Integer damageLevel;

}

