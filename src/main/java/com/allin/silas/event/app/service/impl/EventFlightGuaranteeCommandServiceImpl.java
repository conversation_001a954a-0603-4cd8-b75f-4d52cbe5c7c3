package com.allin.silas.event.app.service.impl;

import com.allin.silas.event.adapter.dto.EventFlightGuaranteeEditDto;
import com.allin.silas.event.app.entity.EventFlightGuarantee;
import com.allin.silas.event.app.service.EventFlightGuaranteeCommandService;
import com.allin.silas.event.infra.repository.EventFlightGuaranteeMapper;
import org.springframework.stereotype.Service;

/**
 * 航班保障事件命令服务实现
 *
 * <AUTHOR>
 * @since 2025/7/25
 */
@Service
public class EventFlightGuaranteeCommandServiceImpl implements EventFlightGuaranteeCommandService {

    private final EventFlightGuaranteeMapper flightGuaranteeMapper;

    public EventFlightGuaranteeCommandServiceImpl(EventFlightGuaranteeMapper flightGuaranteeMapper) {
        this.flightGuaranteeMapper = flightGuaranteeMapper;
    }


    @Override
    public boolean del(String id) {
        return flightGuaranteeMapper.deleteById(id) > 0;
    }

    @Override
    public boolean edit(EventFlightGuaranteeEditDto editDto) {
        final EventFlightGuarantee eventFlightGuarantee = new EventFlightGuarantee();
        eventFlightGuarantee.setId(eventFlightGuarantee.getId());
        eventFlightGuarantee.setRemarks(editDto.getRemarks());
        return flightGuaranteeMapper.updateById(eventFlightGuarantee) > 0;
    }
}
