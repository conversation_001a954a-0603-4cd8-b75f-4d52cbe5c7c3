package com.allin.silas.event.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 入侵事件处置人员表
 */
@Data
@TableName(value = "event_invasion_handle_person")
public class EventInvasionHandlePerson {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 入侵事件ID
     */
    @TableField(value = "event_invasion_id")
    private String eventInvasionId;

    /**
     * 入侵事件处置ID
     */
    @TableField(value = "event_invasion_handle_id")
    private String eventInvasionHandleId;

    /**
     * 处置用户id
     */
    @TableField(value = "handle_user_id")
    private String handleUserId;
}