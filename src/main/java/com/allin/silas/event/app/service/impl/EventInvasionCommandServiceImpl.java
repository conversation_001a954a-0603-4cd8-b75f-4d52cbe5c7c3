package com.allin.silas.event.app.service.impl;

import com.allin.silas.event.adapter.dto.EventInvasionHandleDto;
import com.allin.silas.event.app.entity.EventInvasion;
import com.allin.silas.event.app.entity.EventInvasionHandle;
import com.allin.silas.event.app.manager.EventInvasionHandlePersonManager;
import com.allin.silas.event.app.service.EventInvasionCommandService;
import com.allin.silas.event.infra.repository.EventInvasionHandleMapper;
import com.allin.silas.event.infra.repository.EventInvasionMapper;
import com.allin.silas.report.app.enums.ReportDiscoverPersonReportTypeEnums;
import com.allin.silas.report.client.ReportFacade;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 入侵处置事件命令服务
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
@Service
public class EventInvasionCommandServiceImpl implements EventInvasionCommandService {

    private final EventInvasionMapper eventInvasionMapper;

    private final EventInvasionHandleMapper eventInvasionHandleMapper;

    private final EventInvasionHandlePersonManager eventInvasionHandlePersonManager;

    private final ReportFacade reportFacade;

    public EventInvasionCommandServiceImpl(EventInvasionMapper eventInvasionMapper,
                                           EventInvasionHandleMapper eventInvasionHandleMapper,
                                           EventInvasionHandlePersonManager eventInvasionHandlePersonManager,
                                           ReportFacade reportFacade) {
        this.eventInvasionMapper = eventInvasionMapper;
        this.eventInvasionHandleMapper = eventInvasionHandleMapper;
        this.eventInvasionHandlePersonManager = eventInvasionHandlePersonManager;
        this.reportFacade = reportFacade;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean handle(EventInvasionHandleDto handleDto) {
        final EventInvasion eventInvasion = eventInvasionMapper.selectById(handleDto.getEventInvasionId());
        eventInvasion.setHandleStatus(1);
        if (eventInvasionMapper.updateById(eventInvasion) > 0) {
            final boolean exists = eventInvasionHandleMapper.exists(Wrappers.lambdaQuery(EventInvasionHandle.class)
                    .eq(EventInvasionHandle::getEventInvasionId, handleDto.getEventInvasionId()));
            if (exists) {
                throw new ValidationFailureException(I18nUtil.isExist());
            }
            final EventInvasionHandle invasionHandle = handleDto.toEntity();
            eventInvasionHandleMapper.insert(invasionHandle);
            eventInvasionHandlePersonManager.edit(eventInvasion.getId(), handleDto
                    .toPersons(eventInvasion.getId(), invasionHandle.getId()));
            // 回调改变上报事件状态
            switch (eventInvasion.getEventType()) {
                case 0 ->
                        reportFacade.callbackUpHandleStatus(eventInvasion.getSourceId(), ReportDiscoverPersonReportTypeEnums.BIRD);
                case 1 ->
                        reportFacade.callbackUpHandleStatus(eventInvasion.getSourceId(), ReportDiscoverPersonReportTypeEnums.FLOATER);
                case 2 ->
                        reportFacade.callbackUpHandleStatus(eventInvasion.getSourceId(), ReportDiscoverPersonReportTypeEnums.DRONE);
                default -> throw new ValidationFailureException("事件类型错误");
            }
            return true;
        }
        return false;
    }
}
