package com.allin.silas.event.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 入侵事件信息表
 */
@Data
@TableName(value = "event_invasion")
public class EventInvasion {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 来源：0目标检测、1信息上报
     */
    @TableField(value = "source")
    private Integer source;

    /**
     * 来源id
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 推送方式：0手动推送、1自动推送
     */
    @TableField(value = "push_type")
    private Integer pushType;

    /**
     * 事件类型：0鸟情、1空飘物、2无人机
     */
    @TableField(value = "event_type")
    private Integer eventType;

    /**
     * 发现时间
     */
    @TableField(value = "discover_time")
    private LocalDateTime discoverTime;

    /**
     * 发现区域ID
     */
    @TableField(value = "discover_region_id")
    private String discoverRegionId;

    /**
     * 发现经度
     */
    @TableField(value = "discover_longitude")
    private Double discoverLongitude;

    /**
     * 发现纬度
     */
    @TableField(value = "discover_latitude")
    private Double discoverLatitude;

    /**
     * 高度（米）
     */
    @TableField(value = "altitude")
    private Integer altitude;

    /**
     * 事件危险等级
     */
    @TableField(value = "danger_level")
    private Integer dangerLevel;

    /**
     * 处置状态：0未处置、1已处置
     */
    @TableField(value = "handle_status")
    private Integer handleStatus;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除,0-否,1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}