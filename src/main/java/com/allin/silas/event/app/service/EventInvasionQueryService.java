package com.allin.silas.event.app.service;

import com.allin.silas.event.adapter.query.EventInvasionPageQuery;
import com.allin.silas.event.adapter.vo.EventInvasionHandleVo;
import com.allin.silas.event.adapter.vo.EventInvasionPageVo;
import com.allin.silas.event.adapter.vo.QueryEventInvasionStatisticsVo;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 入侵事件查询服务
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
public interface EventInvasionQueryService {

    /**
     * 分页查询
     */
    Page<EventInvasionPageVo> page(PageParam pageParam, EventInvasionPageQuery query);

    /**
     * 分页查询与指定用户有关的事件
     */
    Page<EventInvasionPageVo> pageByUserId(PageParam pageParam, EventInvasionPageQuery query, String userId);

    /**
     * 列表查询
     */
    List<EventInvasionPageVo> list(EventInvasionPageQuery query);

    /**
     * 列表查询与指定用户有关的事件
     */
    List<EventInvasionPageVo> listByUserId(EventInvasionPageQuery query, String userId);

    /**
     * 根据上报记录id查询处置信息
     */
    default EventInvasionHandleVo getHandleInfoByReportId(String reportId) {
        return getHandleInfoBySourceId(1, reportId);
    }

    /**
     * 根据批次号查询处置信息
     */
    default EventInvasionHandleVo getHandleInfoByBatchNumber(String batchNumber) {
        return getHandleInfoBySourceId(0, batchNumber);
    }

    /**
     * 根据数据来源和数据批次号查询处置信息
     */
    EventInvasionHandleVo getHandleInfoBySourceId(Integer type, String sourceId);

    /**
     * 根据事件id查询处置信息
     */
    EventInvasionHandleVo getHandleInfoByEventId(String eventId);

    /**
     * 统计查询
     */
    QueryEventInvasionStatisticsVo statistics(EventInvasionPageQuery query);

    /**
     * 统计查询与指定用户有关的事件
     */
    QueryEventInvasionStatisticsVo statisticsByUserId(EventInvasionPageQuery query, String userId);

    /**
     * 待用户处置的事件数量
     */
    Integer unhandled(String userId, String projectId);
}
