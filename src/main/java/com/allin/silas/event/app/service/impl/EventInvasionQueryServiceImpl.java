package com.allin.silas.event.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.allin.silas.common.mybatis.util.MybatisResultUtils;
import com.allin.silas.event.adapter.query.EventInvasionPageQuery;
import com.allin.silas.event.adapter.vo.EventInvasionHandleVo;
import com.allin.silas.event.adapter.vo.EventInvasionPageVo;
import com.allin.silas.event.adapter.vo.QueryEventInvasionStatisticsVo;
import com.allin.silas.event.app.entity.EventInvasion;
import com.allin.silas.event.app.entity.EventInvasionHandle;
import com.allin.silas.event.app.entity.EventInvasionHandlePerson;
import com.allin.silas.event.app.service.EventInvasionQueryService;
import com.allin.silas.event.infra.repository.EventInvasionHandleMapper;
import com.allin.silas.event.infra.repository.EventInvasionHandlePersonMapper;
import com.allin.silas.event.infra.repository.EventInvasionMapper;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/5
 */
@Service
public class EventInvasionQueryServiceImpl implements EventInvasionQueryService {

    private final EventInvasionMapper eventMapper;

    private final EventInvasionHandleMapper handleMapper;

    private final EventInvasionHandlePersonMapper handlePersonMapper;

    public EventInvasionQueryServiceImpl(EventInvasionMapper eventMapper,
                                         EventInvasionHandleMapper handleMapper,
                                         EventInvasionHandlePersonMapper handlePersonMapper) {
        this.eventMapper = eventMapper;
        this.handleMapper = handleMapper;
        this.handlePersonMapper = handlePersonMapper;
    }

    @Override
    public Page<EventInvasionPageVo> page(PageParam pageParam, EventInvasionPageQuery query) {
        final Page<EventInvasionPageVo> page = pageParam.toPage();
        handleMapper.page(page, query);
        if (page.getTotal() < 1) {
            return page;
        }
        final List<EventInvasionHandlePerson> handlePersonList = handlePersonMapper
                .listByEventInvasionId(page.getRecords()
                        .stream()
                        .map(EventInvasionPageVo::getId)
                        .toList());
        for (EventInvasionPageVo vo : page.getRecords()) {
            vo.setHandlePersons(handlePersonList.stream()
                    .filter(handlePerson -> handlePerson.getEventInvasionId().equals(vo.getId()))
                    .map(EventInvasionHandlePerson::getHandleUserId)
                    .toList());
        }
        return page;
    }

    @Override
    public Page<EventInvasionPageVo> pageByUserId(PageParam pageParam, EventInvasionPageQuery query, String userId) {
        final Page<EventInvasionPageVo> page = pageParam.toPage();
        handleMapper.pageByUserId(page, query, userId);
        if (page.getTotal() < 1) {
            return page;
        }
        final List<EventInvasionHandlePerson> handlePersonList = handlePersonMapper
                .listByEventInvasionId(page.getRecords()
                        .stream()
                        .map(EventInvasionPageVo::getId)
                        .toList());
        for (EventInvasionPageVo vo : page.getRecords()) {
            vo.setHandlePersons(handlePersonList.stream()
                    .filter(handlePerson -> handlePerson.getEventInvasionId().equals(vo.getId()))
                    .map(EventInvasionHandlePerson::getHandleUserId)
                    .toList());
        }
        return page;
    }

    @Override
    public List<EventInvasionPageVo> list(EventInvasionPageQuery query) {
        final Page<EventInvasionPageVo> page = Page.of(-1, -1);
        return handleMapper.page(page, query).getRecords();
    }

    @Override
    public List<EventInvasionPageVo> listByUserId(EventInvasionPageQuery query, String userId) {
        final Page<EventInvasionPageVo> page = Page.of(-1, -1);
        return handleMapper.pageByUserId(page, query, userId).getRecords();
    }

    @Override
    public EventInvasionHandleVo getHandleInfoBySourceId(Integer source, String sourceId) {
        final EventInvasion eventInvasion = eventMapper.selectOne(Wrappers
                .lambdaQuery(EventInvasion.class)
                .eq(EventInvasion::getSource, source)
                .eq(EventInvasion::getSourceId, sourceId));
        if (eventInvasion == null) {
            return null;
        }
        final EventInvasionHandle eventInvasionHandle = handleMapper.selectOne(Wrappers
                .lambdaQuery(EventInvasionHandle.class)
                .eq(EventInvasionHandle::getEventInvasionId, eventInvasion.getId()));
        final List<String> handlePersons = handlePersonMapper.selectList(Wrappers
                        .lambdaQuery(EventInvasionHandlePerson.class)
                        .eq(EventInvasionHandlePerson::getEventInvasionId, eventInvasion.getId()))
                .stream().map(EventInvasionHandlePerson::getHandleUserId).toList();
        if (eventInvasionHandle == null && CollUtil.isEmpty(handlePersons)) {
            return null;
        }
        return EventInvasionHandleVo.of(eventInvasionHandle, handlePersons);
    }

    @Override
    public EventInvasionHandleVo getHandleInfoByEventId(String eventId) {
        final EventInvasionHandle eventInvasionHandle = handleMapper.selectOne(Wrappers
                .lambdaQuery(EventInvasionHandle.class)
                .eq(EventInvasionHandle::getEventInvasionId, eventId));
        final List<String> handlePersons = handlePersonMapper.selectList(Wrappers
                        .lambdaQuery(EventInvasionHandlePerson.class)
                        .eq(EventInvasionHandlePerson::getEventInvasionId, eventId))
                .stream().map(EventInvasionHandlePerson::getHandleUserId).toList();
        if (eventInvasionHandle == null && CollUtil.isEmpty(handlePersons)) {
            return null;
        }
        return EventInvasionHandleVo.of(eventInvasionHandle, handlePersons);
    }

    @Override
    public QueryEventInvasionStatisticsVo statistics(EventInvasionPageQuery query) {
        final QueryEventInvasionStatisticsVo statisticsVo = new QueryEventInvasionStatisticsVo();

        // 统计危险等级分布
        final List<Map<String, Object>> dangerLevelList = handleMapper.statisticsDangerLevel(query);
        final Map<Integer, Integer> dangerLevelStatMap = MybatisResultUtils.convertListToMap(dangerLevelList,
                Convert::toInt, Convert::toInt);
        statisticsVo.setDangerLevelStatMap(dangerLevelStatMap);

        // 统计处置状态分布
        final List<Map<String, Object>> handleStatusList = handleMapper.statisticsHandleStatus(query);
        final Map<Integer, Integer> handleStatusStatMap = MybatisResultUtils.convertListToMap(handleStatusList,
                Convert::toInt, Convert::toInt);
        statisticsVo.setHandleStatusStatMap(handleStatusStatMap);

        return statisticsVo;
    }

    @Override
    public QueryEventInvasionStatisticsVo statisticsByUserId(EventInvasionPageQuery query, String userId) {
        final QueryEventInvasionStatisticsVo statisticsVo = new QueryEventInvasionStatisticsVo();

        // 统计危险等级分布（按用户ID过滤）
        final List<Map<String, Object>> dangerLevelList = handleMapper.statisticsDangerLevelByUserId(query, userId);
        final Map<Integer, Integer> dangerLevelStatMap = MybatisResultUtils.convertListToMap(dangerLevelList,
                Convert::toInt, Convert::toInt);
        statisticsVo.setDangerLevelStatMap(dangerLevelStatMap);

        // 统计处置状态分布（按用户ID过滤）
        final List<Map<String, Object>> handleStatusList = handleMapper.statisticsHandleStatusByUserId(query, userId);
        final Map<Integer, Integer> handleStatusStatMap = MybatisResultUtils.convertListToMap(handleStatusList,
                Convert::toInt, Convert::toInt);
        statisticsVo.setHandleStatusStatMap(handleStatusStatMap);

        return statisticsVo;
    }

    @Override
    public Integer unhandled(String userId, String projectId) {
        return handleMapper.unhandled(userId, projectId);
    }
}
