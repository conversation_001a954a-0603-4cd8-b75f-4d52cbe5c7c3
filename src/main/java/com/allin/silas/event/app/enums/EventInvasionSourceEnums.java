package com.allin.silas.event.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 入侵事件来源枚举
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
public enum EventInvasionSourceEnums implements IEnums {
    TARGET_DETECT(0, "目标检测"),
    INFORMATION_REPORT(1, "信息上报");

    private final Integer code;

    private final String desc;

    EventInvasionSourceEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }


}
