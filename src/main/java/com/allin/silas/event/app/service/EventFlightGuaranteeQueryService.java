package com.allin.silas.event.app.service;

import com.allin.silas.event.adapter.query.EventFlightGuaranteeQuery;
import com.allin.silas.event.adapter.vo.EventFlightGuaranteePageVo;
import com.allin.silas.event.adapter.vo.EventFlightGuaranteeVo;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 航班保障事件查询服务
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
public interface EventFlightGuaranteeQueryService {

    /**
     * 分页查询
     */
    Page<EventFlightGuaranteePageVo> page(PageParam pageParam, EventFlightGuaranteeQuery query);

    /**
     * 航班保障详情
     */
    EventFlightGuaranteeVo info(String id);
}
