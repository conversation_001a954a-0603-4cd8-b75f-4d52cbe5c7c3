package com.allin.silas.event.app.service.impl;

import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.event.adapter.query.EventFlightGuaranteeQuery;
import com.allin.silas.event.adapter.vo.EventFlightGuaranteePageVo;
import com.allin.silas.event.adapter.vo.EventFlightGuaranteeVo;
import com.allin.silas.event.app.entity.EventFlightGuarantee;
import com.allin.silas.event.app.service.EventFlightGuaranteeQueryService;
import com.allin.silas.event.infra.repository.EventFlightGuaranteeMapper;
import com.allin.silas.visual.client.VisualTargetFacade;
import com.allin.silas.visual.client.vo.VisualTargetMergedInfoClientVo;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/7/25
 */
@Service
public class EventFlightGuaranteeQueryServiceImpl implements EventFlightGuaranteeQueryService {

    private final EventFlightGuaranteeMapper eventFlightGuaranteeMapper;

    private final VisualTargetFacade visualTargetFacade;

    public EventFlightGuaranteeQueryServiceImpl(EventFlightGuaranteeMapper eventFlightGuaranteeMapper, VisualTargetFacade visualTargetFacade) {
        this.eventFlightGuaranteeMapper = eventFlightGuaranteeMapper;
        this.visualTargetFacade = visualTargetFacade;
    }

    @Override
    public Page<EventFlightGuaranteePageVo> page(PageParam pageParam, EventFlightGuaranteeQuery query) {
        String tableName = TableNameUtils.getVisualTargetMergedInfo(query.getStartTime());
        final Page<EventFlightGuaranteePageVo> page = pageParam.toPage();
        return eventFlightGuaranteeMapper.page(page, tableName, query);
    }

    @Override
    public EventFlightGuaranteeVo info(String id) {
        final EventFlightGuarantee eventFlightGuarantee = eventFlightGuaranteeMapper.selectById(id);
        if (eventFlightGuarantee == null) {
            throw new ValidationFailureException(I18nUtil.isNotExist());
        }
        final EventFlightGuaranteeVo vo = new EventFlightGuaranteeVo();
        vo.setId(eventFlightGuarantee.getId());
        vo.setDevNum(eventFlightGuarantee.getDevNum());
        vo.setBatchNumber(eventFlightGuarantee.getBatchNumber());
        vo.setFlightNumber(eventFlightGuarantee.getFlightNumber());
        vo.setCreatedTime(eventFlightGuarantee.getCreatedTime());
        final VisualTargetMergedInfoClientVo targetInfoVo = visualTargetFacade
                .getByBatchNumber(eventFlightGuarantee.getCreatedTime(), eventFlightGuarantee.getBatchNumber());
        if (targetInfoVo == null) {
            throw new ValidationFailureException(I18nUtil.isNotExist("batchNumber"));
        }
        vo.setId(targetInfoVo.getId());
        vo.setDevNum(targetInfoVo.getDevNum());
        vo.setMinRunwayDistance(targetInfoVo.getMinRunwayDistance());
        vo.setMaxRunwayDistance(targetInfoVo.getMaxRunwayDistance());
        vo.setMinDistance(targetInfoVo.getMinDistance());
        vo.setMaxDistance(targetInfoVo.getMaxDistance());
        vo.setMinHeight(targetInfoVo.getMinHeight());
        vo.setMaxHeight(targetInfoVo.getMaxHeight());
        vo.setMinAzimuth(targetInfoVo.getMinAzimuth());
        vo.setMaxAzimuth(targetInfoVo.getMaxAzimuth());
        return vo;
    }
}
