package com.allin.silas.event.app.service.impl;

import com.allin.silas.event.app.service.EventAdsBCommandService;
import com.allin.silas.event.infra.repository.EventAdsBMapper;
import org.springframework.stereotype.Service;

/**
 * ADS-B信息事件命令服务实现
 *
 * <AUTHOR>
 * @since 2025/7/28
 */
@Service
public class EventAdsBCommandServiceImpl implements EventAdsBCommandService {

    private final EventAdsBMapper eventAdsBMapper;

    public EventAdsBCommandServiceImpl(EventAdsBMapper eventAdsBMapper) {
        this.eventAdsBMapper = eventAdsBMapper;
    }

    @Override
    public boolean del(String id) {
        return eventAdsBMapper.deleteById(id) > 0;
    }
}
