package com.allin.silas.event.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 航班保障事件表
 */
@Data
@TableName(value = "event_flight_guarantee")
public class EventFlightGuarantee {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目ID
     */
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 设备编号
     */
    @TableField(value = "dev_num")
    private String devNum;

    /**
     * 批次号
     */
    @TableField(value = "batch_number")
    private String batchNumber;

    /**
     * 航班号
     */
    @TableField(value = "flight_number")
    private String flightNumber;

    /**
     * 事件创建时间
     */
    @TableField(value = "created_time")
    private LocalDateTime createdTime;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    private String remarks;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}