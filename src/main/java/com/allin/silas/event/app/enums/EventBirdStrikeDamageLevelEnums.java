package com.allin.silas.event.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * <AUTHOR>
 * @since 2025/7/25
 **/
public enum EventBirdStrikeDamageLevelEnums implements IEnums {
    EXCEEDED(1, "超标"),
    NOT_EXCEEDED(2, "未超标"),
    NO_DAMAGE(3, "无损伤");

    private final Integer code;

    private final String desc;

    EventBirdStrikeDamageLevelEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
