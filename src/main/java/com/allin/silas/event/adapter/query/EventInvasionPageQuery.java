package com.allin.silas.event.adapter.query;

import com.allin.silas.event.app.enums.EventInvasionSourceEnums;
import com.allin.silas.event.client.enums.EventInvasionTypeEnums;
import com.allin.view.base.enums.validator.IEnumValid;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDateTime;

/**
 * 入侵处置事件分页查询
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
@Data
public class EventInvasionPageQuery {
    /**
     * 来源：0目标检测、1信息上报
     *
     * @see EventInvasionSourceEnums#code
     */
    @IEnumValid(target = EventInvasionSourceEnums.class)
    private Integer source;

    /**
     * 事件类型：0鸟情、1空飘物、2无人机
     *
     * @see EventInvasionTypeEnums#code
     */
    @IEnumValid(target = EventInvasionTypeEnums.class)
    private Integer eventType;

    /**
     * 发现时间 yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime startDiscoverTime;

    /**
     * 发现时间 yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime endDiscoverTime;

    /**
     * 发现区域ID
     */
    private String discoverRegionId;

    /**
     * 事件危险等级
     */
    private Integer startDangerLevel;

    /**
     * 事件危险等级
     */
    private Integer endDangerLevel;

    /**
     * 处置状态：0未处置、1已处置
     */
    @Range(min = 0, max = 1)
    private Integer handleStatus;

    /**
     * 项目编号
     */
    @JsonIgnore
    private String projectId;
}
