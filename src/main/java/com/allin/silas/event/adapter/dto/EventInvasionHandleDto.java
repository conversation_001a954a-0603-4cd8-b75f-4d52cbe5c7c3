package com.allin.silas.event.adapter.dto;

import cn.hutool.core.collection.CollUtil;
import com.allin.silas.event.app.entity.EventInvasionHandle;
import com.allin.silas.event.app.entity.EventInvasionHandlePerson;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 推送处置事件
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
@Data
public class EventInvasionHandleDto {

    /**
     * 事件id
     */
    @NotBlank
    private String eventInvasionId;

    /**
     * 处置时间
     */
    @NotNull
    private LocalDateTime handleTime;

    /**
     * 处置措施
     */
    @NotEmpty
    private List<@NotBlank String> handleMethod;

    /**
     * 处置效果
     */
    @NotEmpty
    private List<@NotBlank String> handleResult;

    /**
     * 是否影响航班：0未影响、1已影响
     */
    @NotNull
    private Integer affectFlight;

    /**
     * 是否通报空管：0未通报、1已通报
     */
    @NotNull
    private Integer notifyAtc;

    /**
     * 是否通报公安机关：0未通报、1已通报
     */
    @NotNull
    private Integer notifyPolice;

    /**
     * 处置人列表
     */
    @NotEmpty
    private List<@NotBlank String> handlePersons;

    /**
     * 附件id
     */
    private String attachmentId;

    /**
     * 猎枪耗弹数量（仅在措施为猎枪时填写）
     */
    private Integer shotgunAmmoCount;

    /**
     * 钝雷弹耗弹数量（仅在措施为钝雷弹时填写）
     */
    private Integer thunderAmmoCount;

    public static void validate(List<String> handleMethod, Integer shotgunAmmoCount, Integer thunderAmmoCount) {
        if (CollUtil.isNotEmpty(handleMethod) && CollUtil.contains(handleMethod, "猎枪")) {
            if (shotgunAmmoCount == null) {
                throw new ValidationFailureException(I18nUtil.isParamException("shotgunAmmoCount"));
            }
        }
        if (CollUtil.isNotEmpty(handleMethod) && CollUtil.contains(handleMethod, "钝雷弹")) {
            if (thunderAmmoCount == null) {
                throw new ValidationFailureException(I18nUtil.isParamException("thunderAmmoCount"));
            }
        }
    }

    public void validate() {
        validate(getHandleMethod(), getShotgunAmmoCount(), getThunderAmmoCount());
    }

    /**
     * 转换成处置事件信息实体
     */
    public EventInvasionHandle toEntity() {
        final EventInvasionHandle eventInvasionHandle = new EventInvasionHandle();
        eventInvasionHandle.setProjectId(SecurityContextHolder.getProjectId());
        eventInvasionHandle.setEventInvasionId(this.eventInvasionId);
        eventInvasionHandle.setHandleTime(this.handleTime);
        eventInvasionHandle.setHandleMethod(String.join(",", this.handleMethod));
        eventInvasionHandle.setHandleResult(String.join(",", this.handleResult));
        eventInvasionHandle.setAffectFlight(this.affectFlight);
        eventInvasionHandle.setNotifyAtc(this.notifyAtc);
        eventInvasionHandle.setNotifyPolice(this.notifyPolice);
        eventInvasionHandle.setAttachmentId(this.attachmentId);
        eventInvasionHandle.setShotgunAmmoCount(this.shotgunAmmoCount);
        eventInvasionHandle.setThunderAmmoCount(this.thunderAmmoCount);
        return eventInvasionHandle;
    }

    public List<EventInvasionHandlePerson> toPersons(String eventInvasionId, String eventInvasionHandleId) {
        return this.handlePersons.stream()
                .map(handlePerson -> {
                    final EventInvasionHandlePerson eventInvasionHandlePerson = new EventInvasionHandlePerson();
                    eventInvasionHandlePerson.setEventInvasionId(eventInvasionId);
                    eventInvasionHandlePerson.setEventInvasionHandleId(eventInvasionHandleId);
                    eventInvasionHandlePerson.setHandleUserId(handlePerson);
                    return eventInvasionHandlePerson;
                }).toList();
    }
}
