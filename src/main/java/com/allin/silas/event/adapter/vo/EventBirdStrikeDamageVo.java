package com.allin.silas.event.adapter.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-07-25 16:04:15
 */
@Data
public class EventBirdStrikeDamageVo {
    /**
     * 鸟击事件id
     */
    private String eventBirdStrikeId;
    /**
     * 被击或损伤部位 1未发现鸟级残留物 2雷达罩 3风挡 4机头 5螺旋桨 6发动机1# 7发动机2# 8发动机3# 9发动机4# 10机翼/旋翼 11机身 12起落架 13机尾 14灯 15轮胎（爆胎） 16轮胎（脱层）17轮胎（扎破）18水平安定面 19其他
     */
    private Integer impactPart;
    /**
     * 损伤程度 1超标 2未超标 3无损伤
     */
    private Integer damageLevel;
}

