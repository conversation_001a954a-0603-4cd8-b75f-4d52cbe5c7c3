package com.allin.silas.event.adapter.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.allin.view.base.execl.DefaultExportStyle;
import com.allin.view.base.execl.converter.IEnumsConverter;
import com.allin.view.base.execl.converter.IEnumsToStrConverter;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ADS-B 飞机信息表
 */
@Data
public class EventAdsBPageVo extends DefaultExportStyle {
    /**
     * 主键ID
     */
    @ExcelIgnore
    private String id;

    /**
     * ICAO地址
     */
    @ExcelProperty(value = "ICAO地址")
    private String icaoAddress;

    /**
     * 航班号
     */
    @ExcelProperty(value = "航班号")
    private String flightNumber;

    /**
     * 飞机呼号
     */
    @ExcelProperty(value = "飞机呼号")
    private String callsign;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Double lon;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Double lat;

    /**
     * 海拔高度(毫米)
     */
    @ExcelProperty(value = "海拔高度(毫米)")
    private Integer altitudeMm;

    /**
     * 水平速度,(厘米/秒)
     */
    @ExcelProperty(value = "水平速度,(厘米/秒)")
    private Integer horVelocityCms;

    /**
     * 垂直速度,(厘米/秒)
     */
    @ExcelProperty(value = "垂直速度,(厘米/秒)")
    private Integer verVelocityCms;

    /**
     * 发射器类型（3通常指的是民航客机）
     */
    @ExcelProperty(value = "发射器类型")
    private Integer emitterType;

    /**
     * 磁航向
     */
    @ExcelProperty(value = "磁航向")
    private Integer magHeading;

    /**
     * 紧急状态
     */
    @ExcelIgnore
    private Integer emergencyStatus;

    /**
     * 航向角
     */
    @ExcelProperty(value = "航向角")
    private Integer headingDe2;

    /**
     * 空地状态, 0通常表示在空中，1表示在地面
     */
    @ExcelIgnore
    private Integer airGroundState;

    /**
     * 状态,0-降落,1-起飞,2-其他
     */
    @ExcelProperty(value = "状态", converter = IEnumsToStrConverter.class)
    @IEnumsConverter(replace = {"0_降落", "1_起飞", "2_其他"})
    private Integer adsBStatus;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createdTime;
}