package com.allin.silas.event.adapter.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.allin.silas.event.app.entity.EventBirdStrikeDamage;
import com.allin.view.auth.excel.converter.UserConverter;
import com.allin.view.auth.serializer.annotation.UserIdToFullName;
import com.allin.view.base.enums.serializer.IEnumsChangeDesc;
import com.allin.view.base.execl.converter.IEnumsToStrConverter;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-25 13:48:04
 */
@Data
public class EventBirdStrikeVo {
    /**
     * id
     */
    @ExcelIgnore
    private String id;
    /**
     * 事件名称
     */
    @ExcelProperty(value = "事件名称")
    private String eventName;
    /**
     * 发生时间
     */
    @ExcelProperty(value = "发生时间")
    private LocalDateTime occurrenceTime;
    /**
     * 事发地点
     */
    @ExcelProperty(value = "事发地点")
    private String occurrenceLocation;
    /**
     * 航空运输企业
     */
    @ExcelProperty(value = "航空运输企业")
    private String airlineCompany;
    /**
     * 航空器型号
     */
    @ExcelProperty(value = "航空器型号")
    private String aircraftModel;
    /**
     * 航空器注册号
     */
    @ExcelProperty(value = "航空器注册号")
    private String aircraftRegistration;
    /**
     * 航班号
     */
    @ExcelProperty(value = "航班号")
    private String flightNumber;
    /**
     * 接报时间
     */
    @ExcelProperty(value = "接报时间")
    private LocalDateTime reportTime;
    /**
     * 发生时段 1夜晚 2黄昏 3黎明 4白天
     */
    @ExcelProperty(value = "发生时段", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "occurrencePeriodDesc", replace = {"1_夜晚", "2_黄昏", "3_黎明", "4_白天"})
    private Integer occurrencePeriod;
    /**
     * 起飞机场
     */
    @ExcelProperty(value = "起飞机场")
    private String departureAirport;
    /**
     * 着陆机场
     */
    @ExcelProperty(value = "着陆机场")
    private String landingAirport;
    /**
     * 撞击残留物
     */
    @ExcelProperty(value = "撞击残留物")
    private String impactResidue;
    /**
     * 是否已采集残留物或样本 1是 0否
     */
    @ExcelProperty(value = "是否已采集残留物或样本", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "residueCollectedDesc", replace = {"1_是", "0_否"})
    private Integer residueCollected;
    /**
     * 是否送检 1是 0否
     */
    @ExcelProperty(value = "是否送检", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "residueSentForTestingDesc", replace = {"1_是", "0_否"})
    private Integer residueSentForTesting;
    /**
     * 鉴定机构名称
     */
    @ExcelProperty(value = "鉴定机构名称")
    private String testingAgency;
    /**
     * 对飞行的影响 1无 2中断起飞 3预防性着陆 4发动机停车 5航班延误 6航班取消 7换机
     */
    @ExcelProperty(value = "对飞行的影响", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "flightImpactDesc", replace = {"1_无", "2_中断起飞", "3_预防性着陆", "4_发动机停车", "5_航班延误", "6_航班取消", "7_换机"})
    private Integer flightImpact;
    /**
     * 离地高度
     */
    @ExcelProperty(value = "离地高度")
    private String altitude;
    /**
     * 指示空速
     */
    @ExcelProperty(value = "指示空速")
    private String indicatedAirspeed;
    /**
     * 飞行阶段 1停靠 2滑行 3起飞滑跑 4爬升 5航路飞行 6下降 7进近 8着陆滑跑 9不详
     */
    @ExcelProperty(value = "飞行阶段", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "flightPhaseDesc", replace = {"1_停靠", "2_滑行", "3_起飞滑跑", "4_爬升", "5_航路飞行", "6_下降", "7_进近", "8_着陆滑跑", "9_不详"})
    private Integer flightPhase;
    /**
     * 天空情况 1多云 2少云 3无云
     */
    @ExcelProperty(value = "天空情况", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "skyConditionDesc", replace = {"1_多云", "2_少云", "3_无云"})
    private Integer skyCondition;
    /**
     * 降水情况 1雾 2雨 3雪 4无
     */
    @ExcelProperty(value = "降水情况", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "precipitationDesc", replace = {"1_雾", "2_雨", "3_雪", "4_无"})
    private Integer precipitation;
    /**
     * 撞击物种
     */
    @ExcelProperty(value = "撞击物种")
    private String impactSpecies;
    /**
     * 物种体型 1大 2中 3小 4无
     */
    @ExcelProperty(value = "物种体型", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "speciesSizeDesc", replace = {"1_大", "2_中", "3_小", "4_无"})
    private Integer speciesSize;
    /**
     * 看到数量
     */
    @ExcelProperty(value = "看到数量")
    private Integer observedQuantity;
    /**
     * 击中数量
     */
    @ExcelProperty(value = "击中数量")
    private Integer actualImpactQuantity;
    /**
     * 修理或替换的损失
     */
    @ExcelProperty(value = "修理或替换的损失")
    private String repairCost;
    /**
     * 其他估算损失
     */
    @ExcelProperty(value = "其他估算损失")
    private String otherEstimatedLoss;
    /**
     * 飞行机组被告知鸟类或其他活动 1是 0否
     */
    @ExcelProperty(value = "飞行机组被告知鸟类或其他活动", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "crewInformedDesc", replace = {"1_是", "0_否"})
    private Integer crewInformed;
    /**
     * 飞行机组通报空管部门 1是 0否
     */
    @ExcelProperty(value = "飞行机组通报空管部门", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "atcNotifiedDesc", replace = {"1_是", "0_否"})
    private Integer atcNotified;
    /**
     * 鸟击航空器事件 1是 0否
     */
    @ExcelProperty(value = "鸟击航空器事件", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "isBirdStrikeEventDesc", replace = {"1_是", "0_否"})
    private Integer isBirdStrikeEvent;
    /**
     * 航空器起降方向
     */
    @ExcelProperty(value = "航空器起降方向")
    private String runwayDirection;
    /**
     * 后续跑道检查情况
     */
    @ExcelProperty(value = "后续跑道检查情况")
    private String runwayCheckStatus;
    /**
     * 是否机场责任范围 1是 0否
     */
    @ExcelProperty(value = "是否机场责任范围", converter = IEnumsToStrConverter.class)
    @IEnumsChangeDesc(key = "airportResponsibilityDesc", replace = {"1_是", "0_否"})
    private Integer airportResponsibility;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;
    /**
     * 附件
     */
    @ExcelIgnore
    private String attachment;
    /**
     * 停场时间
     */
    @ExcelProperty(value = "停场时间")
    private Integer downtimeHours;

    /**
     * 被击或损伤部位
     */
    @ExcelIgnore
    private List<EventBirdStrikeDamage> eventBirdStrikeDamageList;

    /**
     * 被击或损伤部位
     */
    @ExcelProperty(value = "被击或损伤部位")
    private String eventBirdStrikeDamageListDesc;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", converter = UserConverter.class)
    @UserIdToFullName(key = "createdByFullName")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createdTime;
}

