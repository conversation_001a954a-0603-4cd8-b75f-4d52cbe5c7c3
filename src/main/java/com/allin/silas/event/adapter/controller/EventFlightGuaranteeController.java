package com.allin.silas.event.adapter.controller;

import com.allin.silas.event.adapter.dto.EventFlightGuaranteeEditDto;
import com.allin.silas.event.adapter.query.EventFlightGuaranteeQuery;
import com.allin.silas.event.adapter.vo.EventFlightGuaranteePageVo;
import com.allin.silas.event.adapter.vo.EventFlightGuaranteeVo;
import com.allin.silas.event.app.service.EventFlightGuaranteeCommandService;
import com.allin.silas.event.app.service.EventFlightGuaranteeQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 事件管理/航班保障事件
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/event/flight_guarantee")
public class EventFlightGuaranteeController {

    private final EventFlightGuaranteeQueryService queryService;

    private final EventFlightGuaranteeCommandService commandService;

    public EventFlightGuaranteeController(EventFlightGuaranteeQueryService queryService,
                                          EventFlightGuaranteeCommandService commandService) {
        this.queryService = queryService;
        this.commandService = commandService;
    }

    /**
     * 分页查询航班保障事件
     */
    @GetMapping("/page")
    public Result<PageData<EventFlightGuaranteePageVo>> page(PageParam pageParam,
                                                             @Validated EventFlightGuaranteeQuery query) {
        query.setProjectId(SecurityContextHolder.getProjectId());
        return Result.ok(PageData.getInstance(queryService.page(pageParam, query)));
    }

    /**
     * 导出航班保障事件
     */
    @GetMapping("/export/excel")
    public void exportExcel(@Validated EventFlightGuaranteeQuery query,
                            HttpServletResponse response) {
        query.setProjectId(SecurityContextHolder.getProjectId());
        final Page<EventFlightGuaranteePageVo> page = queryService.page(new PageParam(-1, -1), query);
        FastExcelOperUtils.exportXlsx(response, page.getRecords(), EventFlightGuaranteePageVo.class, "航班保障事件");
    }

    /**
     * 航班保障事件详情
     */
    @GetMapping("/{id}")
    public Result<EventFlightGuaranteeVo> info(@PathVariable String id) {
        return Result.ok(queryService.info(id));
    }

    /**
     * 编辑航班保障事件
     */
    @PutMapping
    public Result<Void> edit(@Validated @RequestBody EventFlightGuaranteeEditDto editDto) {
        return commandService.edit(editDto) ? Result.ok() : Result.fail();
    }

    /**
     * 删除航班保障事件
     */
    @DeleteMapping("/{id}")
    public Result<Void> del(@PathVariable String id) {
        return commandService.del(id) ? Result.ok() : Result.fail();
    }
}
