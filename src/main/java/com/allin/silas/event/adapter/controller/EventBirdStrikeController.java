package com.allin.silas.event.adapter.controller;


import com.allin.silas.event.adapter.dto.AddEventBirdStrikeDto;
import com.allin.silas.event.adapter.query.EventBirdStrikeQuery;
import com.allin.silas.event.adapter.vo.EventBirdStrikeVo;
import com.allin.silas.event.app.service.EventBirdStrikeCommandService;
import com.allin.silas.event.app.service.EventBirdStrikeQueryService;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import com.allin.view.log.annotation.Log;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 事件管理/鸟击事件
 *
 * <AUTHOR>
 * @since 2025-07-25 13:48:04
 **/
@RestController
@RequestMapping("/eventBirdStrike")
@Validated
public class EventBirdStrikeController {

    private final EventBirdStrikeCommandService eventBirdStrikeCommandService;

    private final EventBirdStrikeQueryService eventBirdStrikeQueryService;

    public EventBirdStrikeController(EventBirdStrikeCommandService eventBirdStrikeCommandService, EventBirdStrikeQueryService eventBirdStrikeQueryService) {
        this.eventBirdStrikeCommandService = eventBirdStrikeCommandService;
        this.eventBirdStrikeQueryService = eventBirdStrikeQueryService;
    }

    /**
     * 分页查询
     */
    @GetMapping
    public Result<PageData<EventBirdStrikeVo>> page(PageParam pageParam, @Valid EventBirdStrikeQuery query) {
        return Result.ok(eventBirdStrikeQueryService.page(pageParam, query));
    }

    /**
     * 详情
     */
    @GetMapping("/{id}")
    public Result<EventBirdStrikeVo> info(@PathVariable String id) {
        return Result.ok(eventBirdStrikeQueryService.info(id));
    }

    /**
     * 新增
     */
    @PostMapping
    @Log(title = "鸟击事件", operDesc = "新增鸟击事件信息")
    public Result<String> save(@RequestBody @Valid AddEventBirdStrikeDto dto) {
        eventBirdStrikeCommandService.save(dto);
        return Result.ok();
    }

    /**
     * 删除
     */
    @DeleteMapping("/{id}")
    @Log(title = "鸟击事件", operDesc = "删除鸟击事件信息")
    public Result<String> delete(@PathVariable String id) {
        eventBirdStrikeCommandService.delete(id);
        return Result.ok();
    }

    /**
     * 导出
     */
    @PostMapping("/export/excel")
    public void exportExcel(@Validated @RequestBody EventBirdStrikeQuery query, HttpServletResponse response) {
        FastExcelOperUtils.exportXlsx(response, eventBirdStrikeQueryService.page(new PageParam(-1, -1), query).getRecords(), EventBirdStrikeVo.class, "鸟击事件信息");
    }
}

