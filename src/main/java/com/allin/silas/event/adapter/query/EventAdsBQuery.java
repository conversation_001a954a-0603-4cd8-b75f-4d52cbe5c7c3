package com.allin.silas.event.adapter.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDateTime;

/**
 * ADS-B 飞机信息表
 */
@Data
public class EventAdsBQuery {

    /**
     * 项目id
     */
    @JsonIgnore
    private String projectId;

    /**
     * ICAO地址
     */
    private String icaoAddress;

    /**
     * 航班号
     */
    private String flightNumber;

    /**
     * 飞机呼号
     */
    private String callsign;

    /**
     * 状态,0-降落,1-起飞,2-其他
     */
    @Range(min = 0, max = 2)
    private Integer asdBStatus;

    /**
     * 创建时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}