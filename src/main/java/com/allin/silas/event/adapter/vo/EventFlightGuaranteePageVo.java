package com.allin.silas.event.adapter.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.format.DateTimeFormat;
import com.allin.silas.dev.detect.client.annotation.DevNumToName;
import com.allin.silas.dev.detect.client.excel.DevNumToNameConverter;
import com.allin.view.base.execl.DefaultExportStyle;
import com.allin.view.config.serialize.annotation.ApiFoxNoIgnore;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 航班保障事件表
 */
@Data
public class EventFlightGuaranteePageVo extends DefaultExportStyle {
    /**
     * 主键ID
     */
    @ExcelProperty("主键ID")
    private String id;

    /**
     * 设备编号
     */
    @DevNumToName(key = "devName")
    @ExcelProperty(value = "设备编号", converter = DevNumToNameConverter.class)
    private String devNum;

    /**
     * 设备名称
     */
    @ApiFoxNoIgnore
    @ExcelIgnore
    private String devName;

    /**
     * 批次号
     */
    @ExcelProperty("批次号")
    private String batchNumber;

    /**
     * 航班号
     */
    @ExcelProperty("航班号")
    private String flightNumber;

    /**
     * 事件探测时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("事件探测时间")
    private LocalDateTime createdTime;

    /**
     * 离跑道最小距离, 单位米
     */
    @ExcelProperty("离跑道最小距离")
    private Double minRunwayDistance;

    /**
     * 离跑道最大距离, 单位米
     */
    @ExcelProperty("离跑道最大距离")
    private Double maxRunwayDistance;

    /**
     * 离设备最小距离
     */
    @ExcelProperty("离设备最小距离")
    private Float minDistance;

    /**
     * 离设备最大距离
     */
    @ExcelProperty("离设备最大距离")
    private Float maxDistance;

    /**
     * 最小高度
     */
    @ExcelProperty("最小高度")
    private Float minHeight;

    /**
     * 最大高度
     */
    @ExcelProperty("最大高度")
    private Float maxHeight;

    /**
     * 最小方位值
     */
    @ExcelProperty(value = "最小方位值")
    private Float minAzimuth;

    /**
     * 最大方位值
     */
    @ExcelProperty(value = "最大方位值")
    private Float maxAzimuth;
}