package com.allin.silas.event.adapter.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 航班保障事件表
 */
@Data
public class EventFlightGuaranteeQuery {

    /**
     * 设备编号
     */
    private List<String> devNums;

    /**
     * 开始时间, yyyy-MM-dd HH:mm:ss
     */
    @NotNull
    private LocalDateTime startTime;

    /**
     * 结束时间, yyyy-MM-dd HH:mm:ss
     */
    @NotNull
    private LocalDateTime endTime;

    /**
     * 批次号
     */
    private String batchNumber;

    /**
     * 航班号
     */
    private String flightNumber;

    /**
     * 离跑道最小距离
     */
    private Double minRunwayDistance;

    /**
     * 离跑道最大距离
     */
    private Double maxRunwayDistance;

    /**
     * 离设备最小距离
     */
    private Double minDistance;

    /**
     * 离设备最大距离
     */
    private Double maxDistance;

    /**
     * 最小高度
     */
    private Double minHeight;

    /**
     * 最大高度
     */
    private Double maxHeight;

    /**
     * 项目id
     */
    @JsonIgnore
    private String projectId;

}