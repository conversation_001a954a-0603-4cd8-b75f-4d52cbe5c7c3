package com.allin.silas.event.adapter.controller;

import com.allin.silas.event.adapter.dto.EventInvasionHandleDto;
import com.allin.silas.event.adapter.query.EventInvasionPageQuery;
import com.allin.silas.event.adapter.vo.EventInvasionHandleVo;
import com.allin.silas.event.adapter.vo.EventInvasionPageVo;
import com.allin.silas.event.adapter.vo.QueryEventInvasionStatisticsVo;
import com.allin.silas.event.app.service.EventInvasionCommandService;
import com.allin.silas.event.app.service.EventInvasionQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import com.allin.view.base.i18n.I18nUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 事件管理/入侵处置事件
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/event/invasion")
public class EventInvasionController {

    private final EventInvasionQueryService queryService;

    private final EventInvasionCommandService commandService;

    public EventInvasionController(EventInvasionQueryService queryService,
                                   EventInvasionCommandService commandService) {
        this.queryService = queryService;
        this.commandService = commandService;
    }

    /**
     * 查询条件统计
     *
     * @param query     查询条件
     * @param queryType 查询类型 0全部, 1与我有关
     */
    @GetMapping("/query/stat")
    public Result<QueryEventInvasionStatisticsVo> statistics(@Validated EventInvasionPageQuery query,
                                                             @RequestParam Integer queryType) {
        query.setProjectId(SecurityContextHolder.getProjectId());
        if (queryType == 1) {
            return Result.ok(queryService.statisticsByUserId(query, SecurityContextHolder.getUserId()));
        }
        return Result.ok(queryService.statistics(query));
    }

    /**
     * 分页查询入侵处置事件
     *
     * @param queryType 查询类型 0全部, 1与我有关
     */
    @GetMapping("/page")
    public Result<PageData<EventInvasionPageVo>> page(PageParam pageParam,
                                                      @Validated EventInvasionPageQuery query,
                                                      @RequestParam Integer queryType) {
        query.setProjectId(SecurityContextHolder.getProjectId());
        if (queryType == 1) {
            return Result.ok(PageData.getInstance(queryService.pageByUserId(pageParam, query, SecurityContextHolder.getUserId())));
        }
        return Result.ok(PageData.getInstance(queryService.page(pageParam, query)));
    }

    /**
     * 导出入侵处置事件
     *
     * @param queryType 查询类型 0全部, 1与我有关
     */
    @GetMapping("/export/excel")
    public void exportExcel(@Validated EventInvasionPageQuery query,
                            @RequestParam Integer queryType, HttpServletResponse response) {
        query.setProjectId(SecurityContextHolder.getProjectId());
        List<EventInvasionPageVo> exportList;
        if (queryType == 1) {
            exportList = queryService.listByUserId(query, SecurityContextHolder.getUserId());
        } else {
            exportList = queryService.list(query);
        }
        FastExcelOperUtils.exportXlsx(response, exportList, EventInvasionPageVo.class, "入侵处置事件");
    }

    /**
     * 查询上报处置信息
     *
     * @param eventId     入侵事件id
     * @param batchNumber 目标批次号
     * @param reportId    上报记录id
     */
    @GetMapping("/handle/info")
    public Result<EventInvasionHandleVo> handleInfo(@RequestParam(required = false) String reportId,
                                                    @RequestParam(required = false) String batchNumber,
                                                    @RequestParam(required = false) String eventId) {
        if (reportId == null && eventId == null) {
            return Result.fail(I18nUtil.isParamException());
        }
        if (reportId != null) {
            return Result.ok(queryService.getHandleInfoByReportId(reportId));
        }
        if (batchNumber != null) {
            return Result.ok(queryService.getHandleInfoByBatchNumber(batchNumber));
        }
        return Result.ok(queryService.getHandleInfoByEventId(eventId));
    }

    /**
     * 处置入侵事件
     */
    @PostMapping("/handle")
    public Result<String> add(@RequestBody @Validated EventInvasionHandleDto handleDto) {
        handleDto.validate();
        return commandService.handle(handleDto) ? Result.ok() : Result.fail();
    }
}
