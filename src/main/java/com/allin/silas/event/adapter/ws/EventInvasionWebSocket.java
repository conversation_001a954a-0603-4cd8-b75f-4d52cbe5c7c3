package com.allin.silas.event.adapter.ws;

import com.allin.silas.common.constant.WebSocketTypeConstants;
import com.allin.silas.event.app.service.EventInvasionQueryService;
import com.allin.view.ws.entity.WsMessage;
import com.allin.view.ws.event.WebSocketMessageEvent;
import com.allin.view.ws.send.WebSocketMessageSender;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

/**
 * 入侵事件ws
 *
 * <AUTHOR>
 * @since 2025/7/11
 */
@Component
public class EventInvasionWebSocket {

    private final EventInvasionQueryService queryService;

    public EventInvasionWebSocket(EventInvasionQueryService queryService) {
        this.queryService = queryService;
    }

    @Async
    @EventListener
    public void redDot(WebSocketMessageEvent messageEvent) {
        // 校验是不是自己关注的消息类型
        if (messageEvent.verify(WebSocketTypeConstants.EventInvasionRedDotWsMessageType)) {
            final WebSocketMessageEvent.WebSocketMessageEventData messageEventData = messageEvent.getData();
            final WebSocketSession session = messageEventData.getSession();
            final String userId = messageEventData.getUserId();
            final String projectId = messageEventData.getProjectId();
            WebSocketMessageSender.sendToSession(session, WsMessage.of(WebSocketTypeConstants.EventInvasionRedDotWsMessageType,
                    queryService.unhandled(userId, projectId)));
        }
    }
}
