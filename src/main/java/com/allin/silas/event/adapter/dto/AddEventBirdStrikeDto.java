package com.allin.silas.event.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-25 13:48:04
 */
@Data
public class AddEventBirdStrikeDto {
    /**
     * 事件名称
     */
    @NotBlank(message = "事件名称不能为空")
    private String eventName;
    /**
     * 发生时间
     */
    @NotNull(message = "发生时间不能为空")
    private LocalDateTime occurrenceTime;
    /**
     * 事发地点
     */
    @NotBlank(message = "事发地点不能为空")
    private String occurrenceLocation;
    /**
     * 航空运输企业
     */
    @NotBlank(message = "航空运输企业不能为空")
    private String airlineCompany;
    /**
     * 航空器型号
     */
    @NotBlank(message = "航空器型号不能为空")
    private String aircraftModel;
    /**
     * 航空器注册号
     */
    @NotBlank(message = "航空器注册号不能为空")
    private String aircraftRegistration;
    /**
     * 航班号
     */
    @NotBlank(message = "航班号不能为空")
    private String flightNumber;
    /**
     * 接报时间
     */
    @NotNull(message = "接报时间不能为空")
    private LocalDateTime reportTime;
    /**
     * 发生时段 1夜晚 2黄昏 3黎明 4白天
     */
    @NotNull(message = "发生时段不能为空")
    private Integer occurrencePeriod;
    /**
     * 起飞机场
     */
    @NotBlank(message = "起飞机场不能为空")
    private String departureAirport;
    /**
     * 着陆机场
     */
    @NotBlank(message = "着陆机场不能为空")
    private String landingAirport;
    /**
     * 撞击残留物
     */
    @NotBlank(message = "撞击残留物不能为空")
    private String impactResidue;
    /**
     * 是否已采集残留物或样本 1是 0否
     */
    @NotNull(message = "是否已采集残留物或样本不能为空")
    private Integer residueCollected;
    /**
     * 是否送检 1是 0否
     */
    @NotNull(message = "是否送检不能为空")
    private Integer residueSentForTesting;
    /**
     * 鉴定机构名称
     */
    private String testingAgency;
    /**
     * 对飞行的影响 1无 2中断起飞 3预防性着陆 4发动机停车 5航班延误 6航班取消 7换机
     */
    @NotNull(message = "对飞行的影响不能为空")
    private Integer flightImpact;
    /**
     * 离地高度
     */
    @NotBlank(message = "离地高度不能为空")
    private String altitude;
    /**
     * 指示空速
     */
    @NotBlank(message = "指示空速不能为空")
    private String indicatedAirspeed;
    /**
     * 飞行阶段 1停靠 2滑行 3起飞滑跑 4爬升 5航路飞行 6下降 7进近 8着陆滑跑 9不详
     */
    @NotNull(message = "飞行阶段不能为空")
    private Integer flightPhase;
    /**
     * 天空情况 1多云 2少云 3无云
     */
    @NotNull(message = "天空情况不能为空")
    private Integer skyCondition;
    /**
     * 降水情况 1雾 2雨 3雪 4无
     */
    @NotNull(message = "降水情况不能为空")
    private Integer precipitation;
    /**
     * 撞击物种
     */
    @NotBlank(message = "撞击物种不能为空")
    private String impactSpecies;
    /**
     * 物种体型 1大 2中 3小 4无
     */
    @NotNull(message = "物种体型不能为空")
    private Integer speciesSize;
    /**
     * 看到数量
     */
    private Integer observedQuantity;
    /**
     * 击中数量
     */
    private Integer actualImpactQuantity;
    /**
     * 修理或替换的损失
     */
    @NotBlank(message = "修理或替换的损失不能为空")
    private String repairCost;
    /**
     * 其他估算损失
     */
    @NotBlank(message = "其他估算损失不能为空")
    private String otherEstimatedLoss;
    /**
     * 飞行机组被告知鸟类或其他活动 1是 0否
     */
    @NotNull(message = "飞行机组被告知鸟类或其他活动不能为空")
    private Integer crewInformed;
    /**
     * 飞行机组通报空管部门 1是 0否
     */
    @NotNull(message = "飞行机组通报空管部门不能为空")
    private Integer atcNotified;
    /**
     * 鸟击航空器事件 1是 0否
     */
    @NotNull(message = "鸟击航空器事件不能为空")
    private Integer isBirdStrikeEvent;
    /**
     * 航空器起降方向
     */
    @NotBlank(message = "航空器起降方向不能为空")
    private String runwayDirection;
    /**
     * 后续跑道检查情况
     */
    private String runwayCheckStatus;
    /**
     * 是否机场责任范围 1是 0否
     */
    @NotNull(message = "是否机场责任范围不能为空")
    private Integer airportResponsibility;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 附件
     */
    private String attachment;
    /**
     * 停场时间
     */
    @NotNull(message = "停场时间不能为空")
    private Integer downtimeHours;
    /**
     * 被击或损伤部位
     */
    @Size(min = 1, message = "被击或损伤部位不能为空")
    private List<AddEventBirdStrikeDamageDto> eventBirdStrikeDamages;
}

