package com.allin.silas.event.adapter.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.format.DateTimeFormat;
import com.allin.silas.event.client.enums.EventInvasionTypeEnums;
import com.allin.silas.event.client.excel.DangerLevelToDescConverter;
import com.allin.silas.map.client.annotation.MapRegionIdToName;
import com.allin.silas.map.client.excel.MapRegionIdToNameConverter;
import com.allin.view.auth.serializer.annotation.UserIdToFullName;
import com.allin.view.base.execl.DefaultExportStyle;
import com.allin.view.base.execl.converter.IEnumsConverter;
import com.allin.view.base.execl.converter.IEnumsToStrConverter;
import com.allin.view.base.execl.converter.ListToStrConverter;
import com.allin.view.config.serialize.annotation.ApiFoxNoIgnore;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 入侵事件处置信息列表
 */
@Data
public class EventInvasionPageVo extends DefaultExportStyle {
    /**
     * 主键ID
     */
    @ExcelIgnore
    private String id;

    /**
     * 来源：0目标检测、1信息上报
     */
    @IEnumsConverter(replace = {"0_目标检测", "1_信息上报"})
    @ExcelProperty(value = "来源", converter = IEnumsToStrConverter.class)
    private Integer source;

    /**
     * 来源id
     */
    @ExcelIgnore
    private String sourceId;

    /**
     * 事件类型：0鸟情、1空飘物、2无人机
     */
    @IEnumsConverter(enums = EventInvasionTypeEnums.class)
    @ExcelProperty(value = "事件类型", converter = IEnumsToStrConverter.class)
    private Integer eventType;

    /**
     * 发现时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "发现时间")
    private LocalDateTime discoverTime;

    /**
     * 发现区域ID
     */
    @ExcelProperty(value = "发现区域", converter = MapRegionIdToNameConverter.class)
    @MapRegionIdToName(key = "discoverRegionName")
    private String discoverRegionId;

    /**
     * 发现区域名称
     */
    @ExcelIgnore
    @ApiFoxNoIgnore
    private String discoverRegionName;

    /**
     * 高度（米）
     */
    @ExcelProperty(value = "高度（米)")
    private Integer altitude;

    /**
     * 事件危险等级
     */
    @ExcelProperty(value = "事件危险等级", converter = DangerLevelToDescConverter.class)
    private Integer dangerLevel;

    /**
     * 处置状态：0未处置、1已处置
     */
    @IEnumsConverter(replace = {"0_未处置", "1_已处置"})
    @ExcelProperty(value = "处置状态", converter = IEnumsToStrConverter.class)
    private Integer handleStatus;

    /**
     * 处置人员
     */
    @UserIdToFullName(key = "handlePersonsFullNames")
    @ExcelProperty(value = "处置人员", converter = ListToStrConverter.class)
    private List<String> handlePersons;

    /**
     * 处置人员
     */
    @ApiFoxNoIgnore
    @ExcelIgnore
    private List<String> handlePersonsFullNames;
}