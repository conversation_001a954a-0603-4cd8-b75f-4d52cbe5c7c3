package com.allin.silas.event.adapter.query;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/25
 **/
@Data
public class EventBirdStrikeQuery {
    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 发生开始时间
     */
    private LocalDateTime occurrenceStartTime;

    /**
     * 发生结束时间
     */
    private LocalDateTime occurrenceEndTime;

    /**
     * 航空运输企业
     */
    private List<String> airlineCompany;

    /**
     * 航班号
     */
    private String flightNumber;

    /**
     * 发生时段 1夜晚 2黄昏 3黎明 4白天
     */
    @Min(1)
    @Max(4)
    private Integer occurrencePeriod;
}
