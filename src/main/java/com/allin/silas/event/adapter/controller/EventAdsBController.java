package com.allin.silas.event.adapter.controller;

import com.allin.silas.event.adapter.query.EventAdsBQuery;
import com.allin.silas.event.adapter.vo.EventAdsBPageVo;
import com.allin.silas.event.app.service.EventAdsBCommandService;
import com.allin.silas.event.app.service.EventAdsBQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.FastExcelOperUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 事件管理/ADS-B信息事件
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/event/ads_b")
public class EventAdsBController {

    private final EventAdsBQueryService queryService;

    private final EventAdsBCommandService commandService;

    public EventAdsBController(EventAdsBQueryService queryService, EventAdsBCommandService commandService) {
        this.queryService = queryService;
        this.commandService = commandService;
    }

    /**
     * 分页查询ADS-B信息事件
     */
    @GetMapping("/page")
    public Result<PageData<EventAdsBPageVo>> page(PageParam pageParam,
                                                  @Validated EventAdsBQuery query) {
        query.setProjectId(SecurityContextHolder.getProjectId());
        return Result.ok(PageData.getInstance(queryService.page(pageParam, query)));
    }

    /**
     * 分页查询某条ADS-B信息事件
     */
    @GetMapping("/page/callsign")
    public Result<PageData<EventAdsBPageVo>> pageByCallsign(PageParam pageParam,
                                                            @RequestParam LocalDate date,
                                                            @RequestParam String callsign) {
        return Result.ok(PageData.getInstance(queryService.pageByCallsign(pageParam, date, callsign)));
    }

    /**
     * 查询某条ADS-B信息事件轨迹
     */
    @GetMapping("/trace")
    public Result<List<EventAdsBPageVo>> trace(@RequestParam LocalDate date,
                                               @RequestParam String callsign) {
        return Result.ok(queryService.listByCallsign(date, callsign));
    }

    /**
     * 导出ADS-B信息事件
     */
    @GetMapping("/export/excel")
    public void export(@Validated EventAdsBQuery query, HttpServletResponse response) {
        query.setProjectId(SecurityContextHolder.getProjectId());
        final Page<EventAdsBPageVo> page = queryService.page(new PageParam(-1, -1), query);
        FastExcelOperUtils.exportXlsx(response, page.getRecords(), EventAdsBPageVo.class, "ADS-B信息事件");
    }

    /**
     * 导出某条ADS-B信息事件
     */
    @GetMapping("/export/callsign")
    public void exportByCallsign(@RequestParam LocalDate date,
                                 @RequestParam String callsign,
                                 HttpServletResponse response) {
        final List<EventAdsBPageVo> vos = queryService.listByCallsign(date, callsign);
        FastExcelOperUtils.exportXlsx(response, vos, EventAdsBPageVo.class, "ADS-B信息事件");
    }

    /**
     * 删除ADS-B信息事件
     */
    @DeleteMapping("/{id}")
    public Result<Void> del(@PathVariable String id) {
        return commandService.del(id) ? Result.ok() : Result.fail();
    }

}
