package com.allin.silas.event.adapter.vo;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.event.app.entity.EventInvasionHandle;
import com.allin.view.config.serialize.annotation.ApiFoxNoIgnore;
import com.allin.view.file.serializer.FileIdToFileInfo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 入侵事件处置信息表
 */
@Data
public class EventInvasionHandleVo {

    /**
     * 处置时间
     */
    private LocalDateTime handleTime;

    /**
     * 处置措施
     */
    private List<String> handleMethod;

    /**
     * 处置效果
     */
    private List<String> handleResult;

    /**
     * 猎枪耗弹数量（仅在措施为猎枪时填写）
     */
    private Integer shotgunAmmoCount;

    /**
     * 钝雷弹耗弹数量（仅在措施为钝雷弹时填写）
     */
    private Integer thunderAmmoCount;

    /**
     * 是否影响航班：0未影响、1已影响
     */
    private Integer affectFlight;

    /**
     * 是否通报空管：0未通报、1已通报
     */
    private Integer notifyAtc;

    /**
     * 是否通报公安机关：0未通报、1已通报
     */
    private Integer notifyPolice;

    /**
     * 附件id
     */
    @FileIdToFileInfo(key = "attachmentInfo")
    private String attachmentId;

    /**
     * 附件信息
     */
    @ApiFoxNoIgnore
    private String attachmentInfo;

    /**
     * 处置人列表
     */
    private List<String> handlePersons;

    public static EventInvasionHandleVo of(EventInvasionHandle eventInvasionHandle, List<String> handlePersons) {
        final EventInvasionHandleVo vo = new EventInvasionHandleVo();
        vo.setHandlePersons(handlePersons);
        if (eventInvasionHandle == null) {
            return vo;
        }
        BeanUtils.copyProperties(eventInvasionHandle, vo);
        vo.setHandleMethod(StrUtil.split(eventInvasionHandle.getHandleMethod(), ","));
        vo.setHandleResult(StrUtil.split(eventInvasionHandle.getHandleResult(), ","));
        return vo;
    }
}