package com.allin.silas.event.client.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 推送处置事件
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TargetAutoPushEventInvasionDto {

    /**
     * 来源id
     */
    @NotBlank
    private String sourceId;

    /**
     * 事件类型：0鸟情、1空飘物、2无人机
     */
    @NotNull
    private Integer eventType;

    /**
     * 发现时间
     */
    @NotNull
    private LocalDateTime discoverTime;

    /**
     * 发现区域ID
     */
    private String discoverRegionId;

    /**
     * 发现经度
     */
    @NotNull
    private Double discoverLongitude;

    /**
     * 发现纬度
     */
    @NotNull
    private Double discoverLatitude;

    /**
     * 高度（米）
     */
    @NotNull
    private Integer Altitude;

    /**
     * 目标危险等级
     */
    @NotNull
    private Integer dangerLevel;
}
