package com.allin.silas.event.client.excel;

import cn.idev.excel.converters.Converter;
import cn.idev.excel.converters.WriteConverterContext;
import cn.idev.excel.metadata.data.WriteCellData;
import lombok.extern.slf4j.Slf4j;

/**
 * Excel 事件风险等级转换器
 * <p>
 * 事件风险等级=碰撞可能性x目标危险等级。
 * 低风险（1-4）
 * 中风险（5-9）
 * 高风险（10-16）
 * 极高风险（20-25）
 *
 * <AUTHOR>
 * @since 2025/7/8
 */
@Slf4j
public class DangerLevelToDescConverter implements Converter<Integer> {


    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) {
        final Integer dangerLevel = context.getValue();
        if (dangerLevel == null) {
            return new WriteCellData<>("-");
        }
        //  低风险（1-4）
        //  中风险（5-9）
        //  高风险（10-16）
        //  极高风险（20-25）
        if (dangerLevel >= 1 && dangerLevel <= 4) {
            return new WriteCellData<>("低风险");
        }
        if (dangerLevel >= 5 && dangerLevel <= 9) {
            return new WriteCellData<>("中风险");
        }
        if (dangerLevel >= 10 && dangerLevel <= 16) {
            return new WriteCellData<>("高风险");
        }
        if (dangerLevel >= 20 && dangerLevel <= 25) {
            return new WriteCellData<>("极高风险");
        }
        return new WriteCellData<>("-");
    }
}
