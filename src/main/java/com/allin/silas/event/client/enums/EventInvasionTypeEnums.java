package com.allin.silas.event.client.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 入侵事件类型枚举
 * 0鸟情、1空飘物、2无人机
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
public enum EventInvasionTypeEnums implements IEnums {

    BIRD_QUA(0, "鸟情"),
    AIR_OBJECT(1, "空飘物"),
    UAV(2, "无人机");

    private final Integer code;

    private final String desc;

    EventInvasionTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }


}
