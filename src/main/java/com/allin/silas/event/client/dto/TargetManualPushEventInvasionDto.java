package com.allin.silas.event.client.dto;

import com.allin.silas.event.adapter.dto.EventInvasionHandleDto;
import com.allin.silas.event.app.entity.EventInvasionHandle;
import com.allin.silas.event.app.entity.EventInvasionHandlePerson;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 推送处置事件
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TargetManualPushEventInvasionDto {

    /**
     * 来源id
     */
    @NotBlank
    private String sourceId;

    /**
     * 事件类型：0鸟情、1空飘物、2无人机
     */
    @NotNull
    private String eventType;

    /**
     * 发现时间
     */
    @NotNull
    private LocalDateTime discoverTime;

    /**
     * 发现区域ID
     */
    private String discoverRegionId;

    /**
     * 发现经度
     */
    @NotNull
    private Double discoverLongitude;

    /**
     * 发现纬度
     */
    @NotNull
    private Double discoverLatitude;

    /**
     * 高度（米）
     */
    @NotNull
    private Integer altitude;

    /**
     * 目标危险等级
     */
    @NotNull
    private Integer dangerLevel;

    /**
     * 处置信息
     */
    @Valid
    @NotNull
    private ReportHandleEventInvasionDto handleEventInvasionDto;

    public TargetManualPushEventInvasionDto(ReportHandleEventInvasionDto handleEventInvasionDto) {
        this.handleEventInvasionDto = handleEventInvasionDto;
    }

    /**
     * 处置信息
     *
     * <AUTHOR>
     * @since 2025/7/5
     */
    @Data
    public static class ReportHandleEventInvasionDto {

        /**
         * 处置时间
         */
        @NotNull
        private LocalDateTime handleTime;

        /**
         * 处置措施
         */
        @NotEmpty
        private List<@NotBlank String> handleMethod;

        /**
         * 猎枪耗弹数量（仅在措施为猎枪时填写）
         */
        private Integer shotgunAmmoCount;

        /**
         * 钝雷弹耗弹数量（仅在措施为钝雷弹时填写）
         */
        private Integer thunderAmmoCount;

        /**
         * 处置效果
         */
        @NotEmpty
        private List<@NotBlank String> handleResult;

        /**
         * 是否影响航班：0未影响、1已影响
         */
        @NotNull
        private Integer affectFlight;

        /**
         * 是否通报空管：0未通报、1已通报
         */
        @NotNull
        private Integer notifyAtc;

        /**
         * 是否通报公安机关：0未通报、1已通报
         */
        @NotNull
        private Integer notifyPolice;

        /**
         * 附件id
         */
        private String attachmentId;

        /**
         * 处置人列表
         */
        @NotEmpty
        private List<@NotBlank String> handlePersons;

        public void validate() {
            EventInvasionHandleDto.validate(getHandleMethod(), shotgunAmmoCount, thunderAmmoCount);
        }

        /**
         * 转换成处置事件信息实体
         */
        public EventInvasionHandle toEventInvasionHandle(String eventInvasionId) {
            final EventInvasionHandle eventInvasionHandle = new EventInvasionHandle();
            BeanUtils.copyProperties(this, eventInvasionHandle);
            eventInvasionHandle.setEventInvasionId(eventInvasionId);
            return eventInvasionHandle;
        }

        /**
         * 转换成处置人员实体
         */
        public List<EventInvasionHandlePerson> toPersons(String eventInvasionId) {
            return this.handlePersons.stream()
                    .map(handlePerson -> {
                        final EventInvasionHandlePerson eventInvasionHandlePerson = new EventInvasionHandlePerson();
                        eventInvasionHandlePerson.setEventInvasionId(eventInvasionId);
                        eventInvasionHandlePerson.setHandleUserId(handlePerson);
                        return eventInvasionHandlePerson;
                    }).toList();
        }
    }
}
