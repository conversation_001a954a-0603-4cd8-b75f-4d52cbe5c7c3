package com.allin.silas.event.client;

import com.allin.silas.event.client.dto.ReportPushEventInvasionDto;
import com.allin.silas.event.client.dto.TargetAutoPushEventInvasionDto;
import com.allin.silas.event.client.dto.TargetManualPushEventInvasionDto;

/**
 * 入侵处置事件服务门面
 * <p>
 * 对外统一封装调用能力
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
public interface EventInvasionFacade {

    /**
     * 信息上报自动推送处置事件
     */
    boolean reportPushEvent(ReportPushEventInvasionDto dto);

    /**
     * 目标检测自动推送处置事件
     */
    boolean targetAutoPushEvent(TargetAutoPushEventInvasionDto dto);
    
    /**
     * 目标检测手动推送处置事件
     */
    boolean targetManualPushEvent(TargetManualPushEventInvasionDto dto);
}
