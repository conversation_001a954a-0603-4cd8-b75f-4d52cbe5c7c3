package com.allin.silas;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

import java.util.TimeZone;

/**
 * silas 系统启动入口
 *
 * <AUTHOR>
 * @since 2025/4/26
 */
@EnableCaching
@MapperScan("com.allin.silas.**.repository")
@SpringBootApplication
public class AllinSilasBackendApplication {

    public static void main(String[] args) {
        // 设置时区
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
        SpringApplication.run(AllinSilasBackendApplication.class, args);
    }

}
