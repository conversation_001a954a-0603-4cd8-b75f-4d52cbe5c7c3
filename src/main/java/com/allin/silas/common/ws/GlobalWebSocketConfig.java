package com.allin.silas.common.ws;

import com.allin.view.ws.handler.CustomParamWebSocketHandler;
import com.allin.view.ws.properties.WebSocketProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * websocket 服务配置
 * <p>
 * 最多只能允许配置4个
 *
 * <AUTHOR>
 * @since 2025/7/11
 */
@Configuration
public class GlobalWebSocketConfig {

    /**
     * 用户websocket服务
     */
    @Bean
    public CustomParamWebSocketHandler redDotWebSocketHandler(WebSocketProperties properties) {
        return new CustomParamWebSocketHandler(properties,
                "/websocket/user/*", "/websocket/user/{userId}", "userId");
    }
}