package com.allin.silas.common.infra.repository;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 通用表操作
 *
 * <AUTHOR>
 * @since 2025/6/25
 */
@Mapper
public interface CommonTableMapper {

    /**
     * 判断表是否存在
     */
    Integer existsTable(@Param("tableName") String tableName);

    /**
     * 删除表
     */
    void dropTable(@Param("tableName") String tableName);
}
