package com.allin.silas.common.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Wrappers 时间区间工具类
 *
 * <AUTHOR>
 * @since 2025/7/8
 **/
public class QueryWrapperUtils {

    private QueryWrapperUtils() {
    }

    /**
     * 构建时间区间查询
     *
     * @param wrapper    查询条件
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @param timeColumn 时间字段
     */
    public static <T> void buildDateRangeQuery(LambdaQueryWrapper<T> wrapper,
                                               LocalDateTime startDate,
                                               LocalDateTime endDate,
                                               SFunction<T, ?> timeColumn) {
        if (Objects.nonNull(startDate)) {
            wrapper.ge(timeColumn, startDate);
        }

        if (Objects.nonNull(endDate)) {
            wrapper.le(timeColumn, endDate);
        }
    }
}
