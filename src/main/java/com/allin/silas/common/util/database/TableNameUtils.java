package com.allin.silas.common.util.database;

import cn.hutool.extra.spring.SpringUtil;
import com.allin.silas.common.infra.repository.CommonTableMapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 分表表名生成工具
 *
 * <AUTHOR>
 * @since 2025/6/25
 */
public class TableNameUtils {

    private static final String ADS_B = "event_ads_b";

    private static final String VISUAL_TARGET_MERGED_INFO = "visual_target_merged_info";

    private static final String VISUAL_TARGET_ORIGINAL_INFO = "visual_target_original_info";

    private static final String RADAR_TARGET_ORIGINAL_INFO = "radar_target_original_info";

    private static final String RADAR_TARGET_ORIGINAL_EXTEND_INFO = "radar_target_original_extend_info";

    private static final String RADAR_TARGET_MERGED_INFO = "radar_target_merged_info";

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    private TableNameUtils() {
    }

    /**
     * 生成按天分区的表名
     */
    private static String getTableName(LocalDate localDate, String tableNamePrefix) {
        return String.format("%s_%s", tableNamePrefix, localDate.format(formatter));
    }

    /**
     * 根据时间生成AdsB表名
     */
    public static String getAdsBInfo(LocalDate date) {
        return getTableName(date, ADS_B);
    }

    /**
     * 根据时间生成原始目标信息表名
     */
    public static String getVisualTargetOriginalInfo(LocalDateTime localDateTime) {
        return getTableName(localDateTime.toLocalDate(), VISUAL_TARGET_ORIGINAL_INFO);
    }

    /**
     * 根据时间生成原始目标信息表名
     */
    public static String getVisualTargetOriginalInfo(LocalDate date) {
        return getTableName(date, VISUAL_TARGET_ORIGINAL_INFO);
    }

    /**
     * 根据日期范围获取原始目标表名
     */
    public static List<String> getVisualTargetOriginalInfo(LocalDate startDate, LocalDate endDate) {
        return getTableNamesByDateRange(startDate, endDate, VISUAL_TARGET_ORIGINAL_INFO);
    }


    /**
     * 根据时间生成合并目标信息表名
     */
    public static String getVisualTargetMergedInfo(LocalDateTime localDateTime) {
        return getTableName(localDateTime.toLocalDate(), VISUAL_TARGET_MERGED_INFO);
    }

    /**
     * 根据时间生成合并目标信息表名
     */
    public static String getVisualTargetMergedInfo(LocalDate date) {
        return getTableName(date, VISUAL_TARGET_MERGED_INFO);
    }

    /**
     * 根据日期范围获取合并目标表名
     */
    public static List<String> getVisualTargetMergedInfo(LocalDate startDate, LocalDate endDate) {
        return getTableNamesByDateRange(startDate, endDate, VISUAL_TARGET_MERGED_INFO);
    }

    /**
     * 根据时间生成原始目标信息表名
     */
    public static String getRadarTargetOriginalInfo(LocalDateTime localDateTime) {
        return getTableName(localDateTime.toLocalDate(), RADAR_TARGET_ORIGINAL_INFO);
    }

    /**
     * 根据时间生成原始目标信息表名
     */
    public static String getRadarTargetOriginalInfo(LocalDate date) {
        return getTableName(date, RADAR_TARGET_ORIGINAL_INFO);
    }

    /**
     * 根据日期范围获取原始目标表名
     */
    public static List<String> getRadarTargetOriginalInfo(LocalDate startDate, LocalDate endDate) {
        return getTableNamesByDateRange(startDate, endDate, RADAR_TARGET_ORIGINAL_INFO);
    }

    /**
     * 根据时间生成合并目标信息表名
     */
    public static String getRadarTargetMergedInfo(LocalDateTime localDateTime) {
        return getTableName(localDateTime.toLocalDate(), RADAR_TARGET_MERGED_INFO);
    }

    /**
     * 根据时间生成合并目标信息表名
     */
    public static String getRadarTargetMergedInfo(LocalDate date) {
        return getTableName(date, RADAR_TARGET_MERGED_INFO);
    }

    /**
     * 根据日期范围获取合并目标表名
     */
    public static List<String> getRadarTargetMergedInfo(LocalDate startDate, LocalDate endDate) {
        return getTableNamesByDateRange(startDate, endDate, RADAR_TARGET_MERGED_INFO);
    }

    /**
     * 根据时间生成合并目标信息表名
     */
    public static String getRadarTargetOriginalExtendInfo(LocalDateTime localDateTime) {
        return getTableName(localDateTime.toLocalDate(), RADAR_TARGET_ORIGINAL_EXTEND_INFO);
    }

    /**
     * 根据时间生成合并目标信息表名
     */
    public static String getRadarTargetOriginalExtendInfo(LocalDate date) {
        return getTableName(date, RADAR_TARGET_ORIGINAL_EXTEND_INFO);
    }

    /**
     * 根据日期范围获取合并目标表名
     */
    public static List<String> getRadarTargetOriginalExtendInfo(LocalDate startDate, LocalDate endDate) {
        return getTableNamesByDateRange(startDate, endDate, RADAR_TARGET_ORIGINAL_EXTEND_INFO);
    }

    /**
     * 根据日期范围获取所有表名
     */
    public static List<String> getTableNamesByDateRange(LocalDate startDate,
                                                        LocalDate endDate,
                                                        String tableNamePrefix) {
        CommonTableMapper commonTableMapper = SpringUtil.getBean(CommonTableMapper.class);
        if (startDate == null || endDate == null) {
            return Collections.emptyList();
        }

        List<String> tableNames = new ArrayList<>();
        LocalDate currentDate = startDate;

        while (currentDate.isBefore(endDate) || currentDate.isEqual(endDate)) {
            String tableName = TableNameUtils.getTableName(currentDate, tableNamePrefix);

            // 检查表是否存在
            if (commonTableMapper.existsTable(tableName) > 0) {
                tableNames.add(tableName);
            }
            // 加一天
            currentDate = currentDate.plusDays(1);
        }

        return tableNames;
    }
}
