package com.allin.silas.common.util.tcp;

import com.allin.silas.dev.detect.constant.TcpMessageConstants;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.nio.charset.StandardCharsets;

/**
 * 设备相关的ByteBuf操作
 * <AUTHOR>
 * @since 2025/7/14
 */
public class DevByteBufUtils {


    /**
     * 获取数据不改变index
     */
    public static String getStringFromByteBuf(ByteBuf in, int index, int length) {
        byte[] bytes = new byte[length];
        in.getBytes(index, bytes);
        return new String(bytes, StandardCharsets.UTF_8);
    }


    /**
     * 将float数据转byte[]
     */
    public static byte[] float2byte(float f) {

        // 把float转换为byte[]
        int bit = Float.floatToIntBits(f);

        byte[] b = new byte[4];
        for (int i = 0; i < 4; i++) {
            b[i] = (byte) (bit >> (24 - i * 8));
        }

        // 翻转数组
        int len = b.length;
        // 建立一个与源数组元素类型相同的数组
        byte[] dest = new byte[len];
        // 为了防止修改源数组，将源数组拷贝一份副本
        System.arraycopy(b, 0, dest, 0, len);
        byte temp;
        // 将顺位第i个与倒数第i个交换
        for (int i = 0; i < len / 2; ++i) {
            temp = dest[i];
            dest[i] = dest[len - i - 1];
            dest[len - i - 1] = temp;
        }
        return dest;
    }

    /**
     * 获取驱鸟设备CRC校验码
     */
    public static int getCrc(byte[] bytes) {
        int crc = 0xFFFF;
        int poly = 0xa001;
        int i;
        int j;
        for (i = 0; i < bytes.length; i++) {
            crc ^= (bytes[i] & 0xFF);
            for (j = 0; j < 8; j++) {
                if ((crc & 1) != 0) {
                    crc >>= 1;
                    crc ^= poly;
                } else {
                    crc >>= 1;
                }
            }
        }
        return crc;
    }

    /**
     * 读取字节数据成字符串
     */
    public static String readToString(int length, ByteBuf byteBuf) {
        byte[] bytes = new byte[length];
        byteBuf.readBytes(bytes);
        return new String(bytes);
    }

    /**
     * 获取驱鸟设备LRC校验
     */

    public static int calculateLRC(byte[] data) {
        int sum = 0;
        for (byte hex : data) {
            sum += hex;
        }
        int mod = sum % 256;
        return 256 - mod;
    }

    /**
     * 获取TCP头尾数据字符串
     */
    public static String getHeadOrTail(ByteBuf in, int startIndex, int length) {
        StringBuilder sb = new StringBuilder(length * 2);
        for (int i = 0; i < length; i++) {
            int value = in.getUnsignedByte(startIndex + i);
            // 转为大写16进制，补足两位
            sb.append(String.format("%02X", value));
        }
        return sb.toString();
    }


    /**
     * 获取TCP协议字符数据
     */
    public static String parseRelatedFrameData(ByteBuf byteBuf, long relatedFrame, int dataType) {
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < relatedFrame; i++) {
            if (dataType == 0) {
                str.append(byteBuf.readUnsignedIntLE());
            } else if (dataType == 1) {
                str.append(byteBuf.readFloatLE());
            } else if (dataType == 2) {
                byteBuf.readUnsignedByte();
            } else if (dataType == 3) {
                byteBuf.readLongLE();
            }
            if (i < relatedFrame - 1) {
                str.append(",");
            }
        }
        if (",".contentEquals(str)) {
            return null;
        }
        return str.toString();
    }

    /**
     * 生成心跳包
     */
    public static ByteBuf genHeartBeatBytes() {
        ByteBuf byteBuf = Unpooled.buffer(15);
        // 数据报头
        byteBuf.writeBytes(TcpMessageConstants.HEADER);
        // 指令标识
        byteBuf.writeBytes(TcpMessageConstants.COMMAND_HEARTBEAT);
        // 数据包大小
        byteBuf.writeLongLE(0L);
        byteBuf.writeByte(0x00);
        byteBuf.writeBytes(TcpMessageConstants.TAIL);
        return byteBuf;
    }

    /**
     * 获取TCP指令字节数据
     */
    public static ByteBuf genTcpCmdBytes(int dataLen,int cmd) {
        ByteBuf byteBuf = Unpooled.buffer(15);
        // 数据报头
        byteBuf.writeBytes(TcpMessageConstants.HEADER);
        // 指令标识
        byteBuf.writeShortLE(cmd);
        // 数据包大小
        byteBuf.writeLongLE(dataLen);
        byteBuf.writeByte(0x00);
        byteBuf.writeBytes(TcpMessageConstants.TAIL);
        return byteBuf;
    }

    /**
     * 字节数组合并
     */
    public static byte[] byteMerge(byte[] bt1, byte[] bt2) {
        byte[] data = new byte[bt1.length + bt2.length];
        System.arraycopy(bt1, 0, data, 0, bt1.length);
        System.arraycopy(bt2, 0, data, bt1.length, bt2.length);
        return data;
    }


}
