package com.allin.silas.common.util.geo;

import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.geotools.data.geojson.GeoJSONReader;

/**
 * geojson 工具类
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@Slf4j
public class GeoJsonUtils {

    private GeoJsonUtils() {
    }

    /**
     * 校验是否为 geojson 的 geometry 对象
     *
     * @throws ValidationFailureException 校验失败抛出验证异常
     */
    public static void checkGeoJsonGeometry(String geoJson) throws ValidationFailureException {
        checkGeoJsonGeometry(geoJson, null);
    }

    /**
     * 校验是否为 geojson 的 geometry 对象
     *
     * @throws ValidationFailureException 校验失败抛出验证异常
     */
    public static void checkGeoJsonGeometry(String geoJson, String filedName) throws ValidationFailureException {
        if (geoJson == null) {
            return;
        }
        try {
            GeoJSONReader.parseGeometry(geoJson);
        } catch (Exception e) {
            if (filedName == null) {
                filedName = "";
            }
            throw new ValidationFailureException(I18nUtil.getMessage("silas.error.geojson.geometry", filedName));
        }
    }
}
