package com.allin.silas.common.util.geo;

import org.geotools.referencing.GeodeticCalculator;
import org.geotools.referencing.crs.DefaultGeographicCRS;
import org.locationtech.jts.geom.Coordinate;

import java.awt.geom.Point2D;

/**
 * 坐标系工具类
 *
 * <AUTHOR>
 * @since 2025/6/18
 */
public class CoordinateUtils {

    // 地球半径，单位为米
    public static final double earthRadius = 6371e3;

    /**
     * 计算两个经纬度之间的方位角，需要使用 wgs84 坐标系
     *
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 方位角，单位为度
     */
    public static double calculateAzimuth(double lat1, double lon1,
                                          double lat2, double lon2) {
        GeodeticCalculator gc = new GeodeticCalculator(DefaultGeographicCRS.WGS84);
        gc.setStartingGeographicPoint(lon1, lat1);
        gc.setDestinationGeographicPoint(lon2, lat2);
        return gc.getAzimuth();
    }

    /**
     * 获取两个坐标之间的方位角
     *
     * @param from 起点坐标
     * @param to   终点坐标
     * @return 方位角，单位为度
     */
    public static double calculateAzimuth(Coordinate from, Coordinate to) {
        return calculateAzimuth(from.y, from.x, to.y, to.x);
    }

    /**
     * 根据给定的方位角和距离移动一个坐标点
     *
     * @param origin         起始坐标点
     * @param azimuthDeg     方位角，单位为度
     * @param distanceMeters 距离，单位为米
     */
    public static Coordinate moveTo(Coordinate origin, double azimuthDeg, double distanceMeters) {
        GeodeticCalculator gc = new GeodeticCalculator(DefaultGeographicCRS.WGS84);
        gc.setStartingGeographicPoint(origin.x, origin.y);
        gc.setDirection(azimuthDeg, distanceMeters);
        Point2D dest = gc.getDestinationGeographicPoint();
        return new Coordinate(dest.getX(), dest.getY());
    }

    /**
     * 根据给定的方位角和距离移动一个坐标点
     *
     * @param origin         起始坐标点
     * @param azimuthDeg     方位角，单位为度
     * @param distanceMeters 距离，单位为米
     * @param heightMeters   高度，单位为米
     */
    public static Coordinate moveTo3D(Coordinate origin, double azimuthDeg, double distanceMeters, double heightMeters) {
        GeodeticCalculator gc = new GeodeticCalculator(DefaultGeographicCRS.WGS84);
        gc.setStartingGeographicPoint(origin.x, origin.y);
        gc.setDirection(azimuthDeg, distanceMeters);
        Point2D dest = gc.getDestinationGeographicPoint();
        return new Coordinate(dest.getX(), dest.getY(), heightMeters);
    }
}
