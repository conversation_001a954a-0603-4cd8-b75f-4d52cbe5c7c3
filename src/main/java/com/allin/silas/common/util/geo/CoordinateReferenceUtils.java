package com.allin.silas.common.util.geo;

import lombok.extern.slf4j.Slf4j;
import org.geotools.api.referencing.crs.CoordinateReferenceSystem;
import org.geotools.api.referencing.operation.MathTransform;
import org.geotools.referencing.CRS;
import org.geotools.referencing.crs.DefaultGeographicCRS;
import org.locationtech.jts.geom.Geometry;

/**
 * 坐标系转换
 *
 * <AUTHOR>
 * @see <a href="https://docs.geotools.org/latest/userguide/library/referencing/crs.html">CRS 坐标系转换</a>
 * @since 2025/6/18
 */
@Slf4j
public class CoordinateReferenceUtils {

    /**
     * 通用横轴墨卡托
     */
    private static final String projection = "AUTO:42001";


    /**
     * 将几何对象转换为通用横轴墨卡托坐标系
     */
    public static MathTransform toProj(Geometry geometry) throws Exception {
        CoordinateReferenceSystem autoCRS = CRS.decode(projection
                + "," + geometry.getCentroid().getX()
                + "," + geometry.getCentroid().getY()
        );
        return CRS.findMathTransform(DefaultGeographicCRS.WGS84, autoCRS, true);
    }

    public static MathTransform toWGS84(Geometry geometry) throws Exception {
        CoordinateReferenceSystem autoCRS = CRS.decode(projection
                + "," + geometry.getCentroid().getX()
                + "," + geometry.getCentroid().getY()
        );
        return CRS.findMathTransform(autoCRS, DefaultGeographicCRS.WGS84, true);
    }
}
