package com.allin.silas.common.integration;


import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

/**
 * 未知数据兜底处理
 *
 * <AUTHOR>
 * @since 2025/7/12
 */
@Component
@Slf4j
public class UnknownDataHandler implements MessageHandler {

    @ServiceActivator(inputChannel = "unknownDataChannel")
    @Override
    public void handleMessage(Message<?> message) throws MessagingException {
        log.info("未知数据：{}", JSON.toJSONString(message.getPayload()));
    }
}
