package com.allin.silas.common.mybatis.config;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.allin.view.config.filter.ThreadLocalClear;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 动态表名规则配置
 *
 * <AUTHOR>
 * @see com.allin.view.config.filter.ThreadLocalClearFilter
 * @since 2025/6/23
 */
@Component
public class MybatisDynamicTableNameHandler implements ThreadLocalClear, TableNameHandler {
    /**
     * 哪些表可以使用这个动态表名规则
     */
    private static final List<String> configTableInfoList = new ArrayList<>();

    private static final TransmittableThreadLocal<String> threadLocalSuffix = new TransmittableThreadLocal<>();

    /**
     * 设置分表的后缀
     *
     * @param suffix 后缀,例如20250623
     */
    public static void setSuffix(String suffix) {
        threadLocalSuffix.set(suffix);
    }

    public static void setSuffix(LocalDate localDate) {
        setSuffix(LocalDateTimeUtil.format(localDate, DatePattern.PURE_DATE_PATTERN));
    }

    static {
        // 配置分表的名称
        configTableInfoList.add("radar_target_merged_info");
        configTableInfoList.add("radar_target_original_extend_info");
        configTableInfoList.add("radar_target_original_info");
        configTableInfoList.add("visual_target_merged_info");
        configTableInfoList.add("visual_target_original_info");
    }

    @Override
    public String dynamicTableName(String sql, String tableName) {
        if (configTableInfoList.contains(tableName)) {
            String suffix = threadLocalSuffix.get();
            if (suffix == null) {
                suffix = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
            }
            return "%s_%s".formatted(tableName, suffix);
        }
        return tableName;
    }

    public static void clear() {
        threadLocalSuffix.remove();
    }

    /**
     * 自动清除线程变量
     */
    @Override
    public void autoClear() {
        threadLocalSuffix.remove();
    }
}
