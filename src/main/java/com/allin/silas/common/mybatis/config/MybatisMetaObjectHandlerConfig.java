package com.allin.silas.common.mybatis.config;

import com.allin.view.auth.context.SecurityContextHolder;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Mybatis 字段填充
 *
 * <AUTHOR>
 * @since 2023/8/16
 */
@Component
@Slf4j
public class MybatisMetaObjectHandlerConfig implements MetaObjectHandler {

    private static final String CREATED_TIME = "createdTime";

    public static final String CREATED_BY = "createdBy";

    private static final String UPDATED_TIME = "updatedTime";

    private static final String UPDATED_BY = "updatedBy";

    private static final String IS_DELETED = "isDeleted";

    private static final String DELETED = "deleted";

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, IS_DELETED, Integer.class, 0);
        this.strictInsertFill(metaObject, DELETED, Integer.class, 0);
        this.strictInsertFill(metaObject, CREATED_TIME, LocalDateTime::now, LocalDateTime.class);
        this.strictInsertFill(metaObject, UPDATED_TIME, LocalDateTime::now, LocalDateTime.class);
        try {
            SecurityContextHolder
                    .tryGetLoginUser()
                    .ifPresentOrElse(loginUser -> {
                        this.strictInsertFill(metaObject, CREATED_BY, String.class, loginUser.getUserId());
                        this.strictInsertFill(metaObject, UPDATED_BY, String.class, loginUser.getUserId());
                    }, () -> {
                        this.strictInsertFill(metaObject, CREATED_BY, String.class, "-");
                        this.strictInsertFill(metaObject, UPDATED_BY, String.class, "-");
                    });

            SecurityContextHolder.tryGetProjectId()
                    .ifPresent(projectId -> this.strictInsertFill(metaObject, "projectId", String.class, projectId));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 解决Mybatis plus字段填充不覆盖原有值问题
        metaObject.setValue(UPDATED_TIME, null);
        this.strictUpdateFill(metaObject, UPDATED_TIME, LocalDateTime.class, LocalDateTime.now());
        try {
            SecurityContextHolder
                    .tryGetLoginUser()
                    .ifPresentOrElse(loginUser ->
                                    this.strictUpdateFill(metaObject, UPDATED_BY, String.class, loginUser.getUserId()),
                            () -> {
                                this.strictInsertFill(metaObject, CREATED_BY, String.class, "-");
                                this.strictInsertFill(metaObject, UPDATED_BY, String.class, "-");
                            });
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
