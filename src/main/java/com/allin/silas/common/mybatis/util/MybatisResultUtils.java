package com.allin.silas.common.mybatis.util;

import cn.hutool.core.collection.CollUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * mybatis 结果集工具类
 *
 * <AUTHOR>
 * @since 2025/7/8
 */
public class MybatisResultUtils {

    /**
     * 将 List<Map<String, Object>> 转换为 Map<K, V>
     *
     * @param list        MyBatis 查询结果列表，每个 Map 包含 key 和 value 字段
     * @param keyMapper   key 的类型转换器
     * @param valueMapper value 的类型转换器
     * @param <K>         目标 Map 的 key 类型
     * @param <V>         目标 Map 的 value 类型
     * @return 转换后的 Map<K, V>
     */
    public static <K, V> Map<K, V> convertListToMap(List<Map<String, Object>> list,
                                                    Function<Object, K> keyMapper,
                                                    Function<Object, V> valueMapper) {

        Map<K, V> resultMap = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            for (Map<String, Object> item : list) {
                Object rawKey = item.get("key");
                Object rawValue = item.get("value");

                if (rawKey != null && rawValue != null) {
                    K key = keyMapper.apply(rawKey);
                    V value = valueMapper.apply(rawValue);
                    resultMap.put(key, value);
                }
            }
        }
        return resultMap;
    }
}
