package com.allin.silas.common.mybatis.config;

import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * Mybatis 多数据库配置类
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
@Configuration
public class DataBaseConfig {

    public static final String POSTGRE_SQL = "postgresql";

    @Bean
    public DatabaseIdProvider getDatabaseIdProvider() {
        Properties properties = new Properties();
        properties.setProperty("MySQL", "mysql");
        properties.setProperty("DM DBMS", "dm");
        properties.setProperty("PostgreSQL", "postgresql");
        DatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        databaseIdProvider.setProperties(properties);
        return databaseIdProvider;
    }

}
