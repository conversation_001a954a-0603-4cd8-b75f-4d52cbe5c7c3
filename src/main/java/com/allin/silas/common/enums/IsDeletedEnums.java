package com.allin.silas.common.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 软删除标识，0未删除，1已删除
 *
 * <AUTHOR>
 * @date 2025/05/07
 */
public enum IsDeletedEnums implements IEnums {
    /**
     * 0 未删除
     */
    FALSE(0),
    /**
     * 1 已删除
     */
    TRUE(1);

    private final Integer code;

    IsDeletedEnums(Integer code) {
        this.code = code;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
