package com.allin.silas.common.enums;

import com.allin.view.base.enums.base.IEnums;
import com.allin.view.ws.entity.WsMessage;

/**
 * websocket type枚举
 * <p>
 * 对应{@link WsMessage#getType()}
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
public enum WsMessageType implements IEnums {

    /**
     * 低空围栏目标类型
     */
    RANGE_TARGET("rangeTargets"),

    /**
     * 伺服状态信息
     */
    DEV_DETECT_SERVO("servoStatus"),

    /**
     * 设备心跳状态信息
     */
    DEV_DETECT_STATUS("devDetectStatus"),

    /**
     * 探测设备离线状态
     */
    DEV_DETECT_OFFLINE("devDetectOffline"),


    /**
     * 未知消息类型
     */
    UNKNOWN("unknown"),


    ;

    /**
     * type
     */
    private final String type;

    WsMessageType(String type) {
        this.type = type;
    }

    @Override
    public String getCode() {
        return type;
    }
}
