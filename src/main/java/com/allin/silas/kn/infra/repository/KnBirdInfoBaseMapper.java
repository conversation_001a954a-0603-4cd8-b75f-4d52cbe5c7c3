package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.adapter.vo.KnBirdInfoBaseVo;
import com.allin.silas.kn.app.entity.KnBirdInfoBase;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface KnBirdInfoBaseMapper extends BaseMapper<KnBirdInfoBase> {

    /**
     * 查询鸟种的基础信息
     */
    List<KnBirdInfoBaseVo> listBaseInfo();
}