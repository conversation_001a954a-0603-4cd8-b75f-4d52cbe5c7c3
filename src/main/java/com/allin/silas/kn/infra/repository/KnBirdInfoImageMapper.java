package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.adapter.vo.KnImageBaseInfoVo;
import com.allin.silas.kn.app.entity.KnBirdInfoImage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface KnBirdInfoImageMapper extends BaseMapper<KnBirdInfoImage> {

    /**
     * 根据id查询图片base64
     *
     * @param id 图片id
     * @return 图片base64
     */
    String getById(@Param("id") String id);

    /**
     * 查询系统内置图片列表
     *
     * @return 图片列表
     */
    List<KnImageBaseInfoVo> listBuiltIn();

    /**
     * 查询系统内置和自定义的图片列表
     *
     * @param chineseName 中文名
     * @return 图片列表
     */
    List<KnImageBaseInfoVo> listBuiltInAndProject(@Param("chineseName") String chineseName,
                                                  @Param("projectId") String projectId);
}