package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.app.entity.KnInsectInfoAlias;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface KnInsectInfoAliasMapper extends BaseMapper<KnInsectInfoAlias> {

    /**
     * 查询别名
     */
    List<String> listBuiltInAlias(@Param("chineseName") String chineseName);

    /**
     * 查询别名
     */
    List<String> listAliasByProject(@Param("chineseName") String chineseName,
                                    @Param("projectId") String projectId);

    /**
     * 查询系统内置别名
     */
    List<KnInsectInfoAlias> listBuiltInAliasObj();

    /**
     * 查询系统内置和项目自定义的别名
     */
    List<KnInsectInfoAlias> listBuiltInAndProject(@Param("projectId") String projectId);

}