package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.app.entity.KnInsectInfoActiveMonth;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface KnInsectInfoActiveMonthMapper extends BaseMapper<KnInsectInfoActiveMonth> {

    /**
     * 查询某草种的活动月份
     */
    List<Integer> listMonthByProjectId(@Param("chineseName") String chineseName,
                                       @Param("projectId") String projectId);
}