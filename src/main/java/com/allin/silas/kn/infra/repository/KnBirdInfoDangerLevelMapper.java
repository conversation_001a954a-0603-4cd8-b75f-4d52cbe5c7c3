package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.app.entity.KnBirdInfoDangerLevel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface KnBirdInfoDangerLevelMapper extends BaseMapper<KnBirdInfoDangerLevel> {

    /**
     * 根据中文名获取危险等级
     */
    @Nullable
    default KnBirdInfoDangerLevel getByChineseName(String chineseName) {
        return selectOne(Wrappers.lambdaQuery(KnBirdInfoDangerLevel.class)
                .eq(KnBirdInfoDangerLevel::getChineseName, chineseName));
    }
}