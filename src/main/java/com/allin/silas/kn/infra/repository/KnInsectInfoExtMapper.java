package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.app.entity.KnInsectInfoExt;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface KnInsectInfoExtMapper extends BaseMapper<KnInsectInfoExt> {

    /**
     * 查询扩展信息
     */
    default KnInsectInfoExt getByName(String chineseName, String projectId) {
        return selectOne(Wrappers.lambdaQuery(KnInsectInfoExt.class)
                .eq(KnInsectInfoExt::getChineseName, chineseName)
                .eq(KnInsectInfoExt::getProjectId, projectId));
    }
}