package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.app.entity.KnBirdInfoAlias;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface KnBirdInfoAliasMapper extends BaseMapper<KnBirdInfoAlias> {

    /**
     * 查询系统内置别名
     */
    Set<String> listBuiltInAlias(@Param("chineseName") String chineseName);

    /**
     * 查询自定义别名
     */
    Set<String> listAliasByProject(@Param("chineseName") String chineseName,
                                   @Param("projectId") String projectId);

    /**
     * 查询系统内置别名
     */
    List<KnBirdInfoAlias> listBuiltIn();

    /**
     * 查询系统内置和项目自定义的别名
     */
    List<KnBirdInfoAlias> listBuiltInAndProject(@Param("projectId") String projectId);

}