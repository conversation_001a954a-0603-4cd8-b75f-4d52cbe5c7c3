package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.app.entity.KnGrassInfoExt;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface KnGrassInfoExtMapper extends BaseMapper<KnGrassInfoExt> {
    /**
     * 查询扩展信息
     */
    default KnGrassInfoExt getByName(String chineseName, String projectId) {
        return selectOne(Wrappers.lambdaQuery(KnGrassInfoExt.class)
                .eq(KnGrassInfoExt::getChineseName, chineseName)
                .eq(KnGrassInfoExt::getProjectId, projectId));
    }
}