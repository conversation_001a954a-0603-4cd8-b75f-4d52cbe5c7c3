package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.adapter.vo.KnImageBaseInfoVo;
import com.allin.silas.kn.app.entity.KnGrassInfoImage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface KnGrassInfoImageMapper extends BaseMapper<KnGrassInfoImage> {

    /**
     * 根据id查询图片base64
     */
    String getById(@Param("id") String id);

    /**
     * 查询系统内置和自定义的图片列表
     */
    List<KnGrassInfoImage> listBuiltInAndProject(@Param("chineseName") String chineseName,
                                                 @Param("projectId") String projectId);

    /**
     * 查询系统内置的图片
     */
    List<KnImageBaseInfoVo> listBuiltIn();
}