package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.app.entity.KnGrassInfoActiveMonth;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface KnGrassInfoActiveMonthMapper extends BaseMapper<KnGrassInfoActiveMonth> {

    /**
     * 查询某虫种的活动月份
     */
    List<Integer> listMonthByProjectId(@Param("chineseName") String chineseName,
                                       @Param("projectId") String projectId);
}