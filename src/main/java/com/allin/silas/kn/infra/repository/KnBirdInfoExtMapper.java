package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.app.entity.KnBirdInfoExt;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface KnBirdInfoExtMapper extends BaseMapper<KnBirdInfoExt> {

    /**
     * 查询扩展信息
     */
    default KnBirdInfoExt getByName(String chineseName, String projectId) {
        return selectOne(Wrappers.lambdaQuery(KnBirdInfoExt.class)
                .eq(KnBirdInfoExt::getChineseName, chineseName)
                .eq(KnBirdInfoExt::getProjectId, projectId));
    }
}