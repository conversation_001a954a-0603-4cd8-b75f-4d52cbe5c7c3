package com.allin.silas.kn.infra.repository;

import com.allin.silas.kn.app.entity.KnGrassInfoAlias;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface KnGrassInfoAliasMapper extends BaseMapper<KnGrassInfoAlias> {

    /**
     * 查询系统内置别名
     */
    List<String> listBuiltInAlias(@Param("chineseName") String chineseName);

    /**
     * 查询系统内置和项目中的别名列表
     */
    List<String> listBuiltInAndProjectAlias(@Param("chineseName") String chineseName,
                                            @Param("projectId") String projectId);

    /**
     * 查询系统内置别名
     */
    List<KnGrassInfoAlias> listBuiltIn();

    /**
     * 查询项目以及系统内置的别名
     */
    List<KnGrassInfoAlias> listBuiltInAndProject(@Param("projectId") String projectId);
}