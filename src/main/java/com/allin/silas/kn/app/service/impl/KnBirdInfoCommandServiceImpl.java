package com.allin.silas.kn.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.allin.silas.kn.adapter.dto.EditBirdInfoDto;
import com.allin.silas.kn.adapter.dto.EditBirdInfoExtDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.adapter.dto.UploadImageDto;
import com.allin.silas.kn.adapter.vo.KnImageBaseInfoVo;
import com.allin.silas.kn.app.entity.KnBirdInfoImage;
import com.allin.silas.kn.app.manager.KnBirdInfoManager;
import com.allin.silas.kn.app.manager.KnImageManager;
import com.allin.silas.kn.app.service.KnBirdInfoCommandService;
import com.allin.silas.kn.infra.repository.KnBirdInfoImageMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 鸟种百科命令服务接口实现
 *
 * <AUTHOR>
 * @since 2025/4/30
 */
@Service
public class KnBirdInfoCommandServiceImpl implements KnBirdInfoCommandService {

    private final KnBirdInfoManager birdInfoManager;

    private final KnImageManager knImageManager;

    private final KnBirdInfoImageMapper birdInfoImageMapper;

    public KnBirdInfoCommandServiceImpl(KnBirdInfoManager birdInfoManager,
                                        KnImageManager knImageManager,
                                        KnBirdInfoImageMapper birdInfoImageMapper) {
        this.birdInfoManager = birdInfoManager;
        this.knImageManager = knImageManager;
        this.birdInfoImageMapper = birdInfoImageMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized void editInfo(EditBirdInfoDto editInfoDto) {
        // 参数校验
        final List<String> imageIds = birdInfoImageMapper.listBuiltIn().stream().map(KnImageBaseInfoVo::getId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(editInfoDto.getImageIds())) {
            imageIds.addAll(editInfoDto.getImageIds());
        }
        if (!imageIds.contains(editInfoDto.getDefaultImageId())) {
            throw new ValidationFailureException(I18nUtil.getMessage("kn.error.default.image"));
        }

        final String chineseName = editInfoDto.getChineseName();
        // 更新别名
        birdInfoManager.editOtherAlias(chineseName, editInfoDto.getOtherAlias());
        // 更新活动月份
        birdInfoManager.editActiveMonths(chineseName, editInfoDto.getActiveMonths());
        // 更新扩展属性表
        final EditBirdInfoExtDto editExtDto = BeanUtil.copyProperties(editInfoDto, EditBirdInfoExtDto.class);
        birdInfoManager.editExt(editExtDto);
        // 修改图片
        knImageManager.editBirdImages(chineseName, editInfoDto.getImageIds(), SecurityContextHolder.getProjectId());
    }

    @Override
    public void setLocal(IsLocalDto isLocalDto) {
        birdInfoManager.setLocal(isLocalDto);
    }

    @Override
    public String uploadImage(UploadImageDto uploadImageDto) {
        final KnBirdInfoImage image = new KnBirdInfoImage();
        image.setChineseName(uploadImageDto.getChineseName());
        image.setImageBase64(uploadImageDto.getImageBase64());
        image.setProjectId(SecurityContextHolder.getProjectId());
        birdInfoImageMapper.insert(image);
        return image.getId();
    }
}
