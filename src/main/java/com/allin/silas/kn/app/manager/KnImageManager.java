package com.allin.silas.kn.app.manager;

import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 知识百科图片通用逻辑层接口
 *
 * <AUTHOR>
 * @since 2025/4/26
 */
public interface KnImageManager {

    /**
     * 批量填充系统默认的图片id
     */
    <T> void batchFillImages(List<T> birdInfos,
                             Function<T, String> nameGetter,
                             BiConsumer<T, List<String>> imageSetter);

    /**
     * 修改鸟种图片
     */
    void editBirdImages(String chineseName, List<String> imageIds, String projectId);

    /**
     * 修改草种图片
     */
    void editGrassImages(String chineseName, List<String> imageIds, String projectId);

    /**
     * 修改虫种图片
     */
    void editInsectImages(String chineseName, List<String> imageIds, String projectId);
}
