package com.allin.silas.kn.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 虫种别名表
 */
@Data
@TableName(value = "kn_insect_info_alias")
public class KnInsectInfoAlias {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 中文名称
     */
    @TableField(value = "chinese_name")
    private String chineseName;

    /**
     * 别名
     */
    @TableField(value = "alias")
    private String alias;

    /**
     * 项目id
     */
    @TableField(value = "project_id")
    private String projectId;
}