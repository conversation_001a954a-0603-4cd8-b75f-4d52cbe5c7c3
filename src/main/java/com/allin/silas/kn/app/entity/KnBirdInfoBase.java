package com.allin.silas.kn.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 鸟种百科基本信息
 */
@Data
@TableName(value = "kn_bird_info_base")
public class KnBirdInfoBase {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 中文名
     */
    @TableField(value = "chinese_name")
    private String chineseName;

    /**
     * 学名
     */
    @TableField(value = "sci_name")
    private String sciName;

    /**
     * 目
     */
    @TableField(value = "order_name")
    private String orderName;

    /**
     * 科
     */
    @TableField(value = "family_name")
    private String familyName;

    /**
     * 属
     */
    @TableField(value = "genus_name")
    private String genusName;

    /**
     * 鸟种简介
     */
    @TableField(value = "summary")
    private String summary;

    /**
     * 鉴别特征
     */
    @TableField(value = "features")
    private String features;

    /**
     * 生活习性
     */
    @TableField(value = "habits")
    private String habits;

    /**
     * 分布范围
     */
    @TableField(value = "range")
    private String range;

    /**
     * 栖息环境
     */
    @TableField(value = "habitat")
    private String habitat;

    /**
     * 繁殖方式
     */
    @TableField(value = "breeding")
    private String breeding;

    /**
     * 活动时间，日行性、晨昏性、夜行性
     */
    @TableField(value = "active_time")
    private String activeTime;

    /**
     * 防治措施
     */
    @TableField(value = "measures")
    private String measures;

    /**
     * 保护等级
     */
    @TableField(value = "iucn_level")
    private Integer iucnLevel;
}