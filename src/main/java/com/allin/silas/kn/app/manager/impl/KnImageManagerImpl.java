package com.allin.silas.kn.app.manager.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.kn.adapter.vo.KnBirdInfoBaseVo;
import com.allin.silas.kn.adapter.vo.KnGrassInfoBaseVo;
import com.allin.silas.kn.adapter.vo.KnImageBaseInfoVo;
import com.allin.silas.kn.adapter.vo.KnInsectInfoBaseVo;
import com.allin.silas.kn.app.entity.KnBirdInfoImage;
import com.allin.silas.kn.app.entity.KnGrassInfoImage;
import com.allin.silas.kn.app.entity.KnInsectInfoImage;
import com.allin.silas.kn.app.manager.KnImageManager;
import com.allin.silas.kn.infra.repository.KnBirdInfoImageMapper;
import com.allin.silas.kn.infra.repository.KnGrassInfoImageMapper;
import com.allin.silas.kn.infra.repository.KnInsectInfoImageMapper;
import com.allin.view.base.exception.service.ProgramException;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 知识百科图片管理接口实现
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@Service
public class KnImageManagerImpl implements KnImageManager {

    private final KnBirdInfoImageMapper knBirdInfoImageMapper;

    private final KnGrassInfoImageMapper knGrassInfoImageMapper;

    private final KnInsectInfoImageMapper knInsectInfoImageMapper;

    public KnImageManagerImpl(KnBirdInfoImageMapper knBirdInfoImageMapper,
                              KnGrassInfoImageMapper knGrassInfoImageMapper,
                              KnInsectInfoImageMapper knInsectInfoImageMapper) {
        this.knBirdInfoImageMapper = knBirdInfoImageMapper;
        this.knGrassInfoImageMapper = knGrassInfoImageMapper;
        this.knInsectInfoImageMapper = knInsectInfoImageMapper;
    }

    @Override
    public <T> void batchFillImages(List<T> data, Function<T, String> nameGetter, BiConsumer<T, List<String>> imageSetter) {
        if (CollUtil.isEmpty(data)) {
            return;
        }

        // 获取第一个元素判断类型
        Object firstElement = data.get(0);
        Map<String, List<String>> aliasMap;

        if (firstElement instanceof KnBirdInfoBaseVo) {
            List<KnImageBaseInfoVo> images = knBirdInfoImageMapper.listBuiltIn();
            aliasMap = images.stream()
                    .collect(Collectors.groupingBy(
                            KnImageBaseInfoVo::getChineseName,
                            Collectors.mapping(KnImageBaseInfoVo::getId, Collectors.toList())
                    ));
        } else if (firstElement instanceof KnGrassInfoBaseVo) {
            List<KnImageBaseInfoVo> images = knGrassInfoImageMapper.listBuiltIn();
            aliasMap = images.stream()
                    .collect(Collectors.groupingBy(
                            KnImageBaseInfoVo::getChineseName,
                            Collectors.mapping(KnImageBaseInfoVo::getId, Collectors.toList())
                    ));
        } else if (firstElement instanceof KnInsectInfoBaseVo) {
            List<KnImageBaseInfoVo> images = knInsectInfoImageMapper.listBuiltIn();
            aliasMap = images.stream()
                    .collect(Collectors.groupingBy(
                            KnImageBaseInfoVo::getChineseName,
                            Collectors.mapping(KnImageBaseInfoVo::getId, Collectors.toList())
                    ));
        } else {
            throw new ProgramException("知识百科填充内置图像时不支持的类型" + firstElement.getClass().getName());
        }

        // 填充别名
        data.forEach(vo -> {
            String name = nameGetter.apply(vo);
            List<String> imageIds = aliasMap.getOrDefault(name, new ArrayList<>());
            imageSetter.accept(vo, imageIds);
        });
    }

    @Override
    public void editBirdImages(String chineseName, List<String> imageIds, String projectId) {
        // 如果图片数组为空，则删除所有图片
        if (CollUtil.isEmpty(imageIds)) {
            knBirdInfoImageMapper.delete(Wrappers.lambdaQuery(KnBirdInfoImage.class)
                    .eq(KnBirdInfoImage::getChineseName, chineseName)
                    .eq(KnBirdInfoImage::getProjectId, projectId));
            return;
        }
        final List<KnImageBaseInfoVo> images = knBirdInfoImageMapper.listBuiltInAndProject(chineseName, projectId);
        final List<String> delIds = new ArrayList<>();
        for (KnImageBaseInfoVo image : images) {
            // 如果项目所属id为空或者当前图片数组包括该id，则跳过该图片
            if (StrUtil.isBlank(image.getProjectId()) || imageIds.contains(image.getId())) {
                continue;
            }
            delIds.add(image.getId());
        }
        if (CollUtil.isNotEmpty(delIds)) {
            knBirdInfoImageMapper.deleteByIds(delIds);
        }
    }

    @Override
    public void editGrassImages(String chineseName, List<String> imageIds, String projectId) {
        // 如果图片数组为空，则删除所有图片
        if (CollUtil.isEmpty(imageIds)) {
            knGrassInfoImageMapper.delete(Wrappers.lambdaQuery(KnGrassInfoImage.class)
                    .eq(KnGrassInfoImage::getChineseName, chineseName)
                    .eq(KnGrassInfoImage::getProjectId, projectId));
            return;
        }
        final List<KnGrassInfoImage> images = knGrassInfoImageMapper.listBuiltInAndProject(chineseName, projectId);
        final List<String> delIds = new ArrayList<>();
        for (KnGrassInfoImage image : images) {
            // 如果项目所属id为空或者当前图片数组包括该id，则跳过该图片
            if (StrUtil.isBlank(image.getProjectId()) || imageIds.contains(image.getId())) {
                continue;
            }
            delIds.add(image.getId());
        }
        if (CollUtil.isNotEmpty(delIds)) {
            knGrassInfoImageMapper.deleteByIds(delIds);
        }
    }

    @Override
    public void editInsectImages(String chineseName, List<String> imageIds, String projectId) {
        // 如果图片数组为空，则删除所有图片
        if (CollUtil.isEmpty(imageIds)) {
            knInsectInfoImageMapper.delete(Wrappers.lambdaQuery(KnInsectInfoImage.class)
                    .eq(KnInsectInfoImage::getChineseName, chineseName)
                    .eq(KnInsectInfoImage::getProjectId, projectId));
            return;
        }
        final List<KnInsectInfoImage> images = knInsectInfoImageMapper.listBuiltInAndProject(chineseName, projectId);
        final List<String> delIds = new ArrayList<>();
        for (KnInsectInfoImage image : images) {
            // 如果项目所属id为空或者当前图片数组包括该id，则跳过该图片
            if (StrUtil.isBlank(image.getProjectId()) || imageIds.contains(image.getId())) {
                continue;
            }
            delIds.add(image.getId());
        }
        if (CollUtil.isNotEmpty(delIds)) {
            knInsectInfoImageMapper.deleteByIds(delIds);
        }
    }
}
