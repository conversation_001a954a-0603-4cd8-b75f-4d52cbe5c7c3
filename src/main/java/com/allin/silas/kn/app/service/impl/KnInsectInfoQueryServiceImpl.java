package com.allin.silas.kn.app.service.impl;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.kn.adapter.vo.KnInsectInfoBaseVo;
import com.allin.silas.kn.adapter.vo.KnInsectInfoVo;
import com.allin.silas.kn.app.entity.KnInsectInfoBase;
import com.allin.silas.kn.app.entity.KnInsectInfoImage;
import com.allin.silas.kn.app.manager.KnImageManager;
import com.allin.silas.kn.app.manager.KnInsectInfoManager;
import com.allin.silas.kn.app.service.KnInsectInfoQueryService;
import com.allin.silas.kn.infra.repository.KnInsectInfoActiveMonthMapper;
import com.allin.silas.kn.infra.repository.KnInsectInfoAliasMapper;
import com.allin.silas.kn.infra.repository.KnInsectInfoBaseMapper;
import com.allin.silas.kn.infra.repository.KnInsectInfoImageMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 虫种百科查询业务接口实现
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
@Service
public class KnInsectInfoQueryServiceImpl implements KnInsectInfoQueryService {

    private final KnInsectInfoBaseMapper insectInfoBaseMapper;

    private final KnInsectInfoAliasMapper insectInfoAliasMapper;

    private final KnInsectInfoManager insectInfoManager;

    private final KnInsectInfoImageMapper insectInfoImageMapper;

    private final KnInsectInfoActiveMonthMapper insectInfoActiveMonthMapper;

    private final KnImageManager imageManager;

    public KnInsectInfoQueryServiceImpl(KnInsectInfoBaseMapper insectInfoBaseMapper,
                                        KnInsectInfoAliasMapper insectInfoAliasMapper,
                                        KnInsectInfoManager insectInfoManager,
                                        KnInsectInfoImageMapper insectInfoImageMapper,
                                        KnInsectInfoActiveMonthMapper insectInfoActiveMonthMapper,
                                        KnImageManager imageManager) {
        this.insectInfoBaseMapper = insectInfoBaseMapper;
        this.insectInfoAliasMapper = insectInfoAliasMapper;
        this.insectInfoManager = insectInfoManager;
        this.insectInfoImageMapper = insectInfoImageMapper;
        this.insectInfoActiveMonthMapper = insectInfoActiveMonthMapper;
        this.imageManager = imageManager;
    }

    @Override
    public List<KnInsectInfoBaseVo> menu() {
        final List<KnInsectInfoBase> insectInfoBases = insectInfoBaseMapper.listBaseInfo();

        // 转换基本信息
        List<KnInsectInfoBaseVo> menu = insectInfoBases.stream().map(birdInfoBase -> {
            final KnInsectInfoBaseVo vo = new KnInsectInfoBaseVo();
            BeanUtils.copyProperties(birdInfoBase, vo);
            return vo;
        }).toList();

        // 批量填充别名
        insectInfoManager.batchFillAlias(menu, KnInsectInfoBaseVo::getChineseName, KnInsectInfoBaseVo::setAlias);

        // 批量填充系统默认封面
        imageManager.batchFillImages(menu, KnInsectInfoBaseVo::getChineseName, KnInsectInfoBaseVo::setImageIds);

        // 批量填充自定义扩展信息
        insectInfoManager.batchFillExt(menu, KnInsectInfoBaseVo::getChineseName,
                (vo, ext) -> {
                    vo.setIsLocal(ext.getIsLocal());
                    if (ext.getDefaultImageId() != null) {
                        vo.setDefaultImageId(ext.getDefaultImageId());
                    }
                });

        return menu;
    }

    @Override
    public String getImageBase64(String id) {
        return insectInfoImageMapper.getById(id);
    }

    @Override
    public KnInsectInfoVo infoByProjectId(String chineseName, String projectId) {
        final KnInsectInfoBase insectInfoBase = insectInfoBaseMapper.selectOne(Wrappers.lambdaQuery(KnInsectInfoBase.class)
                .eq(KnInsectInfoBase::getChineseName, chineseName));

        final KnInsectInfoVo result = new KnInsectInfoVo();
        BeanUtils.copyProperties(insectInfoBase, result);
        // 填充扩展信息
        insectInfoManager.fillExt(result, KnInsectInfoVo::getChineseName, (insectInfoVo, ext) -> {
            insectInfoVo.setIsLocal(ext.getIsLocal());
            if (StrUtil.isNotBlank(ext.getMeasures())) {
                insectInfoVo.setMeasures(ext.getMeasures());
            }
            if (ext.getDefaultImageId() != null) {
                insectInfoVo.setDefaultImageId(ext.getDefaultImageId());
            }
        });
        // 填充公共别名
        result.setAlias(insectInfoAliasMapper.listBuiltInAlias(insectInfoBase.getChineseName()));
        // 填充其他别名
        result.setOtherAlias(insectInfoAliasMapper.listAliasByProject(insectInfoBase.getChineseName(),
                SecurityContextHolder.getProjectId()));
        // 填充吸引鸟种
        result.setAttractBirds(insectInfoManager.listAttractBirds(chineseName));
        // 填充吸引虫种
        result.setAttractInsects(insectInfoManager.listAttractInsects(chineseName));
        // 查找图片列表
        final List<KnInsectInfoImage> images = insectInfoImageMapper.listBuiltInAndProject(insectInfoBase.getChineseName(), projectId);
        // 填充系统自带图片id
        result.setImageIds(images.stream().filter(image -> image.getProjectId() == null).map(KnInsectInfoImage::getId).toList());
        // 填充自定义图片id
        result.setOtherImageIds(images.stream().filter(image -> image.getProjectId() != null).map(KnInsectInfoImage::getId).toList());
        // 填充活动月份
        result.setActiveMonths(insectInfoActiveMonthMapper.listMonthByProjectId(insectInfoBase.getChineseName(), projectId));
        return result;
    }
}
