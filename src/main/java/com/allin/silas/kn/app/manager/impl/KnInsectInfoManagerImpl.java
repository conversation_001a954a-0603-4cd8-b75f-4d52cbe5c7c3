package com.allin.silas.kn.app.manager.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.kn.adapter.dto.EditInsectInfoExtDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.app.entity.*;
import com.allin.silas.kn.app.manager.KnInsectInfoManager;
import com.allin.silas.kn.infra.repository.*;
import com.allin.view.auth.context.SecurityContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 虫种百科通用逻辑层接口实现
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
@Service
public class KnInsectInfoManagerImpl implements KnInsectInfoManager {

    private final KnInsectInfoExtMapper insectInfoExtMapper;

    private final KnInsectInfoAliasMapper insectInfoAliasMapper;

    private final KnInsectInfoAttractSpeciesMapper insectInfoAttractSpeciesMapper;

    private final KnBirdInfoBaseMapper birdInfoBaseMapper;

    private final KnInsectInfoBaseMapper insectInfoBaseMapper;

    private final KnInsectInfoActiveMonthMapper insectInfoActiveMonthMapper;

    public KnInsectInfoManagerImpl(KnInsectInfoExtMapper insectInfoExtMapper,
                                   KnInsectInfoAliasMapper insectInfoAliasMapper,
                                   KnInsectInfoAttractSpeciesMapper insectInfoAttractSpeciesMapper,
                                   KnBirdInfoBaseMapper birdInfoBaseMapper,
                                   KnInsectInfoBaseMapper insectInfoBaseMapper,
                                   KnInsectInfoActiveMonthMapper insectInfoActiveMonthMapper) {
        this.insectInfoExtMapper = insectInfoExtMapper;
        this.insectInfoAliasMapper = insectInfoAliasMapper;
        this.insectInfoAttractSpeciesMapper = insectInfoAttractSpeciesMapper;
        this.birdInfoBaseMapper = birdInfoBaseMapper;
        this.insectInfoBaseMapper = insectInfoBaseMapper;
        this.insectInfoActiveMonthMapper = insectInfoActiveMonthMapper;
    }

    @Override
    public <T> void fillExt(T insectInfo, Function<T, String> nameGetter, BiConsumer<T, KnInsectInfoExt> setter) {
        if (insectInfo == null) {
            return;
        }

        final String name = nameGetter.apply(insectInfo);

        // 批量查询扩展信息
        KnInsectInfoExt infoExt = insectInfoExtMapper.selectOne(
                Wrappers.lambdaQuery(KnInsectInfoExt.class)
                        .eq(KnInsectInfoExt::getProjectId, SecurityContextHolder.getProjectId())
                        .eq(KnInsectInfoExt::getChineseName, name));

        if (infoExt == null) {
            infoExt = new KnInsectInfoExt();
            infoExt.setChineseName(name);
            infoExt.setIsLocal(0);
        }

        // 填充属性
        setter.accept(insectInfo, infoExt);
    }

    @Override
    public <T> void batchFillAlias(List<T> insectInfos, Function<T, String> nameGetter, BiConsumer<T, List<String>> aliasSetter) {
        if (CollUtil.isEmpty(insectInfos)) {
            return;
        }

        // 批量查询别名
        Map<String, List<String>> aliasMap;

        if (SecurityContextHolder.tryGetProjectId().isPresent()) {
            final String projectId = SecurityContextHolder.tryGetProjectId().get();
            aliasMap = insectInfoAliasMapper.listBuiltInAndProject(projectId)
                    .stream()
                    .collect(Collectors.groupingBy(
                            KnInsectInfoAlias::getChineseName,
                            Collectors.mapping(KnInsectInfoAlias::getAlias, Collectors.toList())
                    ));
        } else {
            aliasMap = insectInfoAliasMapper.listBuiltInAliasObj()
                    .stream()
                    .collect(Collectors.groupingBy(
                            KnInsectInfoAlias::getChineseName,
                            Collectors.mapping(KnInsectInfoAlias::getAlias, Collectors.toList())
                    ));
        }

        // 填充别名
        insectInfos.forEach(vo -> {
            String name = nameGetter.apply(vo);
            List<String> aliases = aliasMap.getOrDefault(name, new ArrayList<>());
            aliasSetter.accept(vo, aliases);
        });
    }

    @Override
    public <T> void batchFillExt(List<T> insectInfos, Function<T, String> nameGetter, BiConsumer<T, KnInsectInfoExt> setter) {
        if (CollUtil.isEmpty(insectInfos)) {
            return;
        }

        // 批量查询扩展信息
        List<KnInsectInfoExt> birdInfoExts = insectInfoExtMapper.selectList(
                Wrappers.lambdaQuery(KnInsectInfoExt.class)
                        .eq(KnInsectInfoExt::getProjectId, SecurityContextHolder.getProjectId()));

        if (CollUtil.isEmpty(birdInfoExts)) {
            return;
        }

        // 转换为Map
        Map<String, KnInsectInfoExt> extMap = birdInfoExts.stream()
                .collect(Collectors.toMap(KnInsectInfoExt::getChineseName, Function.identity()));

        // 填充属性
        insectInfos.forEach(vo -> {
            final String name = nameGetter.apply(vo);
            KnInsectInfoExt infoExt = extMap.get(name);
            if (infoExt == null) {
                infoExt = new KnInsectInfoExt();
                infoExt.setChineseName(name);
                infoExt.setIsLocal(0);
            }
            setter.accept(vo, infoExt);
        });
    }

    @Override
    public synchronized void setLocal(IsLocalDto isLocalDto) {
        KnInsectInfoExt infoExt = insectInfoExtMapper.selectOne(Wrappers.lambdaQuery(KnInsectInfoExt.class)
                .eq(KnInsectInfoExt::getChineseName, isLocalDto.getChineseName())
                .eq(KnInsectInfoExt::getProjectId, SecurityContextHolder.getProjectId()));
        if (infoExt != null) {
            infoExt.setIsLocal(isLocalDto.getIsLocal());
            insectInfoExtMapper.updateById(infoExt);
        } else {
            infoExt = new KnInsectInfoExt();
            infoExt.setChineseName(isLocalDto.getChineseName());
            infoExt.setIsLocal(isLocalDto.getIsLocal());
            insectInfoExtMapper.insert(infoExt);
        }
    }

    @Override
    public Map<String, Boolean> listAttractBirds(String chineseName) {
        final List<KnInsectInfoAttractSpecies> knInsectInfoAttractSpecies = insectInfoAttractSpeciesMapper
                .selectList(Wrappers.lambdaQuery(KnInsectInfoAttractSpecies.class)
                        .eq(KnInsectInfoAttractSpecies::getChineseName, chineseName)
                        .eq(KnInsectInfoAttractSpecies::getSpeciesType, 1));
        Map<String, Boolean> result = new HashMap<>();
        for (KnInsectInfoAttractSpecies species : knInsectInfoAttractSpecies) {
            if (birdInfoBaseMapper.exists(Wrappers.lambdaQuery(KnBirdInfoBase.class)
                    .eq(KnBirdInfoBase::getChineseName, species.getSpeciesName()))) {
                result.put(species.getSpeciesName(), true);
            } else {
                result.put(species.getSpeciesName(), false);
            }
        }
        return result;
    }

    @Override
    public Map<String, Boolean> listAttractInsects(String chineseName) {
        final List<KnInsectInfoAttractSpecies> knInsectInfoAttractSpecies = insectInfoAttractSpeciesMapper
                .selectList(Wrappers.lambdaQuery(KnInsectInfoAttractSpecies.class)
                        .eq(KnInsectInfoAttractSpecies::getChineseName, chineseName)
                        .eq(KnInsectInfoAttractSpecies::getSpeciesType, 2));
        Map<String, Boolean> result = new HashMap<>();
        for (KnInsectInfoAttractSpecies species : knInsectInfoAttractSpecies) {
            if (insectInfoBaseMapper.exists(Wrappers.lambdaQuery(KnInsectInfoBase.class)
                    .eq(KnInsectInfoBase::getChineseName, species.getSpeciesName()))) {
                result.put(species.getSpeciesName(), true);
            } else {
                result.put(species.getSpeciesName(), false);
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized void editAlias(String chineseName, Collection<String> newAlias) {
        if (StrUtil.isBlank(chineseName)) {
            return;
        }

        insectInfoAliasMapper.delete(
                Wrappers.lambdaQuery(KnInsectInfoAlias.class)
                        .eq(KnInsectInfoAlias::getChineseName, chineseName)
                        .eq(KnInsectInfoAlias::getProjectId, SecurityContextHolder.getProjectId())
        );

        if (CollUtil.isNotEmpty(newAlias)) {
            // 插入新增的别名
            List<KnInsectInfoAlias> aliasEntities = newAlias.stream().map(aliasName -> {
                KnInsectInfoAlias entity = new KnInsectInfoAlias();
                entity.setChineseName(chineseName);
                entity.setAlias(aliasName);
                entity.setProjectId(SecurityContextHolder.getProjectId());
                return entity;
            }).collect(Collectors.toList());

            // 批量插入
            insectInfoAliasMapper.insert(aliasEntities);
        }
    }

    @Override
    public synchronized void editExt(EditInsectInfoExtDto editExtDto) {
        KnInsectInfoExt infoExt = insectInfoExtMapper.selectOne(Wrappers.lambdaQuery(KnInsectInfoExt.class)
                .eq(KnInsectInfoExt::getChineseName, editExtDto.getChineseName())
                .eq(KnInsectInfoExt::getProjectId, SecurityContextHolder.getProjectId()));
        if (infoExt != null) {
            infoExt.setMeasures(editExtDto.getMeasures());
            infoExt.setDefaultImageId(editExtDto.getDefaultImageId());
            insectInfoExtMapper.updateById(infoExt);
        } else {
            infoExt = new KnInsectInfoExt();
            infoExt.setChineseName(editExtDto.getChineseName());
            infoExt.setMeasures(editExtDto.getMeasures());
            infoExt.setDefaultImageId(editExtDto.getDefaultImageId());
            infoExt.setIsLocal(1);
            insectInfoExtMapper.insert(infoExt);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized void editActiveMonths(String chineseName, Collection<Integer> newActiveMonths) {
        // 删除旧的活动月份
        insectInfoActiveMonthMapper.delete(Wrappers.lambdaQuery(KnInsectInfoActiveMonth.class)
                .eq(KnInsectInfoActiveMonth::getChineseName, chineseName)
                .eq(KnInsectInfoActiveMonth::getProjectId, SecurityContextHolder.getProjectId())
        );

        if (CollUtil.isNotEmpty(newActiveMonths)) {
            // 插入新的活动月份
            List<KnInsectInfoActiveMonth> activeMonths = newActiveMonths.stream().map(month -> {
                KnInsectInfoActiveMonth entity = new KnInsectInfoActiveMonth();
                entity.setChineseName(chineseName);
                entity.setMonth(month);
                entity.setProjectId(SecurityContextHolder.getProjectId());
                return entity;
            }).collect(Collectors.toList());

            // 批量插入
            insectInfoActiveMonthMapper.insert(activeMonths);
        }
    }
}
