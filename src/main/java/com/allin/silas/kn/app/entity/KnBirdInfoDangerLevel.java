package com.allin.silas.kn.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 鸟种危险等级名录
 */
@Data
@TableName(value = "kn_bird_info_danger_level")
public class KnBirdInfoDangerLevel {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 中文名
     */
    @TableField(value = "chinese_name")
    private String chineseName;

    /**
     * 学名
     */
    @TableField(value = "sci_name")
    private String sciName;

    /**
     * 目
     */
    @TableField(value = "order_name")
    private String orderName;

    /**
     * 科
     */
    @TableField(value = "family_name")
    private String familyName;

    /**
     * 危险等级
     */
    @TableField(value = "danger_level")
    private Integer dangerLevel;

    /**
     * 危险鸟群个体数,>=
     */
    @TableField(value = "danger_count")
    private Integer dangerCount;

    /**
     * 体重(克)
     */
    @TableField(value = "weight")
    private Double weight;
}