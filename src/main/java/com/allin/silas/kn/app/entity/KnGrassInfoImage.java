package com.allin.silas.kn.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 草种图片信息表
 */
@Data
@TableName(value = "kn_grass_info_image")
public class KnGrassInfoImage {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 中文名
     */
    @TableField(value = "chinese_name")
    private String chineseName;

    /**
     * 图片Base64
     */
    @TableField(value = "image_base64")
    private String imageBase64;

    /**
     * 项目id
     */
    @TableField(value = "project_id")
    private String projectId;
}