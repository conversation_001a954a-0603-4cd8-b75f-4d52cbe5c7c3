package com.allin.silas.kn.app.service;

import com.allin.silas.kn.adapter.dto.EditGrassInfoDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.adapter.dto.UploadImageDto;

/**
 * 虫种百科命令服务接口
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
public interface KnGrassInfoCommandService {

    /**
     * 编辑虫种详情
     */
    void editInfo(EditGrassInfoDto editInfoDto);

    /**
     * 纳入本场
     */
    void setLocal(IsLocalDto isLocalDto);

    /**
     * 上传图片
     */
    String uploadImage(UploadImageDto uploadImageDto);
}
