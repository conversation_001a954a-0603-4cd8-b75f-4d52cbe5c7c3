package com.allin.silas.kn.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 鸟种活动月份表
 */
@Data
@TableName(value = "kn_bird_info_active_month")
public class KnBirdInfoActiveMonth {

    /**
     * 鸟种id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 鸟种中文名称
     */
    @TableField(value = "chinese_name")
    private String chineseName;

    /**
     * 活动月份, 0表示全部
     */
    @TableField(value = "month")
    private Integer month;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;
}