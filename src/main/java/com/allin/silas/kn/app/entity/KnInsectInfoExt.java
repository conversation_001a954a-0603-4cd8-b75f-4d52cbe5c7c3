package com.allin.silas.kn.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 虫种百科扩展信息
 */
@Data
@TableName(value = "kn_insect_info_ext")
public class KnInsectInfoExt {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 中文名
     */
    @TableField(value = "chinese_name")
    private String chineseName;

    /**
     * 防治措施
     */
    @TableField(value = "measures")
    private String measures;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否为本场虫种, 0 否, 1 是
     */
    @TableField(value = "is_local")
    private Integer isLocal;

    /**
     * 默认图片id
     */
    @TableField(value = "default_image_id")
    private String defaultImageId;
}