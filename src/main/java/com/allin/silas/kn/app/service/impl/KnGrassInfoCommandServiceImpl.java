package com.allin.silas.kn.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.allin.silas.kn.adapter.dto.EditGrassInfoDto;
import com.allin.silas.kn.adapter.dto.EditInsectInfoExtDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.adapter.dto.UploadImageDto;
import com.allin.silas.kn.adapter.vo.KnImageBaseInfoVo;
import com.allin.silas.kn.app.entity.KnGrassInfoImage;
import com.allin.silas.kn.app.manager.KnGrassInfoManager;
import com.allin.silas.kn.app.manager.impl.KnImageManagerImpl;
import com.allin.silas.kn.app.service.KnGrassInfoCommandService;
import com.allin.silas.kn.infra.repository.KnGrassInfoImageMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/10
 */
@Service
public class KnGrassInfoCommandServiceImpl implements KnGrassInfoCommandService {
    private final KnGrassInfoManager grassInfoManager;

    private final KnGrassInfoImageMapper grassInfoImageMapper;

    private final KnImageManagerImpl knImageManager;

    public KnGrassInfoCommandServiceImpl(KnGrassInfoManager birdInfoManager,
                                         KnGrassInfoImageMapper grassInfoImageMapper, KnImageManagerImpl knImageManager) {
        this.grassInfoManager = birdInfoManager;
        this.grassInfoImageMapper = grassInfoImageMapper;
        this.knImageManager = knImageManager;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized void editInfo(EditGrassInfoDto editInfoDto) {
        // 参数校验
        final List<String> imageIds = grassInfoImageMapper.listBuiltIn().stream().map(KnImageBaseInfoVo::getId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(editInfoDto.getImageIds())) {
            imageIds.addAll(editInfoDto.getImageIds());
        }
        if (!imageIds.contains(editInfoDto.getDefaultImageId())) {
            throw new ValidationFailureException(I18nUtil.getMessage("kn.error.default.image"));
        }

        final String chineseName = editInfoDto.getChineseName();
        // 更新别名
        grassInfoManager.editAlias(chineseName, editInfoDto.getOtherAlias());
        // 更新生长月份
        grassInfoManager.editActiveMonths(chineseName, editInfoDto.getActiveMonths());
        // 更新扩展属性表
        final EditInsectInfoExtDto editExtDto = BeanUtil.copyProperties(editInfoDto, EditInsectInfoExtDto.class);
        grassInfoManager.editExt(editExtDto);
        // 修改图片
        knImageManager.editGrassImages(chineseName, editInfoDto.getImageIds(), SecurityContextHolder.getProjectId());
    }

    @Override
    public void setLocal(IsLocalDto isLocalDto) {
        grassInfoManager.setLocal(isLocalDto);
    }

    @Override
    public String uploadImage(UploadImageDto uploadImageDto) {
        final KnGrassInfoImage image = new KnGrassInfoImage();
        image.setChineseName(uploadImageDto.getChineseName());
        image.setImageBase64(uploadImageDto.getImageBase64());
        image.setProjectId(SecurityContextHolder.getProjectId());
        grassInfoImageMapper.insert(image);
        return image.getId();
    }
}
