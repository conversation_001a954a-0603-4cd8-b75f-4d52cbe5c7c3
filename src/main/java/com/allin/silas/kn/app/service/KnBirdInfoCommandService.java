package com.allin.silas.kn.app.service;

import com.allin.silas.kn.adapter.dto.EditBirdInfoDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.adapter.dto.UploadImageDto;

/**
 * 鸟种百科命令服务接口
 *
 * <AUTHOR>
 * @since 2025/4/26
 */
public interface KnBirdInfoCommandService {

    /**
     * 编辑详情
     *
     */
    void editInfo(EditBirdInfoDto editInfoDto);

    /**
     * 设置是否纳入本场
     */
    void setLocal(IsLocalDto isLocalDto);

    /**
     * 上传图片
     */
    String uploadImage(UploadImageDto uploadImageDto);
}
