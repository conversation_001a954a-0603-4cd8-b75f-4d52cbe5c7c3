package com.allin.silas.kn.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 鸟种别名表
 */
@Data
@TableName(value = "kn_bird_info_alias")
public class KnBirdInfoAlias {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 鸟种中文名称
     */
    @TableField(value = "chinese_name")
    private String chineseName;

    /**
     * 别名
     */
    @TableField(value = "alias")
    private String alias;
}