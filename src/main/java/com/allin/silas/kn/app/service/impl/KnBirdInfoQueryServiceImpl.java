package com.allin.silas.kn.app.service.impl;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.kn.adapter.vo.*;
import com.allin.silas.kn.app.entity.KnBirdInfoBase;
import com.allin.silas.kn.app.entity.KnBirdInfoDangerLevel;
import com.allin.silas.kn.app.manager.KnBirdInfoManager;
import com.allin.silas.kn.app.manager.KnImageManager;
import com.allin.silas.kn.app.service.KnBirdInfoQueryService;
import com.allin.silas.kn.infra.repository.*;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 鸟种百科查询服务接口实现
 *
 * <AUTHOR>
 * @since 2025/4/26
 */
@Service
public class KnBirdInfoQueryServiceImpl implements KnBirdInfoQueryService {

    private final KnBirdInfoBaseMapper birdInfoBaseMapper;

    private final KnBirdInfoAliasMapper birdInfoAliasMapper;

    private final KnBirdInfoDangerLevelMapper birdInfoDangerLevelMapper;

    private final KnBirdInfoManager birdInfoManager;

    private final KnImageManager imageManager;

    private final KnBirdInfoImageMapper birdInfoImageMapper;

    private final KnBirdInfoActiveMonthMapper birdInfoActiveMonthMapper;

    public KnBirdInfoQueryServiceImpl(KnBirdInfoBaseMapper birdInfoBaseMapper,
                                      KnBirdInfoAliasMapper birdInfoAliasMapper,
                                      KnBirdInfoDangerLevelMapper birdInfoDangerLevelMapper,
                                      KnBirdInfoManager birdInfoManager,
                                      KnImageManager imageManager,
                                      KnBirdInfoImageMapper birdInfoImageMapper,
                                      KnBirdInfoActiveMonthMapper birdInfoActiveMonthMapper) {
        this.birdInfoBaseMapper = birdInfoBaseMapper;
        this.birdInfoAliasMapper = birdInfoAliasMapper;
        this.birdInfoDangerLevelMapper = birdInfoDangerLevelMapper;
        this.birdInfoManager = birdInfoManager;
        this.imageManager = imageManager;
        this.birdInfoImageMapper = birdInfoImageMapper;
        this.birdInfoActiveMonthMapper = birdInfoActiveMonthMapper;
    }

    @Override
    public List<KnBirdInfoBaseVo> listBase() {
        return birdInfoManager.listBase();
    }

    @Override
    public List<KnBirdInfoMenuVo> menu() {
        final List<KnBirdInfoBaseVo> birdInfoBaseVos = birdInfoManager.listBase();

        // 转换基本信息
        List<KnBirdInfoMenuVo> menu = birdInfoBaseVos.stream().map(birdInfoBase -> {
            final KnBirdInfoMenuVo vo = new KnBirdInfoMenuVo();
            BeanUtils.copyProperties(birdInfoBase, vo);
            return vo;
        }).toList();

        // 批量填充系统默认封面
        imageManager.batchFillImages(menu, KnBirdInfoMenuVo::getChineseName, KnBirdInfoMenuVo::setImageIds);

        // 批量填充自定义扩展信息
        birdInfoManager.batchFillExt(menu, KnBirdInfoMenuVo::getChineseName, (vo, ext) -> {
            vo.setIsLocal(ext.getIsLocal());
            // 如果有自定义图片id就覆盖
            if (ext.getDefaultImageId() != null) {
                vo.setDefaultImageId(ext.getDefaultImageId());
            }
        });
        return menu;
    }

    @Override
    public String getImageBase64(String id) {
        return birdInfoImageMapper.getById(id);
    }

    @Override
    public KnProjectBirdInfoVo infoByProjectId(String chineseName, String projectId) {
        final KnBirdInfoBase birdInfoBase = birdInfoBaseMapper.selectOne(Wrappers.lambdaQuery(KnBirdInfoBase.class)
                .eq(KnBirdInfoBase::getChineseName, chineseName));

        final KnProjectBirdInfoVo result = new KnProjectBirdInfoVo();
        BeanUtils.copyProperties(birdInfoBase, result);
        // 填充扩展信息
        birdInfoManager.fillExt(result, KnProjectBirdInfoVo::getChineseName, (vo, ext) -> {
            vo.setIsLocal(ext.getIsLocal());
            if (StrUtil.isNotBlank(ext.getMeasures())) {
                vo.setMeasures(ext.getMeasures());
            }
            if (ext.getDefaultImageId() != null) {
                vo.setDefaultImageId(ext.getDefaultImageId());
            }
        });
        // 填充公共别名
        result.setAlias(birdInfoAliasMapper.listBuiltInAlias(birdInfoBase.getChineseName()));
        // 填充其他别名
        result.setOtherAlias(birdInfoAliasMapper.listAliasByProject(birdInfoBase.getChineseName(), projectId));
        // 填充活动月份
        result.setActiveMonths(birdInfoActiveMonthMapper.listMonthByProjectId(birdInfoBase.getChineseName(), projectId));

        // 查找图片列表
        final List<KnImageBaseInfoVo> images = birdInfoImageMapper.listBuiltInAndProject(birdInfoBase.getChineseName(), projectId);
        // 填充系统自带图片id
        result.setImageIds(images.stream().filter(image -> image.getProjectId() == null).map(KnImageBaseInfoVo::getId).toList());
        // 填充自定义图片id
        result.setOtherImageIds(images.stream().filter(image -> image.getProjectId() != null).map(KnImageBaseInfoVo::getId).toList());
        // 填充危险等级信息
        final KnBirdInfoDangerLevel birdDangerInfo = birdInfoDangerLevelMapper.getByChineseName(birdInfoBase.getChineseName());
        if (birdDangerInfo != null) {
            result.setDangerLevel(birdDangerInfo.getDangerLevel());
            result.setDangerCount(birdDangerInfo.getDangerCount());
        } else {
            result.setDangerLevel(1);
        }
        return result;
    }

    @Override
    public KnBirdInfoVo info(String chineseName) {
        final KnBirdInfoBase birdInfoBase = birdInfoBaseMapper.selectOne(Wrappers.lambdaQuery(KnBirdInfoBase.class)
                .eq(KnBirdInfoBase::getChineseName, chineseName));

        final KnBirdInfoVo result = new KnBirdInfoVo();
        BeanUtils.copyProperties(birdInfoBase, result);
        // 填充别名
        result.setAlias(birdInfoAliasMapper.listBuiltInAlias(birdInfoBase.getChineseName()));
        return result;
    }
}
