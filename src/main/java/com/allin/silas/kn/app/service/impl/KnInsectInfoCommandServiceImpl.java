package com.allin.silas.kn.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.allin.silas.kn.adapter.dto.EditInsectInfoDto;
import com.allin.silas.kn.adapter.dto.EditInsectInfoExtDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.adapter.dto.UploadImageDto;
import com.allin.silas.kn.adapter.vo.KnImageBaseInfoVo;
import com.allin.silas.kn.app.entity.KnInsectInfoImage;
import com.allin.silas.kn.app.manager.KnImageManager;
import com.allin.silas.kn.app.manager.KnInsectInfoManager;
import com.allin.silas.kn.app.service.KnInsectInfoCommandService;
import com.allin.silas.kn.infra.repository.KnInsectInfoImageMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 虫种百科命令服务接口实现
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
@Service
public class KnInsectInfoCommandServiceImpl implements KnInsectInfoCommandService {

    private final KnInsectInfoManager insectInfoManager;

    private final KnInsectInfoImageMapper insectInfoImageMapper;

    private final KnImageManager knImageManager;

    public KnInsectInfoCommandServiceImpl(KnInsectInfoManager birdInfoManager,
                                          KnInsectInfoImageMapper insectInfoImageMapper, KnImageManager knImageManager) {
        this.insectInfoManager = birdInfoManager;
        this.insectInfoImageMapper = insectInfoImageMapper;
        this.knImageManager = knImageManager;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized void editInfo(EditInsectInfoDto editInfoDto) {
        // 参数校验
        final List<String> imageIds = insectInfoImageMapper.listBuiltIn().stream().map(KnImageBaseInfoVo::getId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(editInfoDto.getImageIds())) {
            imageIds.addAll(editInfoDto.getImageIds());
        }
        if (!imageIds.contains(editInfoDto.getDefaultImageId())) {
            throw new ValidationFailureException(I18nUtil.getMessage("kn.error.default.image"));
        }

        // 更新别名
        final String chineseName = editInfoDto.getChineseName();
        insectInfoManager.editAlias(chineseName, editInfoDto.getOtherAlias());
        // 更新活动月份
        insectInfoManager.editActiveMonths(chineseName, editInfoDto.getActiveMonths());
        // 更新扩展属性表
        final EditInsectInfoExtDto editExtDto = BeanUtil.copyProperties(editInfoDto, EditInsectInfoExtDto.class);
        insectInfoManager.editExt(editExtDto);
        // 删除图片
        knImageManager.editInsectImages(chineseName, editInfoDto.getImageIds(), SecurityContextHolder.getProjectId());
    }

    @Override
    public void setLocal(IsLocalDto isLocalDto) {
        insectInfoManager.setLocal(isLocalDto);
    }

    @Override
    public String uploadImage(UploadImageDto uploadImageDto) {
        final KnInsectInfoImage image = new KnInsectInfoImage();
        image.setChineseName(uploadImageDto.getChineseName());
        image.setImageBase64(uploadImageDto.getImageBase64());
        image.setProjectId(SecurityContextHolder.getProjectId());
        insectInfoImageMapper.insert(image);
        return image.getId();
    }
}
