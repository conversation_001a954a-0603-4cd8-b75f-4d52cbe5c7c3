package com.allin.silas.kn.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 鸟种图片信息表
 */
@Data
@TableName(value = "kn_bird_info_image")
public class KnBirdInfoImage {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 鸟种中文名
     */
    @TableField(value = "chinese_name")
    private String chineseName;

    /**
     * 图片Base64
     */
    @TableField(value = "image_base64")
    private String imageBase64;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;
}