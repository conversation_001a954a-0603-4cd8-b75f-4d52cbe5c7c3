package com.allin.silas.kn.app.service.impl;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.kn.adapter.vo.KnGrassInfoBaseVo;
import com.allin.silas.kn.adapter.vo.KnGrassInfoVo;
import com.allin.silas.kn.app.entity.KnGrassInfoBase;
import com.allin.silas.kn.app.entity.KnGrassInfoImage;
import com.allin.silas.kn.app.manager.KnGrassInfoManager;
import com.allin.silas.kn.app.manager.KnImageManager;
import com.allin.silas.kn.app.service.KnGrassInfoQueryService;
import com.allin.silas.kn.infra.repository.KnGrassInfoActiveMonthMapper;
import com.allin.silas.kn.infra.repository.KnGrassInfoAliasMapper;
import com.allin.silas.kn.infra.repository.KnGrassInfoBaseMapper;
import com.allin.silas.kn.infra.repository.KnGrassInfoImageMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/10
 */
@Service
public class KnGrassInfoQueryServiceImpl implements KnGrassInfoQueryService {

    private final KnGrassInfoImageMapper grassInfoImageMapper;

    private final KnImageManager imageManager;

    private final KnGrassInfoBaseMapper grassInfoBaseMapper;

    private final KnGrassInfoManager grassInfoManager;

    private final KnGrassInfoAliasMapper grassInfoAliasMapper;

    private final KnGrassInfoActiveMonthMapper grassInfoActiveMonthMapper;

    public KnGrassInfoQueryServiceImpl(KnGrassInfoImageMapper grassInfoImageMapper,
                                       KnImageManager imageManager,
                                       KnGrassInfoBaseMapper grassInfoBaseMapper,
                                       KnGrassInfoManager grassInfoManager,
                                       KnGrassInfoAliasMapper grassInfoAliasMapper,
                                       KnGrassInfoActiveMonthMapper grassInfoActiveMonthMapper) {
        this.grassInfoImageMapper = grassInfoImageMapper;
        this.imageManager = imageManager;
        this.grassInfoBaseMapper = grassInfoBaseMapper;
        this.grassInfoManager = grassInfoManager;
        this.grassInfoAliasMapper = grassInfoAliasMapper;
        this.grassInfoActiveMonthMapper = grassInfoActiveMonthMapper;
    }

    @Override
    public List<KnGrassInfoBaseVo> menu() {
        final List<KnGrassInfoBase> grassInfoBases = grassInfoBaseMapper.listBaseInfo();

        // 转换基本信息
        List<KnGrassInfoBaseVo> menu = grassInfoBases.stream().map(birdInfoBase -> {
            final KnGrassInfoBaseVo vo = new KnGrassInfoBaseVo();
            BeanUtils.copyProperties(birdInfoBase, vo);
            return vo;
        }).toList();

        // 批量填充别名
        grassInfoManager.batchFillAlias(menu, KnGrassInfoBaseVo::getChineseName, KnGrassInfoBaseVo::setAlias);

        // 批量填充系统默认图片
        imageManager.batchFillImages(menu, KnGrassInfoBaseVo::getChineseName, KnGrassInfoBaseVo::setImageIds);

        // 批量填充自定义扩展信息
        grassInfoManager.batchFillExt(menu, KnGrassInfoBaseVo::getChineseName, (vo, ext) -> {
            vo.setIsLocal(ext.getIsLocal());
            if (ext.getDefaultImageId() != null) {
                vo.setDefaultImageId(ext.getDefaultImageId());
            }
        });

        return menu;
    }

    @Override
    public String getImageBase64(String id) {
        return grassInfoImageMapper.getById(id);
    }

    @Override
    public KnGrassInfoVo infoByProjectId(String chineseName, String projectId) {
        final KnGrassInfoBase grassInfoBase = grassInfoBaseMapper.selectOne(Wrappers.lambdaQuery(KnGrassInfoBase.class)
                .eq(KnGrassInfoBase::getChineseName, chineseName));

        final KnGrassInfoVo result = new KnGrassInfoVo();
        BeanUtils.copyProperties(grassInfoBase, result);
        // 填充扩展信息
        grassInfoManager.fillExt(result, KnGrassInfoVo::getChineseName, (grassInfoVo, ext) -> {
            grassInfoVo.setIsLocal(ext.getIsLocal());
            if (StrUtil.isNotBlank(ext.getMeasures())) {
                grassInfoVo.setMeasures(ext.getMeasures());
            }
            if (ext.getDefaultImageId() != null) {
                grassInfoVo.setDefaultImageId(ext.getDefaultImageId());
            }
        });
        // 填充公共别名
        result.setAlias(grassInfoAliasMapper.listBuiltInAlias(grassInfoBase.getChineseName()));
        // 填充其他别名
        result.setOtherAlias(grassInfoAliasMapper.listBuiltInAndProjectAlias(grassInfoBase.getChineseName(),
                SecurityContextHolder.getProjectId()));
        // 填充吸引鸟种
        result.setAttractBirds(grassInfoManager.listAttractBirds(chineseName));
        // 填充吸引虫种
        result.setAttractInsects(grassInfoManager.listAttractInsects(chineseName));
        // 查找图片列表
        final List<KnGrassInfoImage> images = grassInfoImageMapper.listBuiltInAndProject(grassInfoBase.getChineseName(), projectId);
        // 填充系统自带图片id
        result.setImageIds(images.stream().filter(image -> image.getProjectId() == null).map(KnGrassInfoImage::getId).toList());
        // 填充自定义图片id
        result.setOtherImageIds(images.stream().filter(image -> image.getProjectId() != null).map(KnGrassInfoImage::getId).toList());
        // 填充活动月份
        result.setActiveMonths(grassInfoActiveMonthMapper.listMonthByProjectId(grassInfoBase.getChineseName(), projectId));
        return result;
    }
}
