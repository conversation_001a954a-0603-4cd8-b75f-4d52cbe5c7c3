package com.allin.silas.kn.app.manager.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.kn.adapter.dto.EditBirdInfoExtDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.adapter.vo.KnBirdInfoBaseVo;
import com.allin.silas.kn.app.entity.KnBirdInfoActiveMonth;
import com.allin.silas.kn.app.entity.KnBirdInfoAlias;
import com.allin.silas.kn.app.entity.KnBirdInfoExt;
import com.allin.silas.kn.app.manager.KnBirdInfoManager;
import com.allin.silas.kn.infra.repository.KnBirdInfoActiveMonthMapper;
import com.allin.silas.kn.infra.repository.KnBirdInfoAliasMapper;
import com.allin.silas.kn.infra.repository.KnBirdInfoBaseMapper;
import com.allin.silas.kn.infra.repository.KnBirdInfoExtMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 鸟种百科通用通用逻辑层实现
 *
 * <AUTHOR>
 * @since 2025/4/26
 */
@Service
public class KnBirdInfoManagerImpl implements KnBirdInfoManager {

    private final KnBirdInfoExtMapper birdInfoExtMapper;

    private final KnBirdInfoAliasMapper birdInfoAliasMapper;

    private final KnBirdInfoActiveMonthMapper birdInfoActiveMonthMapper;

    private final KnBirdInfoBaseMapper birdInfoBaseMapper;

    public KnBirdInfoManagerImpl(KnBirdInfoExtMapper birdInfoExtMapper,
                                 KnBirdInfoAliasMapper birdInfoAliasMapper,
                                 KnBirdInfoActiveMonthMapper birdInfoActiveMonthMapper,
                                 KnBirdInfoBaseMapper birdInfoBaseMapper) {
        this.birdInfoExtMapper = birdInfoExtMapper;
        this.birdInfoAliasMapper = birdInfoAliasMapper;
        this.birdInfoActiveMonthMapper = birdInfoActiveMonthMapper;
        this.birdInfoBaseMapper = birdInfoBaseMapper;
    }

    @Override
    public List<KnBirdInfoBaseVo> listBase() {
        final List<KnBirdInfoBaseVo> birdInfoBases = birdInfoBaseMapper.listBaseInfo();

        // 批量填充别名
        batchFillAlias(birdInfoBases, KnBirdInfoBaseVo::getChineseName, KnBirdInfoBaseVo::setAlias);

        return birdInfoBases;
    }

    @Override
    public <T> void fillExt(T birdInfo, Function<T, String> nameGetter, BiConsumer<T, KnBirdInfoExt> setter) {
        if (birdInfo == null) {
            return;
        }

        final String name = nameGetter.apply(birdInfo);

        // 批量查询扩展信息
        KnBirdInfoExt infoExt = birdInfoExtMapper.selectOne(
                Wrappers.lambdaQuery(KnBirdInfoExt.class)
                        .eq(KnBirdInfoExt::getProjectId, SecurityContextHolder.getProjectId())
                        .eq(KnBirdInfoExt::getChineseName, name));

        if (infoExt == null) {
            infoExt = new KnBirdInfoExt();
            infoExt.setChineseName(name);
            infoExt.setIsLocal(0);
        }

        // 填充属性
        setter.accept(birdInfo, infoExt);
    }

    @Override
    public <T> void batchFillAlias(List<T> birdInfos, Function<T, String> nameGetter, BiConsumer<T, List<String>> aliasSetter) {
        if (CollUtil.isEmpty(birdInfos)) {
            return;
        }

        Map<String, List<String>> aliasMap;

        if (SecurityContextHolder.tryGetProjectId().isPresent()) {
            final String projectId = SecurityContextHolder.tryGetProjectId().get();
            aliasMap = birdInfoAliasMapper.listBuiltInAndProject(projectId)
                    .stream()
                    .collect(Collectors.groupingBy(
                            KnBirdInfoAlias::getChineseName,
                            Collectors.mapping(KnBirdInfoAlias::getAlias, Collectors.toList())
                    ));
        } else {
            aliasMap = birdInfoAliasMapper.listBuiltIn()
                    .stream()
                    .collect(Collectors.groupingBy(
                            KnBirdInfoAlias::getChineseName,
                            Collectors.mapping(KnBirdInfoAlias::getAlias, Collectors.toList())
                    ));
        }

        // 填充别名
        birdInfos.forEach(vo -> {
            String name = nameGetter.apply(vo);
            List<String> aliases = aliasMap.getOrDefault(name, new ArrayList<>());
            aliasSetter.accept(vo, aliases);
        });
    }

    @Override
    public <T> void batchFillExt(List<T> birdInfos, Function<T, String> nameGetter, BiConsumer<T, KnBirdInfoExt> setter) {
        if (CollUtil.isEmpty(birdInfos)) {
            return;
        }

        // 批量查询扩展信息
        List<KnBirdInfoExt> birdInfoExts = birdInfoExtMapper.selectList(
                Wrappers.lambdaQuery(KnBirdInfoExt.class)
                        .eq(KnBirdInfoExt::getProjectId, SecurityContextHolder.getProjectId()));

        if (CollUtil.isEmpty(birdInfoExts)) {
            return;
        }

        // 转换为Map
        Map<String, KnBirdInfoExt> extMap = birdInfoExts.stream()
                .collect(Collectors.toMap(KnBirdInfoExt::getChineseName, Function.identity()));

        // 填充属性
        birdInfos.forEach(vo -> {
            final String name = nameGetter.apply(vo);
            KnBirdInfoExt birdInfoExt = extMap.get(name);
            if (birdInfoExt == null) {
                birdInfoExt = new KnBirdInfoExt();
                birdInfoExt.setChineseName(name);
                birdInfoExt.setIsLocal(0);
            }
            setter.accept(vo, birdInfoExt);
        });
    }

    @Override
    public synchronized void setLocal(IsLocalDto isLocalDto) {
        KnBirdInfoExt infoExt = birdInfoExtMapper.selectOne(Wrappers.lambdaQuery(KnBirdInfoExt.class)
                .eq(KnBirdInfoExt::getChineseName, isLocalDto.getChineseName())
                .eq(KnBirdInfoExt::getProjectId, SecurityContextHolder.getProjectId()));
        if (infoExt != null) {
            infoExt.setIsLocal(isLocalDto.getIsLocal());
            birdInfoExtMapper.updateById(infoExt);
        } else {
            infoExt = new KnBirdInfoExt();
            infoExt.setChineseName(isLocalDto.getChineseName());
            infoExt.setIsLocal(isLocalDto.getIsLocal());
            birdInfoExtMapper.insert(infoExt);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized void editOtherAlias(String chineseName, Collection<String> newAlias) {
        if (StrUtil.isBlank(chineseName)) {
            return;
        }

        birdInfoAliasMapper.delete(
                Wrappers.lambdaQuery(KnBirdInfoAlias.class)
                        .eq(KnBirdInfoAlias::getChineseName, chineseName)
                        .eq(KnBirdInfoAlias::getProjectId, SecurityContextHolder.getProjectId())
        );

        if (CollUtil.isNotEmpty(newAlias)) {
            // 插入新增的别名
            List<KnBirdInfoAlias> aliasEntities = newAlias.stream().map(aliasName -> {
                KnBirdInfoAlias entity = new KnBirdInfoAlias();
                entity.setChineseName(chineseName);
                entity.setAlias(aliasName);
                entity.setProjectId(SecurityContextHolder.getProjectId());
                return entity;
            }).collect(Collectors.toList());

            // 批量插入
            birdInfoAliasMapper.insert(aliasEntities);
        }
    }

    @Override
    public synchronized void editExt(EditBirdInfoExtDto editExtDto) {
        KnBirdInfoExt birdInfoExt = birdInfoExtMapper.selectOne(Wrappers.lambdaQuery(KnBirdInfoExt.class)
                .eq(KnBirdInfoExt::getChineseName, editExtDto.getChineseName())
                .eq(KnBirdInfoExt::getProjectId, SecurityContextHolder.getProjectId()));
        if (birdInfoExt != null) {
            birdInfoExt.setMeasures(editExtDto.getMeasures());
            birdInfoExt.setDefaultImageId(editExtDto.getDefaultImageId());
            birdInfoExtMapper.updateById(birdInfoExt);
        } else {
            birdInfoExt = new KnBirdInfoExt();
            birdInfoExt.setChineseName(editExtDto.getChineseName());
            birdInfoExt.setMeasures(editExtDto.getMeasures());
            birdInfoExt.setDefaultImageId(editExtDto.getDefaultImageId());
            birdInfoExt.setIsLocal(1);
            birdInfoExtMapper.insert(birdInfoExt);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized void editActiveMonths(String chineseName, Collection<Integer> newActiveMonths) {
        // 删除旧的活动月份
        birdInfoActiveMonthMapper.delete(Wrappers.lambdaQuery(KnBirdInfoActiveMonth.class)
                .eq(KnBirdInfoActiveMonth::getChineseName, chineseName)
                .eq(KnBirdInfoActiveMonth::getProjectId, SecurityContextHolder.getProjectId())
        );

        if (CollUtil.isNotEmpty(newActiveMonths)) {
            // 插入新的活动月份
            List<KnBirdInfoActiveMonth> activeMonths = newActiveMonths.stream().map(month -> {
                KnBirdInfoActiveMonth entity = new KnBirdInfoActiveMonth();
                entity.setChineseName(chineseName);
                entity.setMonth(month);
                entity.setProjectId(SecurityContextHolder.getProjectId());
                return entity;
            }).collect(Collectors.toList());

            // 批量插入
            birdInfoActiveMonthMapper.insert(activeMonths);
        }
    }
}
