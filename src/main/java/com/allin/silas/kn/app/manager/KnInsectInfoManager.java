package com.allin.silas.kn.app.manager;

import com.allin.silas.kn.adapter.dto.EditInsectInfoExtDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.app.entity.KnInsectInfoExt;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 虫种百科通用逻辑层接口
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
public interface KnInsectInfoManager {

    /**
     * 填充扩展信息
     *
     * @param insectInfo 需要填充的对象
     * @param nameGetter 从对象中提取中文名的函数
     * @param setter     设置参数的函数
     * @param <T>        对象类型
     */
    <T> void fillExt(T insectInfo,
                     Function<T, String> nameGetter,
                     BiConsumer<T, KnInsectInfoExt> setter);

    /**
     * 批量填充别名信息
     *
     * @param insectInfos 需要填充的对象列表
     * @param nameGetter  从对象中提取中文名的函数
     * @param aliasSetter 设置别名的函数
     * @param <T>         对象类型
     */
    <T> void batchFillAlias(List<T> insectInfos,
                            Function<T, String> nameGetter,
                            BiConsumer<T, List<String>> aliasSetter);

    /**
     * 批量填充扩展信息
     *
     * @param insectInfos 需要填充的对象列表
     * @param nameGetter  从对象中提取中文名的函数
     * @param setter      设置参数的函数
     * @param <T>         对象类型
     */
    <T> void batchFillExt(List<T> insectInfos,
                          Function<T, String> nameGetter,
                          BiConsumer<T, KnInsectInfoExt> setter);

    /**
     * 查询吸引虫种
     */
    Map<String, Boolean> listAttractBirds(String chineseName);

    /**
     * 查询吸引虫种
     */
    Map<String, Boolean> listAttractInsects(String chineseName);

    /**
     * 纳入本场
     */
    void setLocal(IsLocalDto isLocalDto);

    /**
     * 编辑别名
     */
    void editAlias(String chineseName, Collection<String> newAliases);

    /**
     * 更新扩展属性
     */
    void editExt(EditInsectInfoExtDto editExtDto);

    /**
     * 更新活动月份
     */
    void editActiveMonths(String chineseName, Collection<Integer> newActiveMonths);
}
