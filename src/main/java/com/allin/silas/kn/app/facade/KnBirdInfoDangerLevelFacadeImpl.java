package com.allin.silas.kn.app.facade;

import cn.hutool.core.collection.CollUtil;
import com.allin.silas.kn.app.entity.KnBirdInfoBase;
import com.allin.silas.kn.app.entity.KnBirdInfoDangerLevel;
import com.allin.silas.kn.client.KnBirdInfoDangerLevelFacade;
import com.allin.silas.kn.infra.repository.KnBirdInfoBaseMapper;
import com.allin.silas.kn.infra.repository.KnBirdInfoDangerLevelMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 知识百科鸟种危险等级门面实现
 *
 * <AUTHOR>
 * @since 2025/6/28
 */
@Service
public class KnBirdInfoDangerLevelFacadeImpl implements KnBirdInfoDangerLevelFacade {

    private final KnBirdInfoDangerLevelMapper knBirdInfoDangerLevelMapper;

    private final KnBirdInfoBaseMapper knBirdInfoBaseMapper;

    public KnBirdInfoDangerLevelFacadeImpl(KnBirdInfoDangerLevelMapper knBirdInfoDangerLevelMapper,
                                           KnBirdInfoBaseMapper knBirdInfoBaseMapper) {
        this.knBirdInfoDangerLevelMapper = knBirdInfoDangerLevelMapper;
        this.knBirdInfoBaseMapper = knBirdInfoBaseMapper;
    }

    @Cacheable(cacheNames = "knBirdInfoDangerLevel#60", key = "#detectSubType")
    @Override
    public int getDangerLevelCache(String detectSubType) {
        // 默认为1
        if (detectSubType == null) {
            return 1;
        }

        // 先匹配种
        KnBirdInfoDangerLevel dangerLevelInfo = knBirdInfoDangerLevelMapper.getByChineseName(detectSubType);
        if (dangerLevelInfo != null) {
            return dangerLevelInfo.getDangerLevel();
        }

        // 获取鸟类基础信息用于匹配科和目
        KnBirdInfoBase birdInfoBase = knBirdInfoBaseMapper.selectOne(
                Wrappers.lambdaQuery(KnBirdInfoBase.class)
                        .eq(KnBirdInfoBase::getChineseName, detectSubType)
        );

        // 匹配科
        List<KnBirdInfoDangerLevel> birdInfoDangerLevels = knBirdInfoDangerLevelMapper.selectList(Wrappers
                .lambdaQuery(KnBirdInfoDangerLevel.class)
                .eq(KnBirdInfoDangerLevel::getFamilyName, birdInfoBase != null ? birdInfoBase.getFamilyName() : detectSubType));

        if (CollUtil.isNotEmpty(birdInfoDangerLevels)) {
            return birdInfoDangerLevels.stream().mapToInt(KnBirdInfoDangerLevel::getDangerLevel)
                    .max()
                    .orElse(1);
        }

        // 匹配目
        birdInfoDangerLevels = knBirdInfoDangerLevelMapper.selectList(Wrappers.lambdaQuery(KnBirdInfoDangerLevel.class)
                .eq(KnBirdInfoDangerLevel::getOrderName, birdInfoBase != null ? birdInfoBase.getOrderName() : detectSubType));
        if (CollUtil.isNotEmpty(birdInfoDangerLevels)) {
            return birdInfoDangerLevels.stream().mapToInt(KnBirdInfoDangerLevel::getDangerLevel)
                    .max()
                    .orElse(1);
        }

        // 匹配不到的默认返回1
        return 1;
    }
}
