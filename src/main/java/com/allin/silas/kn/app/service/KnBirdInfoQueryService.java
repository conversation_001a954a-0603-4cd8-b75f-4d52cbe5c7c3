package com.allin.silas.kn.app.service;

import com.allin.silas.kn.adapter.vo.KnBirdInfoBaseVo;
import com.allin.silas.kn.adapter.vo.KnBirdInfoMenuVo;
import com.allin.silas.kn.adapter.vo.KnBirdInfoVo;
import com.allin.silas.kn.adapter.vo.KnProjectBirdInfoVo;

import java.util.List;

/**
 * 鸟种百科查询业务接口
 *
 * <AUTHOR>
 * @since 2025/4/26
 */
public interface KnBirdInfoQueryService {

    /**
     * 查询鸟种列表
     */
    List<KnBirdInfoBaseVo> listBase();
    
    /**
     * 查询百科目录
     */
    List<KnBirdInfoMenuVo> menu();

    /**
     * 查询图片
     *
     * @param id 图片id
     * @return 图片base64
     */
    String getImageBase64(String id);

    /**
     * 详情
     *
     * @param projectId 项目id
     */
    KnProjectBirdInfoVo infoByProjectId(String chineseName, String projectId);

    /**
     * 详情
     */
    KnBirdInfoVo info(String chineseName);
}
