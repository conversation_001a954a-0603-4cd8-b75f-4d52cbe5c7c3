package com.allin.silas.kn.app.manager.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.kn.adapter.dto.EditInsectInfoExtDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.app.entity.*;
import com.allin.silas.kn.app.manager.KnGrassInfoManager;
import com.allin.silas.kn.infra.repository.*;
import com.allin.view.auth.context.SecurityContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 虫种百科通用逻辑层接口实现
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
@Service
public class KnGrassInfoManagerImpl implements KnGrassInfoManager {

    private final KnGrassInfoExtMapper grassInfoExtMapper;

    private final KnGrassInfoAliasMapper grassInfoAliasMapper;

    private final KnGrassInfoAttractSpeciesMapper grassInfoAttractSpeciesMapper;

    private final KnBirdInfoBaseMapper birdInfoBaseMapper;

    private final KnGrassInfoBaseMapper grassInfoBaseMapper;

    private final KnGrassInfoActiveMonthMapper grassInfoActiveMonthMapper;

    public KnGrassInfoManagerImpl(KnGrassInfoExtMapper grassInfoExtMapper,
                                  KnGrassInfoAliasMapper insectInfoAliasMapper,
                                  KnGrassInfoAttractSpeciesMapper grassInfoAttractSpeciesMapper,
                                  KnBirdInfoBaseMapper birdInfoBaseMapper,
                                  KnGrassInfoBaseMapper grassInfoBaseMapper,
                                  KnGrassInfoActiveMonthMapper grassInfoActiveMonthMapper) {
        this.grassInfoExtMapper = grassInfoExtMapper;
        this.grassInfoAliasMapper = insectInfoAliasMapper;
        this.grassInfoAttractSpeciesMapper = grassInfoAttractSpeciesMapper;
        this.birdInfoBaseMapper = birdInfoBaseMapper;
        this.grassInfoBaseMapper = grassInfoBaseMapper;
        this.grassInfoActiveMonthMapper = grassInfoActiveMonthMapper;
    }

    @Override
    public <T> void fillExt(T info, Function<T, String> nameGetter, BiConsumer<T, KnGrassInfoExt> setter) {
        if (info == null) {
            return;
        }

        final String name = nameGetter.apply(info);

        // 批量查询扩展信息
        KnGrassInfoExt infoExt = grassInfoExtMapper.selectOne(
                Wrappers.lambdaQuery(KnGrassInfoExt.class)
                        .eq(KnGrassInfoExt::getProjectId, SecurityContextHolder.getProjectId())
                        .eq(KnGrassInfoExt::getChineseName, name));

        if (infoExt == null) {
            infoExt = new KnGrassInfoExt();
            infoExt.setChineseName(name);
            infoExt.setIsLocal(0);
        }

        // 填充属性
        setter.accept(info, infoExt);
    }

    @Override
    public <T> void batchFillAlias(List<T> infos, Function<T, String> nameGetter, BiConsumer<T, List<String>> aliasSetter) {
        if (CollUtil.isEmpty(infos)) {
            return;
        }

        Map<String, List<String>> aliasMap;

        if (SecurityContextHolder.tryGetProjectId().isPresent()) {
            final String projectId = SecurityContextHolder.tryGetProjectId().get();
            aliasMap = grassInfoAliasMapper.listBuiltInAndProject(projectId)
                    .stream()
                    .collect(Collectors.groupingBy(
                            KnGrassInfoAlias::getChineseName,
                            Collectors.mapping(KnGrassInfoAlias::getAlias, Collectors.toList())
                    ));
        } else {
            aliasMap = grassInfoAliasMapper.listBuiltIn()
                    .stream()
                    .collect(Collectors.groupingBy(
                            KnGrassInfoAlias::getChineseName,
                            Collectors.mapping(KnGrassInfoAlias::getAlias, Collectors.toList())
                    ));
        }

        // 填充别名
        infos.forEach(vo -> {
            String name = nameGetter.apply(vo);
            List<String> aliases = aliasMap.getOrDefault(name, new ArrayList<>());
            aliasSetter.accept(vo, aliases);
        });
    }

    @Override
    public <T> void batchFillExt(List<T> infos, Function<T, String> nameGetter, BiConsumer<T, KnGrassInfoExt> setter) {
        if (CollUtil.isEmpty(infos)) {
            return;
        }

        // 批量查询扩展信息
        List<KnGrassInfoExt> birdInfoExts = grassInfoExtMapper.selectList(
                Wrappers.lambdaQuery(KnGrassInfoExt.class)
                        .eq(KnGrassInfoExt::getProjectId, SecurityContextHolder.getProjectId()));

        if (CollUtil.isEmpty(birdInfoExts)) {
            return;
        }

        // 转换为Map
        Map<String, KnGrassInfoExt> extMap = birdInfoExts.stream()
                .collect(Collectors.toMap(KnGrassInfoExt::getChineseName, Function.identity()));

        // 填充属性
        infos.forEach(vo -> {
            final String name = nameGetter.apply(vo);
            KnGrassInfoExt infoExt = extMap.get(name);
            if (infoExt == null) {
                infoExt = new KnGrassInfoExt();
                infoExt.setChineseName(name);
                infoExt.setIsLocal(0);
            }
            setter.accept(vo, infoExt);
        });
    }

    @Override
    public synchronized void setLocal(IsLocalDto isLocalDto) {
        KnGrassInfoExt infoExt = grassInfoExtMapper.selectOne(Wrappers.lambdaQuery(KnGrassInfoExt.class)
                .eq(KnGrassInfoExt::getChineseName, isLocalDto.getChineseName())
                .eq(KnGrassInfoExt::getProjectId, SecurityContextHolder.getProjectId()));
        if (infoExt != null) {
            infoExt.setIsLocal(isLocalDto.getIsLocal());
            grassInfoExtMapper.updateById(infoExt);
        } else {
            infoExt = new KnGrassInfoExt();
            infoExt.setChineseName(isLocalDto.getChineseName());
            infoExt.setIsLocal(isLocalDto.getIsLocal());
            grassInfoExtMapper.insert(infoExt);
        }
    }

    @Override
    public Map<String, Boolean> listAttractBirds(String chineseName) {
        final List<KnGrassInfoAttractSpecies> knGrassInfoAttractSpecies = grassInfoAttractSpeciesMapper
                .selectList(Wrappers.lambdaQuery(KnGrassInfoAttractSpecies.class)
                        .eq(KnGrassInfoAttractSpecies::getChineseName, chineseName)
                        .eq(KnGrassInfoAttractSpecies::getSpeciesType, 1));
        Map<String, Boolean> result = new HashMap<>();
        for (KnGrassInfoAttractSpecies species : knGrassInfoAttractSpecies) {
            if (birdInfoBaseMapper.exists(Wrappers.lambdaQuery(KnBirdInfoBase.class)
                    .eq(KnBirdInfoBase::getChineseName, species.getSpeciesName()))) {
                result.put(species.getSpeciesName(), true);
            } else {
                result.put(species.getSpeciesName(), false);
            }
        }
        return result;
    }

    @Override
    public Map<String, Boolean> listAttractInsects(String chineseName) {
        final List<KnGrassInfoAttractSpecies> knGrassInfoAttractSpecies = grassInfoAttractSpeciesMapper
                .selectList(Wrappers.lambdaQuery(KnGrassInfoAttractSpecies.class)
                        .eq(KnGrassInfoAttractSpecies::getChineseName, chineseName)
                        .eq(KnGrassInfoAttractSpecies::getSpeciesType, 2));
        Map<String, Boolean> result = new HashMap<>();
        for (KnGrassInfoAttractSpecies species : knGrassInfoAttractSpecies) {
            if (grassInfoBaseMapper.exists(Wrappers.lambdaQuery(KnGrassInfoBase.class)
                    .eq(KnGrassInfoBase::getChineseName, species.getSpeciesName()))) {
                result.put(species.getSpeciesName(), true);
            } else {
                result.put(species.getSpeciesName(), false);
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized void editAlias(String chineseName, Collection<String> newAlias) {
        if (StrUtil.isBlank(chineseName)) {
            return;
        }

        grassInfoAliasMapper.delete(
                Wrappers.lambdaQuery(KnGrassInfoAlias.class)
                        .eq(KnGrassInfoAlias::getChineseName, chineseName)
                        .eq(KnGrassInfoAlias::getProjectId, SecurityContextHolder.getProjectId())
        );

        if (CollUtil.isEmpty(newAlias)) {
            // 插入新增的别名
            List<KnGrassInfoAlias> aliasEntities = newAlias.stream().map(aliasName -> {
                KnGrassInfoAlias entity = new KnGrassInfoAlias();
                entity.setChineseName(chineseName);
                entity.setAlias(aliasName);
                entity.setProjectId(SecurityContextHolder.getProjectId());
                return entity;
            }).collect(Collectors.toList());

            // 批量插入
            grassInfoAliasMapper.insert(aliasEntities);
        }
    }

    @Override
    public synchronized void editExt(EditInsectInfoExtDto editExtDto) {
        KnGrassInfoExt infoExt = grassInfoExtMapper.selectOne(Wrappers.lambdaQuery(KnGrassInfoExt.class)
                .eq(KnGrassInfoExt::getChineseName, editExtDto.getChineseName())
                .eq(KnGrassInfoExt::getProjectId, SecurityContextHolder.getProjectId()));
        if (infoExt != null) {
            infoExt.setMeasures(editExtDto.getMeasures());
            infoExt.setDefaultImageId(editExtDto.getDefaultImageId());
            grassInfoExtMapper.updateById(infoExt);
        } else {
            infoExt = new KnGrassInfoExt();
            infoExt.setChineseName(editExtDto.getChineseName());
            infoExt.setMeasures(editExtDto.getMeasures());
            infoExt.setDefaultImageId(editExtDto.getDefaultImageId());
            infoExt.setIsLocal(1);
            grassInfoExtMapper.insert(infoExt);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized void editActiveMonths(String chineseName, Collection<Integer> newActiveMonths) {
        // 删除旧的生长月份
        grassInfoActiveMonthMapper.delete(Wrappers.lambdaQuery(KnGrassInfoActiveMonth.class)
                .eq(KnGrassInfoActiveMonth::getChineseName, chineseName)
                .eq(KnGrassInfoActiveMonth::getProjectId, SecurityContextHolder.getProjectId())
        );

        if (CollUtil.isNotEmpty(newActiveMonths)) {
            // 插入新的生长月份
            List<KnGrassInfoActiveMonth> activeMonths = newActiveMonths.stream().map(month -> {
                KnGrassInfoActiveMonth entity = new KnGrassInfoActiveMonth();
                entity.setChineseName(chineseName);
                entity.setMonth(month);
                entity.setProjectId(SecurityContextHolder.getProjectId());
                return entity;
            }).collect(Collectors.toList());

            // 批量插入
            grassInfoActiveMonthMapper.insert(activeMonths);
        }
    }
}
