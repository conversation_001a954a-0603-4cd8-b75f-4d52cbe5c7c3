package com.allin.silas.kn.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 草种吸引物种表
 */
@Data
@TableName(value = "kn_grass_info_attract_species")
public class KnGrassInfoAttractSpecies {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 中文名称
     */
    @TableField(value = "chinese_name")
    private String chineseName;

    /**
     * 吸引物种
     */
    @TableField(value = "species_name")
    private String speciesName;

    /**
     * 物种类型, 1鸟类, 2虫类
     */
    @TableField(value = "species_type")
    private Integer speciesType;
}