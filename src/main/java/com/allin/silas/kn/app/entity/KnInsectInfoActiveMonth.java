package com.allin.silas.kn.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 虫种活动月份表
 */
@Data
@TableName(value = "kn_insect_info_active_month")
public class KnInsectInfoActiveMonth {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 中文名称
     */
    @TableField(value = "chinese_name")
    private String chineseName;

    /**
     * 活动月份, 0表示全部
     */
    @TableField(value = "month")
    private Integer month;

    /**
     * 项目id
     */
    @TableField(value = "project_id")
    private String projectId;
}