package com.allin.silas.kn.app.manager;

import com.allin.silas.kn.adapter.dto.EditBirdInfoExtDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.adapter.vo.KnBirdInfoBaseVo;
import com.allin.silas.kn.app.entity.KnBirdInfoExt;

import java.util.Collection;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 鸟种百科通用逻辑层接口
 *
 * <AUTHOR>
 * @since 2025/4/26
 */
public interface KnBirdInfoManager {

    /**
     * 查询鸟种百科基本信息
     */
    List<KnBirdInfoBaseVo> listBase();

    /**
     * 填充扩展信息
     *
     * @param birdInfo   需要填充的对象
     * @param nameGetter 从对象中提取中文名的函数
     * @param setter     设置参数的函数
     * @param <T>        对象类型
     */
    <T> void fillExt(T birdInfo,
                     Function<T, String> nameGetter,
                     BiConsumer<T, KnBirdInfoExt> setter);

    /**
     * 批量填充别名信息
     *
     * @param birdInfos   需要填充别名的对象列表
     * @param nameGetter  从对象中提取中文名的函数
     * @param aliasSetter 设置别名的函数
     * @param <T>         对象类型
     */
    <T> void batchFillAlias(List<T> birdInfos,
                            Function<T, String> nameGetter,
                            BiConsumer<T, List<String>> aliasSetter);

    /**
     * 批量填充扩展信息
     *
     * @param birdInfos  需要填充的对象列表
     * @param nameGetter 从对象中提取中文名的函数
     * @param setter     设置参数的函数
     * @param <T>        对象类型
     */
    <T> void batchFillExt(List<T> birdInfos,
                          Function<T, String> nameGetter,
                          BiConsumer<T, KnBirdInfoExt> setter);

    /**
     * 设置本场鸟种
     */
    void setLocal(IsLocalDto isLocalDto);

    /**
     * 编辑鸟种别名
     */
    void editOtherAlias(String chineseName, Collection<String> newAlias);

    /**
     * 更新扩展属性
     */
    void editExt(EditBirdInfoExtDto editExtDto);

    /**
     * 更新活动月份
     */
    void editActiveMonths(String chineseName, Collection<Integer> activeMonths);
}
