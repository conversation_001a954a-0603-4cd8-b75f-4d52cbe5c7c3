package com.allin.silas.kn.app.manager;

import com.allin.silas.kn.adapter.dto.EditInsectInfoExtDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.app.entity.KnGrassInfoExt;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 虫种百科通用逻辑层接口
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
public interface KnGrassInfoManager {

    /**
     * 填充扩展信息
     */
    <T> void fillExt(T info,
                     Function<T, String> nameGetter,
                     BiConsumer<T, KnGrassInfoExt> setter);

    /**
     * 批量填充别名信息
     */
    <T> void batchFillAlias(List<T> infos,
                            Function<T, String> nameGetter,
                            BiConsumer<T, List<String>> aliasSetter);

    /**
     * 批量填充扩展信息
     */
    <T> void batchFillExt(List<T> infos,
                          Function<T, String> nameGetter,
                          BiConsumer<T, KnGrassInfoExt> setter);

    /**
     * 查询吸引虫种
     */
    Map<String, Boolean> listAttractBirds(String chineseName);

    /**
     * 查询吸引虫种
     */
    Map<String, Boolean> listAttractInsects(String chineseName);

    /**
     * 纳入本场
     */
    void setLocal(IsLocalDto isLocalDto);

    /**
     * 编辑别名
     */
    void editAlias(String chineseName, Collection<String> newAliases);

    /**
     * 更新扩展属性
     */
    void editExt(EditInsectInfoExtDto editExtDto);

    /**
     * 更新生长月份
     */
    void editActiveMonths(String chineseName, Collection<Integer> newActiveMonths);
}
