package com.allin.silas.kn.app.service;

import com.allin.silas.kn.adapter.vo.KnGrassInfoBaseVo;
import com.allin.silas.kn.adapter.vo.KnGrassInfoVo;

import java.util.List;

/**
 * 草种百科查询业务接口
 *
 * <AUTHOR>
 * @since 2025/5/10
 */
public interface KnGrassInfoQueryService {

    /**
     * 查询百科目录
     */
    List<KnGrassInfoBaseVo> menu();

    /**
     * 查询图片
     */
    String getImageBase64(String id);

    /**
     * 详情
     */
    KnGrassInfoVo infoByProjectId(String chineseName, String projectId);
}
