package com.allin.silas.kn.app.service;

import com.allin.silas.kn.adapter.vo.KnInsectInfoBaseVo;
import com.allin.silas.kn.adapter.vo.KnInsectInfoVo;

import java.util.List;

/**
 * 虫种百科查询业务接口
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
public interface KnInsectInfoQueryService {

    /**
     * 查询百科目录
     */
    List<KnInsectInfoBaseVo> menu();

    /**
     * 查询图片
     */
    String getImageBase64(String id);

    /**
     * 详情
     */
    KnInsectInfoVo infoByProjectId(String chineseName, String projectId);
}
