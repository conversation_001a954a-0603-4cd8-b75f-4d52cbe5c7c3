package com.allin.silas.kn.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 鸟种声音信息表
 */
@Data
@TableName(value = "kn_bird_info_sound")
public class KnBirdInfoSound {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 鸟种中文名
     */
    @TableField(value = "chinese_name")
    private String chineseName;

    /**
     * 鸟种声音地址
     */
    @TableField(value = "sound_base64")
    private String soundBase64;
}