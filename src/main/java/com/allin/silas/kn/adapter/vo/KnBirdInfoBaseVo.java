package com.allin.silas.kn.adapter.vo;

import lombok.Data;

import java.util.List;

/**
 * 鸟种百科基本信息
 */
@Data
public class KnBirdInfoBaseVo {

    /**
     * 中文名
     */
    private String chineseName;

    /**
     * 学名
     */
    private String sciName;

    /**
     * 目
     */
    private String orderName;

    /**
     * 科
     */
    private String familyName;

    /**
     * 属
     */
    private String genusName;

    /**
     * 别名列表
     */
    private List<String> alias;

    /**
     * 系统内置图片列表
     */
    private List<String> imageIds;

    /**
     * 危险等级
     */
    private Integer dangerLevel;

    /**
     * 没有危险等级时默认为1
     */
    public Integer getDangerLevel() {
        if (dangerLevel == null) {
            return 1;
        }
        return dangerLevel;
    }
}