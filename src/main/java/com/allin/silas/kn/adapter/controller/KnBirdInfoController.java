package com.allin.silas.kn.adapter.controller;

import cn.hutool.crypto.SecureUtil;
import com.allin.silas.kn.adapter.dto.EditBirdInfoDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.adapter.dto.UploadImageDto;
import com.allin.silas.kn.adapter.vo.KnBirdInfoMenuVo;
import com.allin.silas.kn.adapter.vo.KnProjectBirdInfoVo;
import com.allin.silas.kn.app.service.KnBirdInfoCommandService;
import com.allin.silas.kn.app.service.KnBirdInfoQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import com.allin.view.log.annotation.Log;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 知识百科/鸟种百科
 *
 * <AUTHOR>
 * @since 2025/4/26
 */
@Validated
@RestController
@RequestMapping("/kn/bird")
class KnBirdInfoController {

    // 查询服务
    private final KnBirdInfoQueryService birdInfoQueryService;

    // 命令服务
    private final KnBirdInfoCommandService birdInfoCommandService;

    KnBirdInfoController(KnBirdInfoQueryService birdInfoQueryService,
                         KnBirdInfoCommandService birdInfoCommandService) {
        this.birdInfoQueryService = birdInfoQueryService;
        this.birdInfoCommandService = birdInfoCommandService;
    }

    /**
     * 查询鸟种百科列表
     */
    @GetMapping("/menu")
    Result<List<KnBirdInfoMenuVo>> menu() {
        final List<KnBirdInfoMenuVo> menu = birdInfoQueryService.menu();
        return Result.ok(menu);
    }

    /**
     * 查询图片
     *
     * @param id 图片id
     */
    @GetMapping("/image/{id}")
    ResponseEntity<Result<String>> image(@PathVariable String id) {
        final String imageBase64 = birdInfoQueryService.getImageBase64(id);
        if (imageBase64 == null) {
            return ResponseEntity.notFound().build();
        }

        // 返回带有 ETag 的响应
        return ResponseEntity.ok()
                .cacheControl(CacheControl.maxAge(30, TimeUnit.DAYS)
                        .cachePrivate()
                        .noTransform()
                        .mustRevalidate())
                .eTag(SecureUtil.sha256(imageBase64))
                .body(Result.ok(birdInfoQueryService.getImageBase64(id)));
    }

    /**
     * 查询鸟种详情
     *
     * @param chineseName 中文名
     */
    @GetMapping("/info/{chineseName}")
    Result<KnProjectBirdInfoVo> info(@PathVariable String chineseName) {
        return Result.ok(birdInfoQueryService.infoByProjectId(chineseName, SecurityContextHolder.getProjectId()));
    }

    /**
     * 设置是否为本场鸟种
     */
    @Log(title = "鸟种百科", operDesc = "设置是否为本场鸟种")
    @PutMapping("/set_local")
    Result<Void> setLocal(@RequestBody @Validated IsLocalDto isLocalDto) {
        birdInfoCommandService.setLocal(isLocalDto);
        return Result.ok();
    }

    /**
     * 上传图片
     *
     * @return 返回图片id
     */
    @PostMapping("/upload/image")
    Result<String> uploadImage(@RequestBody @Validated UploadImageDto uploadImageDto) {
        return Result.ok(birdInfoCommandService.uploadImage(uploadImageDto));
    }

    /**
     * 编辑鸟种详情
     */
    @Log(title = "鸟种百科", operDesc = "编辑鸟种详情")
    @PutMapping("/info")
    Result<Void> editInfo(@Validated @RequestBody EditBirdInfoDto editInfoDto) {
        birdInfoCommandService.editInfo(editInfoDto);
        return Result.ok();
    }
}
