package com.allin.silas.kn.adapter.vo;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 鸟种百科目录
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KnBirdInfoMenuVo extends KnBirdInfoBaseVo {

    /**
     * 是否为本场，0 否, 1 是
     */
    private Integer isLocal;

    /**
     * 封面图片id
     */
    private String defaultImageId;

    public String getDefaultImageId() {
        if (defaultImageId == null && CollUtil.isNotEmpty(getImageIds())) {
            return getImageIds().get(0);
        }
        return defaultImageId;
    }
}