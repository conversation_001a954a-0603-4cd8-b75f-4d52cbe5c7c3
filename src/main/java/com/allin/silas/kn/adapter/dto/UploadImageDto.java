package com.allin.silas.kn.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 上传图片
 *
 * <AUTHOR>
 * @since 2025/5/27
 */
@Data
public class UploadImageDto {

    /**
     * 中文名称
     */
    @NotBlank
    @Size(max = 255)
    private String chineseName;

    /**
     * 图片base64
     */
    @NotBlank
    private String imageBase64;

    public String getChineseName() {
        if (chineseName != null) {
            chineseName = chineseName.strip();
        }
        return chineseName;
    }
}
