package com.allin.silas.kn.adapter.controller;

import cn.hutool.crypto.SecureUtil;
import com.allin.silas.kn.adapter.dto.EditInsectInfoDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.adapter.dto.UploadImageDto;
import com.allin.silas.kn.adapter.vo.KnInsectInfoBaseVo;
import com.allin.silas.kn.adapter.vo.KnInsectInfoVo;
import com.allin.silas.kn.app.service.KnInsectInfoCommandService;
import com.allin.silas.kn.app.service.KnInsectInfoQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import com.allin.view.log.annotation.Log;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 知识百科/虫种百科
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
@Validated
@RestController
@RequestMapping("/kn/insect")
class KnInsectInfoController {

    // 查询服务
    private final KnInsectInfoQueryService insectInfoQueryService;

    // 命令服务
    private final KnInsectInfoCommandService insectInfoCommandService;

    KnInsectInfoController(KnInsectInfoQueryService insectInfoQueryService,
                           KnInsectInfoCommandService insectInfoCommandService) {
        this.insectInfoQueryService = insectInfoQueryService;
        this.insectInfoCommandService = insectInfoCommandService;
    }

    /**
     * 查询虫种百科列表
     */
    @GetMapping("/menu")
    Result<List<KnInsectInfoBaseVo>> menu() {
        final List<KnInsectInfoBaseVo> menu = insectInfoQueryService.menu();
        return Result.ok(menu);
    }

    /**
     * 查询图片
     *
     * @param id 图片id
     */
    @GetMapping("/image/{id}")
    ResponseEntity<Result<String>> image(@PathVariable String id) {
        final String imageBase64 = insectInfoQueryService.getImageBase64(id);
        if (imageBase64 == null) {
            return ResponseEntity.notFound().build();
        }

        // 返回带有 ETag 的响应
        return ResponseEntity.ok()
                .cacheControl(CacheControl.maxAge(30, TimeUnit.DAYS)
                        .cachePrivate()
                        .noTransform()
                        .mustRevalidate())
                .eTag(SecureUtil.sha256(imageBase64))
                .body(Result.ok(insectInfoQueryService.getImageBase64(id)));
    }

    /**
     * 查询虫种详情
     *
     * @param chineseName 中文名
     */
    @GetMapping("/info/{chineseName}")
    Result<KnInsectInfoVo> info(@PathVariable String chineseName) {
        return Result.ok(insectInfoQueryService.infoByProjectId(chineseName, SecurityContextHolder.getProjectId()));
    }

    /**
     * 设置是否为本场虫种
     */
    @Log(title = "虫种百科", operDesc = "设置是否为本场虫种")
    @PutMapping("/set_local")
    Result<Void> setLocal(@RequestBody IsLocalDto isLocalDto) {
        insectInfoCommandService.setLocal(isLocalDto);
        return Result.ok();
    }

    /**
     * 上传图片
     *
     * @return 返回图片id
     */
    @PostMapping("/upload/image")
    Result<String> uploadImage(@RequestBody @Validated UploadImageDto uploadImageDto) {
        return Result.ok(insectInfoCommandService.uploadImage(uploadImageDto));
    }

    /**
     * 编辑虫种详情
     */
    @Log(title = "虫种百科", operDesc = "编辑虫种详情")
    @PutMapping("/info")
    Result<Void> editInfo(@Validated @RequestBody EditInsectInfoDto editInfoDto) {
        insectInfoCommandService.editInfo(editInfoDto);
        return Result.ok();
    }
}
