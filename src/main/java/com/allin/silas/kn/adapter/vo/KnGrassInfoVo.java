package com.allin.silas.kn.adapter.vo;

import lombok.Data;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 草种百科基本信息
 */
@Data
public class KnGrassInfoVo {
    /**
     * 主键id
     */
    private String id;

    /**
     * 中文名
     */
    private String chineseName;

    /**
     * 学名
     */
    private String sciName;

    /**
     * 目
     */
    private String orderName;

    /**
     * 科
     */
    private String familyName;

    /**
     * 草种简介
     */
    private String summary;

    /**
     * 鉴别特征
     */
    private String features;

    /**
     * 生长习性
     */
    private String habits;

    /**
     * 分布范围
     */
    private String range;

    /**
     * 生长环境
     */
    private String habitat;

    /**
     * 繁殖方式
     */
    private String breeding;

    /**
     * 防治措施
     */
    private String measures;

    /**
     * 是否为本场草种，0 否, 1 是
     */
    private Integer isLocal;

    /**
     * 别名列表
     */
    private List<String> alias;

    /**
     * 其他别名
     */
    private List<String> otherAlias;

    /**
     * 图片id
     */
    private List<String> imageIds;

    /**
     * 活动月份
     */
    private Collection<Integer> activeMonths;

    /**
     * 吸引鸟种
     */
    private Map<String, Boolean> attractBirds;

    /**
     * 吸引虫种
     */
    private Map<String, Boolean> attractInsects;

    /**
     * 封面图片id
     */
    private String defaultImageId;

    /**
     * 自定义图片id列表
     */
    private List<String> otherImageIds;

    public String getDefaultImageId() {
        if (defaultImageId == null) {
            return getImageIds().get(0);
        }
        return defaultImageId;
    }
}