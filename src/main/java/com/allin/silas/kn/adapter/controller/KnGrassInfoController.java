package com.allin.silas.kn.adapter.controller;

import cn.hutool.crypto.SecureUtil;
import com.allin.silas.kn.adapter.dto.EditGrassInfoDto;
import com.allin.silas.kn.adapter.dto.IsLocalDto;
import com.allin.silas.kn.adapter.dto.UploadImageDto;
import com.allin.silas.kn.adapter.vo.KnGrassInfoBaseVo;
import com.allin.silas.kn.adapter.vo.KnGrassInfoVo;
import com.allin.silas.kn.app.service.KnGrassInfoCommandService;
import com.allin.silas.kn.app.service.KnGrassInfoQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import com.allin.view.log.annotation.Log;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 知识百科/草种百科
 *
 * <AUTHOR>
 * @since 2025/5/10
 */
@Validated
@RestController
@RequestMapping("/kn/grass")
class KnGrassInfoController {

    // 查询服务
    private final KnGrassInfoQueryService grassInfoQueryService;

    // 命令服务
    private final KnGrassInfoCommandService grassInfoCommandService;

    KnGrassInfoController(KnGrassInfoQueryService grassInfoQueryService,
                          KnGrassInfoCommandService grassInfoCommandService) {
        this.grassInfoQueryService = grassInfoQueryService;
        this.grassInfoCommandService = grassInfoCommandService;
    }

    /**
     * 查询草种百科列表
     */
    @GetMapping("/menu")
    Result<List<KnGrassInfoBaseVo>> menu() {
        final List<KnGrassInfoBaseVo> menu = grassInfoQueryService.menu();
        return Result.ok(menu);
    }

    /**
     * 查询图片
     *
     * @param id 图片id
     */
    @GetMapping("/image/{id}")
    ResponseEntity<Result<String>> image(@PathVariable String id) {
        final String imageBase64 = grassInfoQueryService.getImageBase64(id);
        if (imageBase64 == null) {
            return ResponseEntity.notFound().build();
        }

        // 返回带有 ETag 的响应
        return ResponseEntity.ok()
                .cacheControl(CacheControl.maxAge(30, TimeUnit.DAYS)
                        .cachePrivate()
                        .noTransform()
                        .mustRevalidate())
                .eTag(SecureUtil.sha256(imageBase64))
                .body(Result.ok(grassInfoQueryService.getImageBase64(id)));
    }

    /**
     * 查询草种详情
     *
     * @param chineseName 中文名
     */
    @GetMapping("/info/{chineseName}")
    Result<KnGrassInfoVo> info(@PathVariable String chineseName) {
        return Result.ok(grassInfoQueryService.infoByProjectId(chineseName, SecurityContextHolder.getProjectId()));
    }

    /**
     * 设置是否为本场草种
     *
     */
    @Log(title = "草种百科", operDesc = "设置是否为本场草种")
    @PutMapping("/set_local")
    Result<Void> setLocal(@RequestBody IsLocalDto isLocalDto) {
        grassInfoCommandService.setLocal(isLocalDto);
        return Result.ok();
    }

    /**
     * 上传图片
     *
     * @return 返回图片id
     */
    @PostMapping("/upload/image")
    Result<String> uploadImage(@RequestBody @Validated UploadImageDto uploadImageDto) {
        return Result.ok(grassInfoCommandService.uploadImage(uploadImageDto));
    }

    /**
     * 编辑草种详情
     */
    @Log(title = "草种百科", operDesc = "编辑草种详情")
    @PutMapping("/info")
    Result<Void> editInfo(@Validated @RequestBody EditGrassInfoDto editInfoDto) {
        grassInfoCommandService.editInfo(editInfoDto);
        return Result.ok();
    }
}
