package com.allin.silas.kn.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * 纳入本地
 *
 * <AUTHOR>
 * @since 2025/5/27
 */
@Data
public class IsLocalDto {

    /**
     * 中文名称
     */
    @NotBlank
    @Size(max = 255)
    private String chineseName;

    /**
     * 是否纳入本地,0-否,1-是
     */
    @Range(min = 0, max = 1)
    private Integer isLocal;

    public String getChineseName() {
        if (chineseName != null) {
            chineseName = chineseName.strip();
        }
        return chineseName;
    }
}
