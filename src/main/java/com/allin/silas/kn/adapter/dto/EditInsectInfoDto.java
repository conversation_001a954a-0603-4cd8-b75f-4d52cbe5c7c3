package com.allin.silas.kn.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 编辑虫种信息
 *
 * <AUTHOR>
 * @since 2025/4/30
 */
@Data
public class EditInsectInfoDto {

    /**
     * 中文名
     */
    @NotBlank(message = "kn.chinese.name.empty")
    @Size(max = 255)
    private String chineseName;

    /**
     * 别名列表
     */
    private Set<String> otherAlias;

    /**
     * 活动月份
     */
    private Set<Integer> activeMonths;

    /**
     * 防治措施
     */
    private String measures;

    /**
     * 默认图片id
     */
    @NotNull
    private String defaultImageId;

    /**
     * 新增的图片base64列表
     */
    private List<String> imageIds;
}
