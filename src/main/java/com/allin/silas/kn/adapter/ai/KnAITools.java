package com.allin.silas.kn.adapter.ai;

import com.allin.silas.kn.adapter.vo.KnBirdInfoBaseVo;
import com.allin.silas.kn.adapter.vo.KnBirdInfoVo;
import com.allin.silas.kn.app.service.KnBirdInfoQueryService;
import com.allin.view.base.domain.Result;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 知识百科MCP工具服务
 *
 * <AUTHOR>
 * @since 2025/5/13
 */
@Service
class KnAITools {

    private final KnBirdInfoQueryService birdInfoQueryService;

    KnAITools(KnBirdInfoQueryService birdInfoQueryService) {
        this.birdInfoQueryService = birdInfoQueryService;
    }

    /**
     * 鸟种百科基本信息列表
     *
     * <AUTHOR>
     * @since 2025/5/13
     */
    @Tool(description = "查询鸟种百科中有哪些鸟种，返回鸟种的基本信息")
    public Result<List<KnBirdInfoBaseVo>> list() {
        return Result.ok(birdInfoQueryService.listBase());
    }

    /**
     * 鸟种百科详情
     *
     * <AUTHOR>
     * @since 2025/5/13
     */
    @Tool(description = "根据鸟种的中文名称查询鸟种的详细信息")
    public Result<KnBirdInfoVo> info(
            @ToolParam(description = "鸟种中文名称") String chineseName) {
        return Result.ok(birdInfoQueryService.info(chineseName));
    }

}
