package com.allin.silas.kn.adapter.vo;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

import java.util.List;

/**
 * 虫种百科基本信息
 */
@Data
public class KnInsectInfoBaseVo {

    /**
     * 中文名
     */
    private String chineseName;

    /**
     * 学名
     */
    private String sciName;

    /**
     * 目
     */
    private String orderName;

    /**
     * 科
     */
    private String familyName;

    /**
     * 是否为本场，0 否, 1 是
     */
    private Integer isLocal;

    /**
     * 别名列表
     */
    private List<String> alias;

    /**
     * 系统默认图片id列表
     */
    private List<String> imageIds;

    /**
     * 默认封面图片id
     */
    private String defaultImageId;

    public String getDefaultImageId() {
        if (defaultImageId == null && CollUtil.isNotEmpty(getImageIds())) {
            return getImageIds().get(0);
        }
        return defaultImageId;
    }

}