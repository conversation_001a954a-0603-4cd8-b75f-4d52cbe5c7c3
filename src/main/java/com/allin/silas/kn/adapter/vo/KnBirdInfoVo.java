package com.allin.silas.kn.adapter.vo;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 鸟种详情
 *
 * <AUTHOR>
 * @since 2025/4/30
 */
@Data
public class KnBirdInfoVo {

    /**
     * 中文名
     */
    private String chineseName;

    /**
     * 学名
     */
    private String sciName;

    /**
     * 目
     */
    private String orderName;

    /**
     * 科
     */
    private String familyName;

    /**
     * 属
     */
    private String genusName;

    /**
     * 简介
     */
    private String summary;

    /**
     * 鉴别特征
     */
    private String features;

    /**
     * 生活习性
     */
    private String habits;

    /**
     * 分布范围
     */
    private String range;

    /**
     * 栖息环境
     */
    private String habitat;

    /**
     * 繁殖方式
     */
    private String breeding;

    /**
     * 危险等级
     */
    private Integer dangerLevel;

    /**
     * 危险鸟群个体数
     */
    private Integer dangerCount;

    /**
     * 活动时间，日行性、晨昏性、夜行性
     */
    private String activeTime;

    /**
     * 防治措施
     */
    private String measures;

    /**
     * 保护等级
     */
    private Integer iucnLevel;

    /**
     * 别名列表
     */
    private Set<String> alias;

    /**
     * 图片id
     */
    private List<String> imageIds;
}
