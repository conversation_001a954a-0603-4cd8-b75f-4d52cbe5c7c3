package com.allin.silas.kn.adapter.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 某机场的鸟种详情
 *
 * <AUTHOR>
 * @since 2025/4/30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KnProjectBirdInfoVo extends KnBirdInfoVo {

    /**
     * 是否为本场鸟种，0 否, 1 是
     */
    private Integer isLocal;

    /**
     * 本场活动情况
     */
    private String localActivities;

    /**
     * 活动月份
     */
    private Collection<Integer> activeMonths;

    /**
     * 封面图片id
     */
    private String defaultImageId;

    /**
     * 自定义图片id列表
     */
    private List<String> otherImageIds;

    /**
     * 其他别名
     */
    private Set<String> otherAlias;

    public String getDefaultImageId() {
        if (defaultImageId == null) {
            return getImageIds().get(0);
        }
        return defaultImageId;
    }
}
