package com.allin.silas.kn.adapter.ai;

import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 知识百科工具配置
 * MCP服务器配置类，负责注册MCP工具
 *
 * <AUTHOR>
 * @since 2025/5/13
 */
@Configuration
class KnAIToolConfig {

    @Bean
    public ToolCallbackProvider knTools(KnAITools knAITools) {
        return MethodToolCallbackProvider.builder().toolObjects(knAITools).build();
    }
}
