package com.allin.silas.dev.detect.constant;

/**
 * 探测设备发送TCP数据协议常量类
 *
 * <AUTHOR>
 * @since 2025/3/24
 */
public class TcpMessageConstants {


    /**
     * 协议数据头长度
     */
    public static final Integer RECEIVE_HEAD_LEN = 215;

    /**
     * 协议数据头部标识
     */
    public static final String HEAD_SIGN = "AABB";

    /**
     * 协议数据尾部标识
     */
    public static final String TAIL_SIGN = "1122";


    /**
     * TCP数据包最大长度
     */
    public static final Long TCP_MAX_LENGTH = 10485760L;

    /**
     * TCP数据头部标识
     */
    public static final byte[] HEADER = {(byte) 0xAA, (byte) 0xBB};

    /**
     * TCP数据尾部标识
     */
    public static final byte[] TAIL = {(byte) 0x11, (byte) 0x22};


    /**
     * 设备心跳指令标识
     */
    public static final byte[] COMMAND_HEARTBEAT = {(byte) 0xA1, (byte) 0xA1};










}
