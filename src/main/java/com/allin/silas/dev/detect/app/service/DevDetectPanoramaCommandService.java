package com.allin.silas.dev.detect.app.service;


import com.allin.silas.dev.detect.adapter.dto.DevDetectPanoramaDto;
import com.allin.silas.dev.detect.adapter.dto.EditDevDetectPanoramaDto;


/**
* <AUTHOR>
* 针对表【dev_detect_panorama】的数据库操作Service
* @date 2025-04-29 17:12:59
*/
public interface DevDetectPanoramaCommandService {

    boolean addPanorama(DevDetectPanoramaDto dto);

    boolean updatePanorama(EditDevDetectPanoramaDto dto);

    boolean deleteById(String id);

}
