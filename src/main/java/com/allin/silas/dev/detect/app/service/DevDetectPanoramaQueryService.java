package com.allin.silas.dev.detect.app.service;


import com.allin.silas.dev.detect.adapter.query.DevDetectPanoramaQuery;
import com.allin.silas.dev.detect.adapter.vo.DevDetectPanoramaVo;

import java.util.List;

/**
* <AUTHOR>
* 针对表【dev_detect_panorama】的数据库操作Service
* @date 2025-04-29 17:12:59
*/
public interface DevDetectPanoramaQueryService {


    List<DevDetectPanoramaVo> findList(DevDetectPanoramaQuery param);
}
