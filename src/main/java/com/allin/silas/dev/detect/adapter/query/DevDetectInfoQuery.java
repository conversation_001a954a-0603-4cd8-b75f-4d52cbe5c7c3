package com.allin.silas.dev.detect.adapter.query;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;

/**
 * 探测设备信息表
 */

@Data
public class DevDetectInfoQuery implements Serializable {

    /**
     * 项目ID
     */
    @JsonIgnore
    private String projectId;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备类型
     */
    private String devType;

    /**
     * 所在跑道
     */
    private String runwayId;

    /**
     * 设备状态:0：离线；1：在线；2：异常
     */
    @Range(min = 0, max = 2)
    private Integer devStatus;

}
