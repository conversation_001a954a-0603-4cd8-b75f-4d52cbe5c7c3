package com.allin.silas.dev.detect.app.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.allin.silas.dev.detect.adapter.vo.DevDetectModeVo;
import com.allin.silas.dev.detect.app.entity.DevDetectMode;
import com.allin.silas.dev.detect.app.service.DevDetectModeQueryService;
import com.allin.silas.dev.detect.infra.repository.DevDetectModeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 针对表【dev_detect_mode】的数据库操作Service实现
 * @date 2025-04-29 17:32:15
 */
@Service
public class DevDetectModeQueryServiceImpl implements DevDetectModeQueryService {

    private final DevDetectModeMapper devDetectModeMapper;

    public DevDetectModeQueryServiceImpl(DevDetectModeMapper devDetectModeMapper) {
        this.devDetectModeMapper = devDetectModeMapper;
    }

    @Override
    public List<DevDetectModeVo> list(String devNum) {
        LambdaQueryWrapper<DevDetectMode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DevDetectMode::getDevNum, devNum);
        queryWrapper.orderByDesc(DevDetectMode::getId);
        List<DevDetectMode> devDetectModes = devDetectModeMapper.selectList(queryWrapper);
        if (ObjectUtil.isNotEmpty(devDetectModes)) {
            return BeanUtil.copyToList(devDetectModes, DevDetectModeVo.class);
        }
        return Collections.emptyList();
    }

}




