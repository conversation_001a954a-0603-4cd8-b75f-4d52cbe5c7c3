package com.allin.silas.dev.detect.infra.relay;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.dev.detect.app.manager.impl.DevRelayManagerImpl;
import com.allin.silas.dev.detect.constant.RelayConstants;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;

import java.net.InetSocketAddress;


/**
 * 网络继电器处理器
 *
 * <AUTHOR>
 * @since 2025/5/8
 */
@Slf4j
@ChannelHandler.Sharable
public class RelayServer extends SimpleChannelInboundHandler<String> {


    private final DevRelayManagerImpl devRelayService;


    public RelayServer(DevRelayManagerImpl devRelayService) {
        this.devRelayService = devRelayService;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        InetSocketAddress address = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = address.getAddress().getHostAddress();
        int port = address.getPort();
        log.info("网络继电器开关客户端{}:{}上线", ip, port);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        InetSocketAddress socket = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = socket.getAddress().getHostAddress();
        log.info(StrUtil.format("网络开关客户端{} 异常断开{}...", ip, cause.getMessage()));
        RelayRunner.getChannels().remove(ip);
        ctx.close();
    }

    @Override
    public void channelRegistered(ChannelHandlerContext ctx) {
        InetSocketAddress socket = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = socket.getAddress().getHostAddress();
        log.info(StrUtil.format("网络开关客户端{} 注册...", ip));
    }

    @Override
    public void channelUnregistered(ChannelHandlerContext ctx) {
        InetSocketAddress socket = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = socket.getAddress().getHostAddress();
        log.info(StrUtil.format("网络开关客户端{} 已注销...", ip));
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        InetSocketAddress address = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = address.getAddress().getHostAddress();
        int port = address.getPort();
        log.info("网络继电器开关客户端{}:{}离线", ip, port);
        ctx.close();
    }

    /**
     * 接收客继电器发送消息
     *
     */
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, String message) {
        try {
            // 需约定好客户端发送的消息格式，网络注册包自定义内容为所属设备编号
            if (!message.contains(RelayConstants.END_LINE)) {
                RelayRunner.getChannels().put(message, ctx.channel());
            }
        }finally {
            ReferenceCountUtil.release(message);
        }
    }
}
