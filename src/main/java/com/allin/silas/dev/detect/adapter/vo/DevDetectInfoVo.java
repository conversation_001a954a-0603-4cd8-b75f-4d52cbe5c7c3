package com.allin.silas.dev.detect.adapter.vo;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 探测设备信息
 */
@Data
public class DevDetectInfoVo implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备类型
     */
    private String devType;

    /**
     * 设备ip地址
     */
    private String devIp;

    /**
     * 设备端口
     */
    private Integer devPort;

    /**
     * 设备分割位数量
     */
    private Integer devSeparatorsNums;

    /**
     * 设备俯仰角
     */
    private Integer devPitch;

    /**
     * 设备经度
     */
    private String longitude;

    /**
     * 设备纬度
     */
    private String latitude;

    /**
     * 设备高度
     */
    private Float height;

    /**
     * 正北偏移角
     */
    private Float devNorthOffset;

    /**
     * 所在跑道id
     */
    private String runwayId;

    /**
     * 是否具备电源：0：否；1：是；
     */
    private Integer isHaveSwitch;

    /**
     * 电源开关状态：0：关闭；1：开启；
     */
    private Integer switchStatus;

    /**
     * 电源开关IP
     */
    private String switchIp;

    /**
     * 电源开关端口
     */
    private Integer switchPort;

    /**
     * 备注信息
     */
    private String devRemarks;

    /**
     * 全景图文件ID
     */
    private String panoramaImg;

    /**
     * 设备状态：0：离线；1：在线；2：异常
     */
    private Integer devStatus;

    /**
     * 设备状态详情
     */
    private String devRunStatus;

    /**
     * 设备扫描半径（单位：米，保留整数）
     */
    private Integer scanRadius;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;


}
