package com.allin.silas.dev.detect.infra.range;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.common.util.tcp.DevByteBufUtils;
import com.allin.silas.dev.detect.infra.channel.DevDetectChannelPool;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.util.concurrent.TimeUnit;


/**
 * 低空围栏处理器
 *
 * <AUTHOR>
 * @since 2025/7/12
 */
@Slf4j
@Component
@ChannelHandler.Sharable
public class RangeHandlerAdapter extends ChannelInboundHandlerAdapter {


    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        InetSocketAddress address = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = address.getAddress().getHostAddress();
        int port = address.getPort();
        log.info("低空围栏客户端{}:{}上线", ip, port);
        DevDetectChannelPool.putChannels(ip, port, ctx.channel());
        // 定时发送心跳信息
        ctx.executor().scheduleAtFixedRate(() -> {
            if (ctx.channel().isActive()) {
                // 每2秒发送一次心跳
                ctx.writeAndFlush(DevByteBufUtils.genHeartBeatBytes());
            }
        }, 0, 2, TimeUnit.SECONDS);
        // 需要传递连接事件
        ctx.fireChannelActive();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        InetSocketAddress socket = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = socket.getAddress().getHostAddress();
        int port = socket.getPort();
        log.info("低空围栏客户端{} 异常断开{}...", ip, cause.getMessage());
        DevDetectChannelPool.removeChannels(ip,port);
        ctx.close();
    }

    @Override
    public void channelRegistered(ChannelHandlerContext ctx) {
        InetSocketAddress socket = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = socket.getAddress().getHostAddress();
        log.info("低空围栏客户端{} 注册...", ip);
    }

    @Override
    public void channelUnregistered(ChannelHandlerContext ctx) {
        InetSocketAddress socket = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = socket.getAddress().getHostAddress();
        log.info(StrUtil.format("低空围栏客户端{} 已注销...", ip));
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        InetSocketAddress address = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = address.getAddress().getHostAddress();
        int port = address.getPort();
        log.info("低空围栏客户端{}:{}离线", ip, port);
        DevDetectChannelPool.removeChannels(ip,port);
        ctx.close();
    }


}
