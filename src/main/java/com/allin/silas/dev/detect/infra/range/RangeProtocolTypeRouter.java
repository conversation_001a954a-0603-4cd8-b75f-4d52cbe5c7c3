package com.allin.silas.dev.detect.infra.range;

import com.allin.silas.dev.detect.infra.range.dto.TcpReceiveDataDto;
import com.allin.silas.dev.detect.infra.range.enums.TcpMessageTypeEnums;
import com.allin.view.base.utils.thread.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.Router;
import org.springframework.integration.channel.ExecutorChannel;
import org.springframework.messaging.MessageChannel;

/**
 * 低空围栏设备协议数据路由配置
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
@Slf4j
@Configuration
public class RangeProtocolTypeRouter {

    /**
     * 主输入通道, 从 Netty 解码器接收消息
     */
    @Bean
    public MessageChannel rangeInputChannel() {
        return new ExecutorChannel(ThreadPoolUtils.createThreadPoolTaskExecutor(TcpMessageTypeEnums.TARGET.getChannelName(), 10));
    }

    /**
     * 设备全景图输入通道
     */
    @Bean
    public MessageChannel roiInputChannel() {
        return new ExecutorChannel(ThreadPoolUtils.createThreadPoolTaskExecutor(TcpMessageTypeEnums.PANORAMA.getChannelName(), 10));
    }

    /**
     * 消息路由器
     */
    @Router(inputChannel = "rangeInputChannel")
    public String router(TcpReceiveDataDto dto) {
        return TcpMessageTypeEnums.getChannelName(dto.getType());
    }
}
