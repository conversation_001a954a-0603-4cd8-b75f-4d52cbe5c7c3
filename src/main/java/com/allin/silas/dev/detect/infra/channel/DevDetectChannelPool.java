package com.allin.silas.dev.detect.infra.channel;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.allin.silas.common.enums.WsMessageType;
import com.allin.silas.dev.detect.app.entity.DevDetectInfo;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.ws.entity.WsMessage;
import com.allin.view.ws.handler.CustomParamWebSocketHandler;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.netty.channel.Channel;
import org.springframework.lang.Nullable;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 探测设备TCP通道管理
 * <AUTHOR>
 * @since 2025/7/29
 */
public class DevDetectChannelPool {

    /**
     * 用于保存标识和连接通道之间的关联关系
     * key: ip-port
     */
    private static final Map<String, Channel> CHANNEL_MAP = new ConcurrentHashMap<>();


    /**
     * 用于保存设备信息
     * key:设备编号
     * value:ip-port
     */
    private static final Map<String, String> DEV_MAP = new ConcurrentHashMap<>();

    public static void put(String key, Channel channel) {
        CHANNEL_MAP.putIfAbsent(key, channel);
    }


    /**
     * 根据设备编号获取通道
     */
    public static Channel getChannel(String devNum) {
        String ipPort = DEV_MAP.get(devNum);
        if (StrUtil.isBlank(ipPort)) {
            throw new ValidationFailureException("指令下发失败，设备未连接！");
        }
        Channel channel = CHANNEL_MAP.get(ipPort);
        if (ObjectUtil.isEmpty(channel)) {
            throw new ValidationFailureException("指令下发失败，获取TCP通道为空！");
        }
        return channel;
    }


    /**
     * 根据设备编号获取通道(为空返回null,不抛出异常)
     */
    @Nullable
    public static Channel tryGetChannel(String devNum) {
        String ipPort = DEV_MAP.get(devNum);
        if (StrUtil.isBlank(ipPort)) {
            return null;
        }
        Channel channel = CHANNEL_MAP.get(ipPort);
        if (ObjectUtil.isEmpty(channel)) {
            return null;
        }
        return channel;
    }


    /**
     * 获取所有的在线通道
     */
    public static Map<String, Channel> getChannels() {
        return CHANNEL_MAP;
    }

    /**
     * 获取所有的设备信息
     */
    public static Map<String, String> getDevMap() {
        return DEV_MAP;
    }


    /**
     * 移除channel
     * @param channel 连接通道
     */
    public static void remove(Channel channel) {
        CHANNEL_MAP.entrySet().removeIf(entry -> channel.equals(entry.getValue()));
    }

    /**
     * 保存ip:port和channel之间的关联关系
     *
     * @param ip      ip
     * @param port    端口
     * @param channel 通道
     */
    public static void putChannels(String ip, Integer port, Channel channel) {
        CHANNEL_MAP.put(ip + "-" + port, channel);
    }

    /**
     * 保存设备编号和ip:port之间的关联关系
     *
     * @param devNum 设备编号
     * @param ip     ip
     * @param port   端口
     */
    public static void putDevMap(String devNum, String ip, Integer port) {
        DEV_MAP.put(devNum, ip + "-" + port);
    }


    /**
     * 删除离线的通道信息
     * @param ip   通道ip地址
     * @param port 通道端口
     */
    public static void removeChannels(String ip, Integer port) {
        String ipPort = ip + "-" + port;
        CHANNEL_MAP.remove(ipPort);
        DEV_MAP.forEach((k, v) -> {
            if (v.equals(ipPort)) {
                final DevDetectInfo devDetectInfo = Db.getOne(Wrappers.lambdaQuery(DevDetectInfo.class)
                        .eq(DevDetectInfo::getDevNum, k));
                if(devDetectInfo !=null){
                    //通知前端界面设备已离线
                    CustomParamWebSocketHandler webSocketHandler = SpringUtil.getBean(CustomParamWebSocketHandler.class);
                    webSocketHandler.getSender().sendToParam(devDetectInfo.getProjectId(),
                            WsMessage.of(WsMessageType.DEV_DETECT_OFFLINE.getCode(), devDetectInfo.getDevName()+"设备已离线！"));
                }

            }
        });
        // 使用 iterator 的 remove 方法安全删除元素
        DEV_MAP.entrySet().removeIf(entry -> entry.getValue().equals(ipPort));
    }



}
