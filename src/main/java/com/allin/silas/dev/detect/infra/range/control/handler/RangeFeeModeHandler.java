package com.allin.silas.dev.detect.infra.range.control.handler;


import cn.hutool.core.util.ObjectUtil;
import com.allin.silas.common.util.tcp.DevByteBufUtils;
import com.allin.silas.dev.detect.adapter.dto.DevDetectControlDto;
import com.allin.silas.dev.detect.adapter.vo.DevDetectModeControlVo;
import com.allin.silas.dev.detect.infra.channel.DevDetectChannelPool;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.stereotype.Component;

/**
 * 低空围栏空闲模式控制
 * <AUTHOR>
 * @since 2025/7/28
 */
@Component
@Slf4j
public class RangeFeeModeHandler implements RangeControlHandler {



    @ServiceActivator(inputChannel = "freeModeChannel", outputChannel = "rangeReplyChannel")
    @Override
    public DevDetectModeControlVo control(DevDetectControlDto dto) {
        DevDetectModeControlVo vo = new DevDetectModeControlVo();
        //获取设备通道信息
        String devNum = dto.getDevNum();
        //获取设备通道信息
        Channel channel = DevDetectChannelPool.getChannel(devNum);
        vo.setDevNum(devNum);
        if (ObjectUtil.isEmpty(channel)) {
            vo.setIsOk(false);
            vo.setMessage("指令下发失败，设备不在线！");
            return vo;
        }
        //下发控制指令,待机模式、全景拼图模式等不需要参数的都可以用该指令
        ByteBuf byteBuf = DevByteBufUtils.genTcpCmdBytes(0,dto.getCode());
        channel.writeAndFlush(byteBuf);
        vo.setIsOk(true);
        return vo;

    }



}
