package com.allin.silas.dev.detect.adapter.vo;


import lombok.Data;
import java.time.LocalDateTime;

/**
 * 雷达设备扩展信息
 * @TableName dev_detect_radar_ext
 */

@Data
public class DevDetectRadarExtVo {


    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备分割位数量
     */
    private Integer devSeparatorsNums;

    /**
     * 设备俯仰角
     */
    private Float devPitch;

    /**
     * 设备俯仰分隔位数量
     */
    private Integer devPitchSeparatorsNums;

    /**
     * 设备水平分割位数量
     */
    private Float devHorizontalAngle;

    /**
     * 设备俯仰分割位数量
     */
    private Float devPitchAngle;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private LocalDateTime updatedTime;

    /**
     * 修改人
     */
    private String updatedBy;
}