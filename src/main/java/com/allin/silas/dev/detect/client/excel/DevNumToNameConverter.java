package com.allin.silas.dev.detect.client.excel;

import cn.hutool.extra.spring.SpringUtil;
import cn.idev.excel.converters.Converter;
import cn.idev.excel.converters.WriteConverterContext;
import cn.idev.excel.metadata.data.WriteCellData;
import com.allin.silas.dev.detect.app.manager.DevDetectInfoManager;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Excel 设备编号转设备名称转换器
 *
 * <AUTHOR>
 * @since 2025/7/25
 */
@Slf4j
public class DevNumToNameConverter implements Converter<Object> {

    private final DevDetectInfoManager devDetectInfoManager;

    public DevNumToNameConverter() {
        this.devDetectInfoManager = SpringUtil.getBean(DevDetectInfoManager.class);
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Object> context) {
        final Object value = context.getValue();
        if (Objects.isNull(context.getValue())) {
            return new WriteCellData<>("-");
        }
        try {
            if (value instanceof String devNum) {
                final String devName = devDetectInfoManager.getDevNameFromCache(devNum);
                return new WriteCellData<>(Objects.requireNonNullElse(devName, "-"));
            } else if (value instanceof List devNums) {
                final List<String> devNames = new ArrayList<>();
                for (Object num : devNums) {
                    final String devName = devDetectInfoManager.getDevNameFromCache(String.valueOf(num));
                    if (devName != null) {
                        devNames.add(devName);
                    }
                }
                return new WriteCellData<>(String.join(",", devNames));
            }
            log.warn("不支持的数据类型: {}", value.getClass());
            return new WriteCellData<>("-");
        } catch (Exception e) {
            log.error("设备编号转换失败", e);
            return new WriteCellData<>("-");
        }
    }
}
