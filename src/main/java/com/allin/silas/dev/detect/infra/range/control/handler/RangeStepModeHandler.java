package com.allin.silas.dev.detect.infra.range.control.handler;


import cn.hutool.core.util.ObjectUtil;
import com.allin.silas.common.util.tcp.DevByteBufUtils;
import com.allin.silas.dev.detect.adapter.dto.DevDetectControlDto;
import com.allin.silas.dev.detect.adapter.dto.DevDetectStepParamDto;
import com.allin.silas.dev.detect.adapter.vo.DevDetectModeControlVo;
import com.allin.silas.dev.detect.app.enums.DevDetectRunModeEnums;
import com.allin.silas.dev.detect.infra.channel.DevDetectChannelPool;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 低空围栏位置模式控制
 * <AUTHOR>
 * @since 2025/7/28
 */
@Component
@Slf4j
public class RangeStepModeHandler implements RangeControlHandler {



    @ServiceActivator(inputChannel = "stepModeChannel", outputChannel = "rangeReplyChannel")
    @Override
    public DevDetectModeControlVo control(DevDetectControlDto dto) {
        DevDetectModeControlVo vo = new DevDetectModeControlVo();
        //获取设备通道信息
        String devNum = dto.getDevNum();
        //获取设备通道信息
        Channel channel = DevDetectChannelPool.getChannel(devNum);
        vo.setDevNum(devNum);
        if (ObjectUtil.isEmpty(channel)) {
            vo.setIsOk(false);
            vo.setMessage("指令下发失败，设备不在线！");
            return vo;
        }
        //获取步进参数
        DevDetectStepParamDto stepParam = dto.getDevDetectStepParamDto();
        List<Integer> stepParamList = stepParam.getStepParam();
        int dataLen = stepParamList.size()*4;
        ByteBuf dataBuf = Unpooled.buffer(dataLen);
        for (Integer param : stepParamList) {
            dataBuf.writeIntLE(param);
        }
        //下发控制指令
        ByteBuf byteBuf = DevByteBufUtils.genTcpCmdBytes(dataLen,DevDetectRunModeEnums.STEP.getCode());
        byte[] headData = byteBuf.array();
        byte[] data = dataBuf.array();
        //数据组装一起
        byte[] sendData = DevByteBufUtils.byteMerge(headData,data);
        channel.writeAndFlush(sendData);
        vo.setIsOk(true);
        return vo;

    }



}
