package com.allin.silas.dev.detect.infra.range.control;

import com.allin.silas.dev.detect.adapter.dto.DevDetectControlDto;
import com.allin.silas.dev.detect.adapter.vo.DevDetectModeControlVo;
import org.springframework.integration.annotation.Gateway;
import org.springframework.integration.annotation.MessagingGateway;

/**
 * <AUTHOR>
 * @since 2025/7/28
 */
@MessagingGateway
public interface RangeControlGateway {

    @Gateway(requestChannel = "rangeOutputChannel", replyChannel = "rangeReplyChannel")
    DevDetectModeControlVo control(DevDetectControlDto dto);

}
