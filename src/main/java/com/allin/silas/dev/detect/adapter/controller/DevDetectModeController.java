package com.allin.silas.dev.detect.adapter.controller;

import com.allin.silas.dev.detect.adapter.dto.DevDetectModeDto;
import com.allin.silas.dev.detect.adapter.vo.DevDetectModeVo;
import com.allin.silas.dev.detect.app.enums.DevDetectRunModeEnums;
import com.allin.silas.dev.detect.app.service.DevDetectModeCommandService;
import com.allin.silas.dev.detect.app.service.DevDetectModeQueryService;
import com.allin.view.base.domain.Result;
import com.allin.view.base.domain.SelectItem;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 探测设备模式管理
 *
 * <AUTHOR>
 * @since 2025/4/28
 */
@Validated
@RestController
@RequestMapping("/dev/dev_detect_mode")
class DevDetectModeController {

    private final DevDetectModeCommandService devDetectModeCommandService;

    private final DevDetectModeQueryService devDetectModeQueryService;

    public DevDetectModeController(DevDetectModeCommandService devDetectModeService, DevDetectModeQueryService devDetectModeQueryService) {
        this.devDetectModeCommandService = devDetectModeService;
        this.devDetectModeQueryService = devDetectModeQueryService;
    }

    /**
     * 获取探测设备运行模式可选列表
     */
    @GetMapping("/select")
    public Result<List<SelectItem<Integer>>> select() {
        List<SelectItem<Integer>> selectItems = new ArrayList<>();
        for (DevDetectRunModeEnums value : DevDetectRunModeEnums.values()) {
            final SelectItem<Integer> item = new SelectItem<>();
            item.setLabel(value.getDesc());
            item.setValue(value.getCode());
            selectItems.add(item);
        }
        return Result.ok(selectItems);
    }

    /**
     * 获取设备的运行模式列表
     */
    @GetMapping
    public Result<List<DevDetectModeVo>> get(@RequestParam String devNum) {
        return Result.ok(devDetectModeQueryService.list(devNum));
    }

    /**
     * 编辑设备模式参数
     */
    @PostMapping("/config")
    public Result<Void> config(@Validated @RequestBody DevDetectModeDto devDetectMode) {
        return devDetectModeCommandService.editModeConfig(devDetectMode) ? Result.ok() : Result.fail();
    }
}
