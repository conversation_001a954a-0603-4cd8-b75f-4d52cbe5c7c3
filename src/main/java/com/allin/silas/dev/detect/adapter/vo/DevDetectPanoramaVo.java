package com.allin.silas.dev.detect.adapter.vo;

import com.allin.view.config.serialize.annotation.ApiFoxNoIgnore;
import com.allin.view.file.pojo.vo.FileVo;
import com.allin.view.file.serializer.FileIdToFileInfo;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 *  探测设备全景图信息
 *  dev_detect_panorama
 */
@Data
public class DevDetectPanoramaVo {
    /**
     * 主键ID（雪花ID）
     */
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 通道类型
     */
    private String channelType;

    /**
     * 全景图偏移角度
     */
    private Float panoramaOffset;

    /**
     * 全景图图片ID
     */
    @FileIdToFileInfo(key="panoramaImgFile")
    private String panoramaImg;


    /**
     * 全景图文件信息
     */
    @ApiFoxNoIgnore
    private List<FileVo> panoramaImgFile;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 是否删除：0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 是否默认：0-否，1-是
     */
    private Integer isDefault;
}