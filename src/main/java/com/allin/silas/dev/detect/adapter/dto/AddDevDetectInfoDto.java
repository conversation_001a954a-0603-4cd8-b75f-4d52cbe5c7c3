package com.allin.silas.dev.detect.adapter.dto;


import com.allin.silas.dev.detect.app.entity.DevDetectCameraExt;
import com.allin.silas.dev.detect.app.entity.DevDetectRadarExt;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 探测设备信息表
 */
@Data
public class AddDevDetectInfoDto implements Serializable {

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空")
    private String devName;

    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    private String devNum;

    /**
     * 设备类型
     */
    @NotBlank(message = "设备类型不能为空")
    private String devType;

    /**
     * 设备ip地址
     */
    @NotBlank(message = "设备ip地址不能为空")
    private String devIp;

    /**
     * 设备端口
     */
    @NotNull(message = "设备端口不能为空")
    private Integer devPort;

    /**
     * 设备分割位数量
     */
    @NotNull(message = "设备分割位数量不能为空")
    private Integer devSeparatorsNums;

    /**
     * 设备俯仰角
     */
    @NotNull(message = "设备俯仰角不能为空")
    private Integer devPitch;

    /**
     * 设备经度
     */
    @NotBlank(message = "设备经度不能为空")
    private String longitude;

    /**
     * 设备纬度
     */
    @NotBlank(message = "设备纬度不能为空")
    private String latitude;

    /**
     * 设备高度
     */
    @NotNull(message = "设备高度不能为空")
    private Float height;

    /**
     * 正北偏移角
     */
    @NotNull(message = "正北偏移角不能为空")
    private Float devNorthOffset;

    /**
     * 设备扫描半径（单位：米，保留整数）
     */
    @NotNull(message = "设备扫描半径不能为空")
    private Integer scanRadius;

    /**
     * 所在跑道
     */
    @NotBlank
    private String runwayId;

    /**
     * 是否具备电源：0：否；1：是；
     */
    private Integer isHaveSwitch;

    /**
     * 电源开关状态：0：关闭；1：开启；
     */
    private Integer switchStatus;

    /**
     * 电源开关IP
     */
    private String switchIp;

    /**
     * 电源开关端口
     */
    private Integer switchPort;

    /**
     * 备注信息
     */
    private String devRemarks;

    /**
     * 全景图文件ID
     */
    private String panoramaImg;


    /**
     * 光电探测设备拓展表
     */
    private DevDetectCameraExt devDetectCameraExt;

    /**
     * 雷达探测设备拓展表
     */
    private DevDetectRadarExt devDetectRadarExt;


}
