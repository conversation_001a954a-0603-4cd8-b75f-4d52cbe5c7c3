package com.allin.silas.dev.detect.app.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 探测设备信息表
 */
@TableName(value = "dev_detect_info")
@Data
public class DevDetectInfo implements Serializable {

    /**
     * ID主键（雪花ID）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private String projectId;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备类型
     */
    private String devType;

    /**
     * 设备ip地址
     */
    private String devIp;

    /**
     * 设备端口
     */
    private Integer devPort;

    /**
     * 设备分割位数量
     */
    private Integer devSeparatorsNums;

    /**
     * 设备俯仰角
     */
    private Integer devPitch;

    /**
     * 设备经度
     */
    private String longitude;

    /**
     * 设备纬度
     */
    private String latitude;

    /**
     * 设备高度
     */
    private Float height;

    /**
     * 正北偏移角
     */
    private Float devNorthOffset;

    /**
     * 设备扫描半径（单位：米，保留整数）
     */
    private Integer scanRadius;

    /**
     * 所在跑道
     */
    private String runwayId;

    /**
     * 是否具备电源：0：否；1：是；
     */
    private Integer isHaveSwitch;

    /**
     * 电源开关状态：0：关闭；1：开启；
     */
    private Integer switchStatus;

    /**
     * 电源开关IP
     */
    private String switchIp;

    /**
     * 电源开关端口
     */
    private Integer switchPort;

    /**
     * 备注信息
     */
    private String devRemarks;

    /**
     * 全景图文件ID
     */
    private String panoramaImg;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;


}
