package com.allin.silas.dev.detect.app.service;


import com.allin.silas.dev.detect.adapter.vo.DevDetectStatusVo;

import java.util.List;

/**
* <AUTHOR>
* 针对表【dev_detect_status】的数据库操作Service
* @date 2025-04-29 17:55:25
*/
public interface DevDetectStatusQueryService {

    /**
     * 根据设备编号查询设备状态
     */
    DevDetectStatusVo findByDevDetectStatus(String devNum);

    List<DevDetectStatusVo> findDevStatusList(String projectId);
}
