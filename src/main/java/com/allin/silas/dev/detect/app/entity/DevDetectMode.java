package com.allin.silas.dev.detect.app.entity;


import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 探测设备信息模式
 * @TableName dev_detect_mode
 */
@TableName(value ="dev_detect_mode")
@Data
public class DevDetectMode {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 模式编号
     */
    private String devModeNum;

    /**
     * 模式名称
     */
    private String devModeName;

    /**
     * 模式控制参数（JSON格式数据）
     */
    private String devModeParam;

    /**
     * 是否可用；（0:不可用；1：可用）
     */
    private Integer isUseable;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;
}