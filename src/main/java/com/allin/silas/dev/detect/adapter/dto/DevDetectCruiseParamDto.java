package com.allin.silas.dev.detect.adapter.dto;


import lombok.Data;

import java.util.List;


/**
 * 巡航模式参数DTO
 * <AUTHOR>
 * @since 2025/7/28
 */
@Data
public class DevDetectCruiseParamDto {

    /**
     * 预置位数量
     */
    private Integer positionNum;

    /**
     * 方位角
     */
    private List<Float> azimuthParam;

    /**
     * 俯仰角
     */
    private List<Float> pitchParam;

    /**
     * 停止时间
     */
    private List<Integer> stopTime;



    


}
