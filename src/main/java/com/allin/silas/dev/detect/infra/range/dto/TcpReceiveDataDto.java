package com.allin.silas.dev.detect.infra.range.dto;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * 前端设备发送数据实体
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
@Data
public class TcpReceiveDataDto {

    /**
     * 前端发送数据ip
     */
    private String ip;

    /**
     * 前端发送数据端口
     */
    private Integer port;

    /**
     * 前端发送数据报头
     */
    private String messageHead;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 指令标识
     */
    private Integer type;

    /**
     * 数据包大小
     */
    private Long dataLength;

    /**
     * 校验和
     */
    private Short checkSum;

    /**
     * 数据报尾
     */
    private String messageEnd;



    /**
     * 大数据包
     */
    private ByteBuf data;


    /**
     * 释放数据包
     */
    public void release() {
        if (data != null && data.refCnt() > 0) {
            data.release();
        }
    }

}
