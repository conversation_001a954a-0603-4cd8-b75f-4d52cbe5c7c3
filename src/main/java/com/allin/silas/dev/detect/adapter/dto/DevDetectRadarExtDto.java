package com.allin.silas.dev.detect.adapter.dto;


import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 雷达扩展信息实体
 * @TableName dev_detect_radar_ext
 */
@Data
public class DevDetectRadarExtDto {


    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    private String devNum;

    /**
     * 设备分割位数量
     */
    private Integer devSeparatorsNums;

    /**
     * 设备俯仰角
     */
    private Float devPitch;

    /**
     * 设备俯仰分隔位数量
     */
    private Integer devPitchSeparatorsNums;

    /**
     * 设备水平分割位数量
     */
    private Float devHorizontalAngle;

    /**
     * 设备俯仰分隔位数量
     */
    private Float devPitchAngle;


}