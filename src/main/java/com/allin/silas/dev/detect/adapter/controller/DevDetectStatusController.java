package com.allin.silas.dev.detect.adapter.controller;

import com.allin.silas.dev.detect.adapter.vo.DevDetectStatusVo;
import com.allin.silas.dev.detect.app.service.DevDetectStatusQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 探测设备状态信息
 *
 * <AUTHOR>
 * @since 2025/4/28
 */
@Validated
@RestController
@RequestMapping("/dev/dev_detect_status")
class DevDetectStatusController {

    private final DevDetectStatusQueryService devDetectStatusService;

    DevDetectStatusController(DevDetectStatusQueryService devDetectStatusService) {
        this.devDetectStatusService = devDetectStatusService;
    }


    /**
     * 根据设备编号查询设备状态信息
     */
    @GetMapping("/find_dev_status")
    public Result<DevDetectStatusVo> findByDevDetectStatus(String devNum){
        return Result.ok(devDetectStatusService.findByDevDetectStatus(devNum));
    }

    /**
     * 查询探测设备状态信息列表
     */
    @GetMapping("/find_status_list")
    public Result<List<DevDetectStatusVo>> findDevStatusList(){
        return Result.ok(devDetectStatusService.findDevStatusList(SecurityContextHolder.getProjectId()));
    }




}
