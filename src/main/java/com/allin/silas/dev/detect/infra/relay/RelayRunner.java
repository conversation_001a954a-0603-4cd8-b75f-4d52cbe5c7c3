package com.allin.silas.dev.detect.infra.relay;

import com.allin.silas.dev.detect.app.manager.impl.DevRelayManagerImpl;
import com.allin.silas.dev.detect.constant.DevConstants;
import com.allin.view.tcp.server.TcpServerV2;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 继电器开关TCP服务端
 * <AUTHOR>
 * @since 2025/5/7
 */
@Component
@Slf4j
public class RelayRunner implements CommandLineRunner {

    /**
     * 用于保存已连接通道信息
     */
    private static final Map<String, Channel> CHANNEL_MAP = new ConcurrentHashMap<>();

    public static Map<String, Channel> getChannels() {
        return CHANNEL_MAP;
    }

    private final DevRelayManagerImpl devRelayService;

    public RelayRunner(DevRelayManagerImpl devRelayService) {
        this.devRelayService = devRelayService;
    }


    @Override
    @Async
    public void run(String... args) {
        TcpServerV2.bootstrap(DevConstants.relayPort, 2, new ChannelInitializer<>() {
            @Override
            protected void initChannel(Channel channel)  {
                channel.pipeline().addLast(new StringDecoder());
                channel.pipeline().addLast(new StringEncoder());
                channel.pipeline().addLast(new RelayServer(devRelayService));
            }
        });

    }
}
