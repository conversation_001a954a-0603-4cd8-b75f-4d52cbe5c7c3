package com.allin.silas.dev.detect.adapter.dto;


import lombok.Data;


/**
 * 打击设备控制参数DTO
 * <AUTHOR>
 * @since 2025/7/28
 */
@Data
public class DevDetectDriveParamDto {

    /**
     * 打击方位角
     */
    private Float azimuth;

    /**
     * 打击俯仰角
     */
    private Float pitch;

    /**
     * 是否打击：0-否，1-是
     */
    private Integer isStrike;

    /**
     * 歌曲名
     */
    private Integer song;

    /**
     * 音量
     */
    private Integer volume;






}
