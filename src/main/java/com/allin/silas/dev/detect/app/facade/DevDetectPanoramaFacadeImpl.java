package com.allin.silas.dev.detect.app.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.allin.silas.dev.detect.adapter.dto.DevDetectPanoramaDto;
import com.allin.silas.dev.detect.app.entity.DevDetectInfo;
import com.allin.silas.dev.detect.app.manager.DevDetectInfoManager;
import com.allin.silas.dev.detect.app.service.DevDetectPanoramaCommandService;
import com.allin.silas.dev.detect.client.DevDetectPanoramaFacade;
import com.allin.silas.dev.detect.infra.range.dto.TcpPanoramaDto;
import com.allin.silas.visual.constant.FileStorageConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;

/**
 * 设备全景图接口实现类
 * <AUTHOR>
 * @since 2025/7/22
 */

@Slf4j
@Service
public class DevDetectPanoramaFacadeImpl implements DevDetectPanoramaFacade {

    private final DevDetectInfoManager devDetectInfoManager;
    private final DevDetectPanoramaCommandService detectPanoramaCommandService;

    public DevDetectPanoramaFacadeImpl(DevDetectInfoManager devDetectInfoManager, DevDetectPanoramaCommandService detectPanoramaCommandService) {
        this.devDetectInfoManager = devDetectInfoManager;
        this.detectPanoramaCommandService = detectPanoramaCommandService;
    }

    /**
     * 保存设备全景图
     */
    @Override
    public void savDevPanorama(TcpPanoramaDto dto) {
        String devNum = dto.getDevNum();
        //根据设备编号获取projectId
        DevDetectInfo devDetectInfo = devDetectInfoManager.getInfoFromCache(devNum);
        if(ObjectUtil.isNotEmpty(devDetectInfo)){
            dto.setProjectId(devDetectInfo.getProjectId());
        }
        //存储全景图数据
        byte[] imgData = dto.getImgData();
        LocalDateTime now = LocalDateTime.now();
        String timestamp = LocalDateTimeUtil.format(now, DatePattern.PURE_DATETIME_MS_FORMATTER);
        String fileName = String.format("%s_%s_%s.jpg",devNum,timestamp,dto.getChannelType());
        String imgUrl = File.separator + String.join(File.separator, "upload", "panorama", devNum, fileName);
        try {
            FileUtil.writeBytes(imgData, FileStorageConstants.visualImageFileDirPath+imgUrl);
        } catch (Exception e) {
            log.error("保存全景图片失败：{}", e.getMessage());
        }
        DevDetectPanoramaDto devDetectPanorama = new DevDetectPanoramaDto();
        BeanUtil.copyProperties(dto, devDetectPanorama);
        devDetectPanorama.setPanoramaImg(imgUrl);
        //左目和右目转换
        devDetectPanorama.setChannelName(dto.getChannelType().equals("0")?"左目":"右目");
        devDetectPanorama.setPanoramaOffset(0f);
        detectPanoramaCommandService.addPanorama(devDetectPanorama);

    }
}
