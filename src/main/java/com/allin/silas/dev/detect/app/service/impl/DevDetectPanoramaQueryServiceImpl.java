package com.allin.silas.dev.detect.app.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.common.enums.IsDeletedEnums;
import com.allin.silas.dev.detect.adapter.query.DevDetectPanoramaQuery;
import com.allin.silas.dev.detect.adapter.vo.DevDetectPanoramaVo;
import com.allin.silas.dev.detect.app.entity.DevDetectPanorama;
import com.allin.silas.dev.detect.app.service.DevDetectPanoramaQueryService;
import com.allin.silas.dev.detect.infra.repository.DevDetectPanoramaMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* 针对表【dev_detect_panorama】的数据库操作Service实现
* @date 2025-04-29 17:12:59
*/
@Service
public class DevDetectPanoramaQueryServiceImpl implements DevDetectPanoramaQueryService {

    private final DevDetectPanoramaMapper devDetectPanoramaMapper;

    public DevDetectPanoramaQueryServiceImpl(DevDetectPanoramaMapper devDetectPanoramaMapper) {
        this.devDetectPanoramaMapper = devDetectPanoramaMapper;
    }

    @Override
    public List<DevDetectPanoramaVo> findList(DevDetectPanoramaQuery param) {
        LambdaQueryWrapper<DevDetectPanorama> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(param.getProjectId()),DevDetectPanorama::getProjectId,param.getProjectId());
        queryWrapper.eq(StrUtil.isNotEmpty(param.getDevNum()),DevDetectPanorama::getDevNum,param.getDevNum());
        queryWrapper.eq(StrUtil.isNotEmpty(param.getChannelName()),DevDetectPanorama::getChannelName,param.getChannelName());
        queryWrapper.eq(DevDetectPanorama::getIsDeleted,IsDeletedEnums.FALSE.getCode());
        queryWrapper.orderByDesc(DevDetectPanorama::getId);
        return BeanUtil.copyToList(devDetectPanoramaMapper.selectList(queryWrapper),DevDetectPanoramaVo.class);
    }
}




