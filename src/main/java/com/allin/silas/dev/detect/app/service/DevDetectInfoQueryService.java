package com.allin.silas.dev.detect.app.service;


import com.allin.silas.dev.detect.adapter.query.DevDetectInfoQuery;
import com.allin.silas.dev.detect.adapter.vo.DevDetectInfoVo;

import java.util.List;

/**
* 设备信息表 服务类
* <AUTHOR>
*/
public interface DevDetectInfoQueryService {

    /**
     * 探测设备信息分页查询
     */
    List<DevDetectInfoVo> findList(DevDetectInfoQuery param);

    /**
     *判断探测设备是否存在
     */
    boolean isExistDevNum(String devNum);
}
