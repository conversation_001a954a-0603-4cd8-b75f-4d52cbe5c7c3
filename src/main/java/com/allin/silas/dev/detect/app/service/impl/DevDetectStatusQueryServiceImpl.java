package com.allin.silas.dev.detect.app.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.allin.silas.dev.detect.adapter.vo.DevDetectStatusVo;
import com.allin.silas.dev.detect.app.service.DevDetectStatusQueryService;
import com.allin.silas.dev.detect.app.entity.DevDetectInfo;
import com.allin.silas.dev.detect.app.entity.DevDetectStatus;
import com.allin.silas.dev.detect.infra.repository.DevDetectInfoMapper;
import com.allin.silas.dev.detect.infra.repository.DevDetectStatusMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 探测设备状态信息服务实现类
 * <AUTHOR>
*/
@Service
public class DevDetectStatusQueryServiceImpl implements DevDetectStatusQueryService {

    private final DevDetectStatusMapper devDetectStatusMapper;

    private final DevDetectInfoMapper devDetectInfoMapper;

    public DevDetectStatusQueryServiceImpl(DevDetectStatusMapper devDetectStatusMapper,
                                           DevDetectInfoMapper devDetectInfoMapper) {
        this.devDetectStatusMapper = devDetectStatusMapper;
        this.devDetectInfoMapper = devDetectInfoMapper;
    }

    @Override
    public DevDetectStatusVo findByDevDetectStatus(String devNum) {
        LambdaQueryWrapper<DevDetectStatus> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DevDetectStatus::getDevNum,devNum);
        queryWrapper.orderByDesc(DevDetectStatus::getId);
        DevDetectStatus devDetectStatus = devDetectStatusMapper.selectOne(queryWrapper);
        DevDetectStatusVo infoVo = new DevDetectStatusVo();
        BeanUtil.copyProperties(devDetectStatus,infoVo);
        return infoVo;
    }

    @Override
    public List<DevDetectStatusVo> findDevStatusList(String projectId) {
        List<DevDetectStatusVo> devDetectStatusVos = CollUtil.newArrayList();
        //查询项目下的所有设备
        LambdaQueryWrapper<DevDetectInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DevDetectInfo::getProjectId,projectId);
        List<DevDetectInfo> devDetectInfos = devDetectInfoMapper.selectList(queryWrapper);
       for(DevDetectInfo devDetectInfo:devDetectInfos){
            String devNum = devDetectInfo.getDevNum();
            DevDetectStatusVo vo = new DevDetectStatusVo();
            //获取探测设备状态详情信息
            DevDetectStatusVo devDetectStatusVo = findByDevDetectStatus(devNum);
           if(ObjectUtil.isNotEmpty(devDetectStatusVo.getDevNum())){
                BeanUtil.copyProperties(devDetectStatusVo,vo);
           }else{
               //没有说明设备离线
               vo.setDevStatus(0);
               vo.setCreatedTime(devDetectInfo.getCreatedTime());
           }
           vo.setDevNum(devNum);
           vo.setDevName(devDetectInfo.getDevName());
           devDetectStatusVos.add(vo);
       }
        return devDetectStatusVos;
    }
}




