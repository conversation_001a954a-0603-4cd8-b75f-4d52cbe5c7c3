package com.allin.silas.dev.detect.infra.range.dto;

import lombok.Data;

/**
 * TCP接收设备伺服状态信息
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
public class TcpDevServoDto {

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 当前分割位
     */
    private Integer currentPosition;

    /**
     * 方位角
     */
    private Float azimuth;

    /**
     * 俯仰角
     */
    private Float pitch;


    /**
     * 状态：0停止；1：启动
     */
    private Integer state;



}
