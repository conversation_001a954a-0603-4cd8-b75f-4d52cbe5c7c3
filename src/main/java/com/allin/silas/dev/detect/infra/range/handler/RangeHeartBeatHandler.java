package com.allin.silas.dev.detect.infra.range.handler;


import com.allin.silas.common.enums.WsMessageType;
import com.allin.silas.dev.detect.adapter.dto.DevDetectStatusDto;
import com.allin.silas.dev.detect.adapter.dto.DevDetectStatusPayloadDto;
import com.allin.silas.dev.detect.app.entity.DevDetectInfo;
import com.allin.silas.dev.detect.app.enums.DevDetectRunModeEnums;
import com.allin.silas.dev.detect.app.manager.DevDetectInfoManager;
import com.allin.silas.dev.detect.client.event.DevDetectStatusEvent;
import com.allin.silas.dev.detect.infra.channel.DevDetectChannelPool;
import com.allin.silas.dev.detect.infra.range.dto.TcpReceiveDataDto;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.stereotype.Component;

/**
 * 低空围栏心跳处理
 * <AUTHOR>
 * @since 2025/7/12
 */
@Component
@Slf4j
public class RangeHeartBeatHandler implements RangeDataHandler {

    private final DevDetectInfoManager devDetectInfoManager;
    private final ApplicationContext applicationContext;


    public RangeHeartBeatHandler(DevDetectInfoManager devDetectInfoManager, ApplicationContext applicationContext) {
        this.devDetectInfoManager = devDetectInfoManager;
        this.applicationContext = applicationContext;
    }


    /**
     * 低空围栏心跳处理
     */
    @ServiceActivator(inputChannel = "rangeHeartBeatChannel")
    @Override
    public void dealData(TcpReceiveDataDto payload) {
        log.info("低空围栏心跳处理：{}", payload.getType());
        //解析设备心跳数据
        DevDetectChannelPool.putDevMap(payload.getDevNum(), payload.getIp(), payload.getPort());
        DevDetectStatusDto dto = createDevDetectStatusDto(payload);
        DevDetectStatusPayloadDto payloadDto = new DevDetectStatusPayloadDto();
        payloadDto.setDevNum(payload.getDevNum());
        payloadDto.setDevName(dto.getDevName());
        payloadDto.setProjectId(dto.getProjectId());
        payloadDto.setDevDetectStatusDto(dto);
        payloadDto.setMessageType(WsMessageType.DEV_DETECT_STATUS.getCode());
        //推送到前端web界面
        applicationContext.publishEvent(new DevDetectStatusEvent(this,payloadDto));


    }


    /**
     * 创建低空围栏设备状态数据
     * @param payload 接收数据
     *
     */
    private DevDetectStatusDto createDevDetectStatusDto(TcpReceiveDataDto payload) {
        //解析设备心跳数据
        ByteBuf in = payload.getData();
        String devNum = payload.getDevNum();
        //根据设备编号获取一次设备名称
        DevDetectInfo devDetectInfo = devDetectInfoManager.getInfoFromCache(devNum);
        String devName ="";
        String projectId = "";
        if(devDetectInfo != null){
            devName = devDetectInfo.getDevName();
            projectId = devDetectInfo.getProjectId();
        }
        // 系统工作模式
        int state = in.readUnsignedByte();
        // 自动打击开关(0:关闭；1开启)
        int autoStrike = in.readUnsignedByte();
        // 打击设备状态
        int strikeStatus = in.readUnsignedByte();
        // 歌曲序号
        int songNumber = in.readIntLE();
        // 打击强度（音量）比率
        float volume = in.readFloatLE();
        // 联动模式是否为自动
        int isAuto = in.readUnsignedByte();
        // 联动设备类型
        int linkDevType = in.readUnsignedByte();
        // 系统预留状态
        byte[] states = new byte[116];
        in.readBytes(states);
        // 电机是否上电
        int work = in.readUnsignedByte();
        // 电机是否使能
        int isWork = in.readUnsignedByte();
        // 电机是否回零
        int isZero = in.readUnsignedByte();
        // 电机是否静止
        int isStop = in.readUnsignedByte();
        // 三光板上FPGA版本
        int fpgaVersion = in.readIntLE();
        // agx上的FPGA版本
        int fpga = in.readIntLE();
        // 处理软件版本
        int dealVersion = in.readIntLE();
        // 通信协议版本
        int tongxinVersion = in.readIntLE();
        // 上一版本批次号
        long lastTargetNum = in.readLongLE();
        // 设备温度
        float devTemperature = in.readFloatLE();
        // 伺服状态
        int sifuStatus = in.readUnsignedByte();
        // 光纤状态
        int netStatus = in.readUnsignedByte();
        // 三光板状态
        int threePlateStatus = in.readUnsignedByte();
        // 相机状态
        int cameraStatus = in.readUnsignedByte();
        // 其他参数预留
        byte[] bak = new byte[83];
        in.readBytes(bak);
        return DevDetectStatusDto.builder()
                .devNum(devNum)
                .devName(devName)
                .projectId(projectId)
                .workMode(state)
                .workModeName(DevDetectRunModeEnums.getNameByCode(state))
                .isAutoStrike(autoStrike)
                .strikeStatus(strikeStatus)
                .strikeSong(songNumber)
                .strikeVolume(volume)
                .linkMode(isAuto)
                .linkDevType(linkDevType)
                .motorPower(work)
                .motorEnable(isWork)
                .motorIsZero(isZero)
                .motorIsStop(isStop)
                .fpgaVersionBoard(fpgaVersion)
                .fpgaVersionAgx(fpga)
                .softwareVersion(dealVersion)
                .protocolVersion(tongxinVersion)
                .lastBatchId(lastTargetNum)
                .deviceTemperature(devTemperature)
                .controllerStatus(sifuStatus)
                .fiberStatus(netStatus)
                .boardStatus(threePlateStatus)
                .cameraStatus(cameraStatus).build();

    }
}
