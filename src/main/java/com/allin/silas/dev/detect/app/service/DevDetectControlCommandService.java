package com.allin.silas.dev.detect.app.service;


import com.allin.silas.dev.detect.adapter.dto.DevDetectControlDto;
import com.allin.silas.dev.detect.adapter.vo.DevDetectModeControlVo;

/**
* 探测设备控制 服务类
* <AUTHOR>
*/
public interface DevDetectControlCommandService {

    /**
     * 探测设备控制
     * @param devControl
     * @return
     */
    DevDetectModeControlVo control(DevDetectControlDto devControl);
}
