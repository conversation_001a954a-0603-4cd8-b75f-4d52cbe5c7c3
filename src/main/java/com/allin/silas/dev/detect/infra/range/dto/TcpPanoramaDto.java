package com.allin.silas.dev.detect.infra.range.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TCP接收全景图信息
 * <AUTHOR>
 * @since 2025/7/16
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class TcpPanoramaDto {

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 相机编号
     */
    private String channelType;
    /**
     * 方位角
     */
    private Float azimuth;

    /**
     * 俯仰角
     */
    private Float pitch;

    /**
     * 当前位置
     */
    private Integer currentPosition;

    /**
     * 图片数据
     */
    private byte[] imgData;

}
