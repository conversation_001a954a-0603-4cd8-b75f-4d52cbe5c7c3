package com.allin.silas.dev.detect.adapter.dto;


import com.allin.silas.dev.detect.app.entity.DevDetectMode;
import com.allin.silas.dev.detect.app.enums.DevDetectRunModeEnums;
import com.allin.view.base.enums.base.IEnums;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 探测设备运行模式
 * dev_detect_mode
 */
@Data
public class DevDetectModeDto {

    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    private String devNum;

    /**
     * 模式编号
     */
    @NotBlank(message = "模式编号不能为空")
    private String devModeNum;

    /**
     * 模式控制参数JSON格式数据
     */
    private String devModeParam;


    public DevDetectMode toEntity() {
        final DevDetectMode devDetectMode = new DevDetectMode();
        devDetectMode.setDevNum(this.devNum);
        devDetectMode.setDevModeNum(this.devModeNum);
        devDetectMode.setDevModeName(IEnums.tryGetDesc(DevDetectRunModeEnums.class, this.devModeNum)
                .orElse("-"));
        devDetectMode.setDevModeParam(this.devModeParam);
        return devDetectMode;
    }

}