package com.allin.silas.dev.detect.app.service;


import com.allin.silas.dev.detect.adapter.dto.AddDevDetectInfoDto;
import com.allin.silas.dev.detect.adapter.dto.EditDevDetectInfoDto;


/**
* 设备信息表 服务类
* <AUTHOR>
*/
public interface DevDetectInfoCommandService {

    boolean addDevInfo(AddDevDetectInfoDto devDetectInfo);

    /**
     * 删除设备信息
     */
    boolean deleteById(String id);

    /**
     * 更新探测设备信息
     */
    boolean updateDevInfo(EditDevDetectInfoDto devDetectInfo);

    /**
     *开关继电器操作
     */
    boolean switchRelay(String devNum, int status);
}
