package com.allin.silas.dev.detect.app.service.impl;


import com.allin.silas.dev.detect.adapter.query.DevDetectInfoQuery;
import com.allin.silas.dev.detect.adapter.vo.DevDetectInfoVo;
import com.allin.silas.dev.detect.app.entity.DevDetectInfo;
import com.allin.silas.dev.detect.app.service.DevDetectInfoQueryService;
import com.allin.silas.dev.detect.infra.repository.DevDetectInfoMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DevDetectInfoQueryServiceImpl implements DevDetectInfoQueryService {

    private final DevDetectInfoMapper devDetectInfoMapper;


    public DevDetectInfoQueryServiceImpl(DevDetectInfoMapper devDetectInfoMapper) {
        this.devDetectInfoMapper = devDetectInfoMapper;
    }


    /**
     * 分页查询探测设备信息
     */
    @Override
    public List<DevDetectInfoVo> findList(DevDetectInfoQuery param) {
        return devDetectInfoMapper.findList(param);
    }

    /**
     * 判断设备编号是否存在
     */
    @Override
    public boolean isExistDevNum(String devNum) {
        return devDetectInfoMapper.selectCount(
                Wrappers.<DevDetectInfo>lambdaQuery()
                        .eq(DevDetectInfo::getDevNum, devNum)
        ) > 0;
    }

}




