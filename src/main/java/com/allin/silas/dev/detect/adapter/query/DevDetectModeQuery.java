package com.allin.silas.dev.detect.adapter.query;



import lombok.Data;


/**
 * 
 *探测设备运行模式查询实体
 */
@Data
public class DevDetectModeQuery {


    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 模式编号
     */
    private String devModeNum;

    /**
     * 模式名称
     */
    private String devModeName;

    /**
     * 模式控制参数
     */
    private Object devModeParam;

    /**
     * 是否可用；（0:不可用；1：可用）
     */
    private Integer isUseable;

}