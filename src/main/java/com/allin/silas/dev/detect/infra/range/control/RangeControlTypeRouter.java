package com.allin.silas.dev.detect.infra.range.control;

import com.allin.silas.dev.detect.adapter.dto.DevDetectControlDto;
import com.allin.silas.dev.detect.infra.range.enums.TcpDevControlEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.Router;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.messaging.MessageChannel;

/**
 * 低空围栏设备控制路由
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
@Slf4j
@Configuration
public class RangeControlTypeRouter {


    /**
     * 指令控制路由器
     */
    @Router(inputChannel = "rangeOutputChannel")
    public String router(DevDetectControlDto dto) {
        return TcpDevControlEnums.getChannelName(dto.getCode());
    }

    @Bean
    public MessageChannel rangeOutputChannel() {
        return new DirectChannel();
    }

    /**
     * 指令响应通道（用于接收处理结果）
     */
    @Bean
    public MessageChannel rangeReplyChannel() {
        return new DirectChannel();
    }
}
