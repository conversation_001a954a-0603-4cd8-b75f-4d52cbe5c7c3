package com.allin.silas.dev.detect.app.service.impl;


import com.allin.silas.dev.detect.adapter.dto.DevDetectModeDto;
import com.allin.silas.dev.detect.app.entity.DevDetectMode;
import com.allin.silas.dev.detect.app.service.DevDetectModeCommandService;
import com.allin.silas.dev.detect.infra.repository.DevDetectModeMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * 针对表【dev_detect_mode】的数据库操作Service实现
 * @date 2025-04-29 17:32:15
 */
@Service
public class DevDetectModeCommandServiceImpl implements DevDetectModeCommandService {

    private final DevDetectModeMapper devDetectModeMapper;

    public DevDetectModeCommandServiceImpl(DevDetectModeMapper devDetectModeMapper) {
        this.devDetectModeMapper = devDetectModeMapper;
    }

    @Override
    public boolean editModeConfig(DevDetectModeDto dto) {
        // 根据设备和设备模式查询是否存在配置信息。存在就修改，不存在就新增
        final DevDetectMode devDetectMode = devDetectModeMapper.selectOne(Wrappers.<DevDetectMode>lambdaQuery()
                .eq(DevDetectMode::getDevNum, dto.getDevNum())
                .eq(DevDetectMode::getDevModeNum, dto.getDevModeNum()));
        if (devDetectMode != null) {
            devDetectMode.setDevModeParam(dto.getDevModeParam());
            return devDetectModeMapper.updateById(devDetectMode) > 0;
        }
        return devDetectModeMapper.insert(dto.toEntity()) > 0;
    }


}




