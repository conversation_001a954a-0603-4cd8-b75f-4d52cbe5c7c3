package com.allin.silas.dev.detect.adapter.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 探测设备状态信息
 * <AUTHOR>
 * @since 2025/7/16
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DevDetectStatusDto {

    /**
     * 设备编号
     */
    private String devNum;
    /**
     * 设备名称
     */
    private String devName;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 工作模式
     * 0为空闲模式；
     * 1为全景拼图模式；
     * 2为步进模式；
     * 3为凝视模式；
     * 4为扇形分时周扫模式；
     * 5为连续周扫检测模式；
     * 6为联动模式；
     * 7为跟踪模式；
     * 8为巡航模式；
     * 9为自动聚焦模式；
     * 10为摇杆模式；
     * 11为跟踪飞机模式；
     * 12为指引跟踪起飞飞机模式；
     * 13为指引跟踪降落飞机模式；
     * 14为跑道探鸟模式；
     * 15为巡航模式--只跟空漂物;
     */
    private Integer workMode;

    /**
     * 工作模式名称
     */
    private String workModeName;

    /**
     * 是否自动打点
     * 0为否；
     * 1为是；
     */
    private Integer isAutoStrike;

    /**
     * 打击状态
     * 0未打击
     * 1正在打击
     */
    private Integer strikeStatus;

    /**
     * 打击歌曲名
     *
     */
    private Integer strikeSong;

    /**
     * 打击强度
     */
    private Float strikeVolume;



    /**
     * 联动模式下：
     * 0为前端自动；
     * 1为后台手动；
      */
    private Integer linkMode;

    /**
     * 当前联动设备类型：
     * 0，没有联动设备；
     * 1，正在和低空围栏联动；
     * 2，正在和第三方雷达联动
     */
    private Integer linkDevType;


    /**
     * 电机是否上电：
     * 0为未上电；
     * 1为已上电；
     */
    private Integer motorPower;

    /**
     * 电机是否使能：
     * 0为未使能；
     * 1为已使能；
     */
    private Integer motorEnable;


    /**
     * 点击是否回零：
     * 0为未回零；
     * 1为已回零；
     */
    private Integer motorIsZero;

    /**
     * 电机是否静止
     * 0为未静止；
     * 1为已静止；
     */
    private  Integer motorIsStop;


    /**
     * 三光板上 FPGA 版本
     */
    private long fpgaVersionBoard;

    /**
     * AGX 上 FPGA 版本
     */
    private long fpgaVersionAgx;

    /**
     * 处理软件版本
     */
    private long softwareVersion;

    /**
     * 通信协议版本
     */
    private long protocolVersion;

    /**
     *
     * 每次新的连接都要初始化为0；用于后台TCP阻塞问题。
     */
    private long lastBatchId;

    /**
     * 设备温度
     */
    private float deviceTemperature;

    /**
     * 控制器状态（0为异常，1为正常）
     */
    private Integer controllerStatus;

    /**
     * 光纤状态（0为异常，1为正常）
     */
    private Integer fiberStatus;

    /**
     * 三光板状态（0为异常，1为正常）
     */
    private Integer boardStatus;

    /**
     * 相机状态（0为异常，1为正常）
     */
    private Integer cameraStatus;
}
