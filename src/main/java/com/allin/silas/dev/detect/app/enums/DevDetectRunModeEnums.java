package com.allin.silas.dev.detect.app.enums;


import com.allin.view.base.enums.base.IEnums;

/**
 * 探测设备运行模式映射
 */
public enum DevDetectRunModeEnums implements IEnums {
    /**
     * -1为设备离线
     */
    OFFLINE(-1, "设备离线"),
    /**
     * 0为空闲模式/待机模式
     */
    FREE(0, "待机模式"),
    /**
     * 1为全景拼图模式
     */
    PANORAMA(1, "全景拼图模式"),
    /**
     * 2为步进模式
     */
    STEP(2, "步进模式"),
    /**
     * 3为凝视模式
     */
    GAZE(3, "凝视模式"),
    /**
     * 4为扇形分时周扫模式
     */
    SECTOR_SCAN(4, "扇形分时周扫模式"),
    /**
     * 5为连续周扫检测模式/自搜索模式
     */
    CONTINUOUS_SCAN(5, "自搜索模式"),
    /**
     * 6为联动模式
     */
    LINKAGE(6, "联动模式"),
    /**
     * 7为跟踪模式
     */
    TRACK(7, "跟踪模式"),
    /**
     * 8为巡航模式
     */
    CRUISE(8, "巡航模式"),
    /**
     * 9为自动聚焦模式
     */
    AUTO_FOCUS(9, "自动聚焦模式"),
    /**
     * 10为摇杆模式
     */
    ROCK(10, "摇杆模式"),
    /**
     * 11为跟飞机模式/后台指引模式
     */
    TRACK_PLANE(11, "后台指引模式"),
    /**
     * 12为指引跟起飞模式
     */
    TRACK_PLANE_TAKE_OFF_MODE(12, "指引跟起飞模式"),
    /**
     * 13为指引跟降落模式
     */
    TRACK_PLANE_LANDING_MODE(13, "指引跟降落模式"),
    /**
     * 14为跑道探鸟模式
     */
    RUN_WAY_BIRD_MODE(14, "跑道探鸟模式"),
    /**
     * 15为跑道探鸟模式
     */
    FLOATER_TRACK_MODE(15, "空飘物模式");

    public final int code;

    public final String desc;

    DevDetectRunModeEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取运行模式名称
     *
     */
    public static String getNameByCode(int code) {
        for (DevDetectRunModeEnums mode : values()) {
            if (mode.code == code) {
                return mode.desc;
            }
        }
        return "未知模式";
    }
}