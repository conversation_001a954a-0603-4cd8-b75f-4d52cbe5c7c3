package com.allin.silas.dev.detect.adapter.vo;


import lombok.Data;
import java.time.LocalDateTime;

/**
 * 
 *探测设备状态信息Vo
 */
@Data
public class DevDetectStatusVo {




    /**
     * 探测设备
     */
    private String devNum;

    /**
     * 探测设备名称
     */
    private String devName;

    /**
     * 设备状态：0：离线；1：在线；2：异常
     */
    private Integer devStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 设备运行状态详情
     */
    private String devRunStatus;


}