package com.allin.silas.dev.detect.constant;

/**
 * 继电器开关常量
 *
 * <AUTHOR>
 * @date 2025/5/8
 */
public interface RelayConstants {

    /**
     * 网络继电器常开
     */
    String TCPKPC1_OPEN = "AT+STACH1=1\r\n";

    /**
     * 网络继电器常关
     */
    String TCPKPC1_OFF = "AT+STACH1=0\r\n";

    /**
     * 设置网络继电器状态上传
     * 所有继电器通道状态上传间隔10秒
     */
    String TCPKPC1_STATE = "AT+DOUPLOAD0=1,100\r\n";

    /**
     * 数据尾
     */
    String END_LINE = "\r\n";

    /**
     * 数据尾
     */
    String DEV_NUM = "relay_";

    /**
     * 网络继电器状态
     */
    String STATE = "+STACH1:";


}
