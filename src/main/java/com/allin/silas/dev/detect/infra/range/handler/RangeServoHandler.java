package com.allin.silas.dev.detect.infra.range.handler;


import cn.hutool.core.util.ObjectUtil;
import com.allin.silas.common.enums.WsMessageType;
import com.allin.silas.dev.detect.adapter.dto.DevDetectStatusPayloadDto;
import com.allin.silas.dev.detect.app.entity.DevDetectInfo;
import com.allin.silas.dev.detect.app.manager.DevDetectInfoManager;
import com.allin.silas.dev.detect.client.event.DevDetectStatusEvent;
import com.allin.silas.dev.detect.infra.range.dto.TcpDevServoDto;
import com.allin.silas.dev.detect.infra.range.dto.TcpReceiveDataDto;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.stereotype.Component;

/**
 * 低空围栏伺服启停数据处理
 * <AUTHOR>
 * @since 2025/7/12
 */
@Component
@Slf4j
public class RangeServoHandler implements RangeDataHandler {

    private final DevDetectInfoManager devDetectInfoManager;
    private final ApplicationContext applicationContext;


    public RangeServoHandler(DevDetectInfoManager devDetectInfoManager, ApplicationContext applicationContext) {
        this.devDetectInfoManager = devDetectInfoManager;
        this.applicationContext = applicationContext;
    }

    @ServiceActivator(inputChannel = "rangeDevServoChannel")
    @Override
    public void dealData(TcpReceiveDataDto payload) {
        String devNum = payload.getDevNum();
        ByteBuf in =payload.getData();
        //当前分割位
        int currentPos = in.readUnsignedByte();
        //当前方位角
        float azimuth = in.readFloatLE();
        //当前俯仰角
        float pitch = in.readFloatLE();
        //当前分割位启停信息
        int state = in.readUnsignedByte();
        TcpDevServoDto dto = new TcpDevServoDto();
        dto.setDevNum(devNum);
        dto.setState(state);
        dto.setPitch(pitch);
        dto.setAzimuth(azimuth);
        dto.setCurrentPosition(currentPos);
        //获取projectId推送到前端界面
        DevDetectInfo devDetectInfo = devDetectInfoManager.getInfoFromCache(devNum);
        if(ObjectUtil.isNotEmpty(devDetectInfo)){
            dto.setProjectId(devDetectInfo.getProjectId());
            dto.setDevName(devDetectInfo.getDevName());
        }
        DevDetectStatusPayloadDto payloadDto = new DevDetectStatusPayloadDto();
        payloadDto.setDevNum(payload.getDevNum());
        payloadDto.setDevName(dto.getDevName());
        payloadDto.setProjectId(dto.getProjectId());
        payloadDto.setMessageType(WsMessageType.DEV_DETECT_SERVO.getCode());
        payloadDto.setDevServoDto(dto);
        //推送到前端web界面
        applicationContext.publishEvent(new DevDetectStatusEvent(this,payloadDto));
    }
}
