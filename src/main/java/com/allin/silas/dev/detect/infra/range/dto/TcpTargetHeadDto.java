package com.allin.silas.dev.detect.infra.range.dto;

import lombok.Data;

/**
 * TCP目标数据头
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
public class TcpTargetHeadDto {

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 后端生成的批次号
     */
    private String backendBatchNumber;

    /**
     * 当前批次号
     */
    private Integer splitBit;

    /**
     * 全景图编号
     */
    private Long panoramaId;

    /**
     * 全景图x坐标
     */
    private Double panoAzimuth;

    /**
     * 全景图y坐标
     */
    private Double panoPitch;

    /**
     * 目标数量
     */
    private Long targetNum;

}
