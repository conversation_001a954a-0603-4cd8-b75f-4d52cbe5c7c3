package com.allin.silas.dev.detect.app.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.allin.silas.dev.detect.adapter.dto.AddDevDetectInfoDto;
import com.allin.silas.dev.detect.adapter.dto.EditDevDetectInfoDto;
import com.allin.silas.dev.detect.app.entity.DevDetectCameraExt;
import com.allin.silas.dev.detect.app.entity.DevDetectInfo;
import com.allin.silas.dev.detect.app.entity.DevDetectRadarExt;
import com.allin.silas.dev.detect.app.manager.DevRelayManager;
import com.allin.silas.dev.detect.app.service.DevDetectInfoCommandService;
import com.allin.silas.dev.detect.infra.repository.DevDetectCameraExtMapper;
import com.allin.silas.dev.detect.infra.repository.DevDetectInfoMapper;
import com.allin.silas.dev.detect.infra.repository.DevDetectRadarExtMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DevDetectInfoCommandServiceImpl implements DevDetectInfoCommandService {

    private final DevDetectInfoMapper devDetectInfoMapper;

    private final DevDetectCameraExtMapper devDetectCameraExtMapper;

    private final DevDetectRadarExtMapper devDetectRadarExtMapper;

    private final DevRelayManager devRelayManger;


    public DevDetectInfoCommandServiceImpl(DevDetectInfoMapper devDetectInfoMapper,
                                           DevDetectCameraExtMapper devDetectCameraExtMapper,
                                           DevDetectRadarExtMapper devDetectRadarExtMapper,
                                           DevRelayManager devRelayManger) {
        this.devDetectInfoMapper = devDetectInfoMapper;
        this.devDetectCameraExtMapper = devDetectCameraExtMapper;
        this.devDetectRadarExtMapper = devDetectRadarExtMapper;
        this.devRelayManger = devRelayManger;
    }

    /**
     * 新增设备设备信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addDevInfo(AddDevDetectInfoDto dto) {
        DevDetectInfo info = new DevDetectInfo();
        BeanUtil.copyProperties(dto, info);
        int detectNum = devDetectInfoMapper.insert(info);
        DevDetectCameraExt devDetectCameraExt = dto.getDevDetectCameraExt();
        if (ObjectUtil.isNotEmpty(devDetectCameraExt)) {
            devDetectCameraExt.setDevNum(info.getDevNum());
            devDetectCameraExtMapper.insert(devDetectCameraExt);
        }
        DevDetectRadarExt devDetectRadarExt = dto.getDevDetectRadarExt();
        if (ObjectUtil.isNotEmpty(devDetectRadarExt)) {
            devDetectRadarExt.setDevNum(info.getDevNum());
            devDetectRadarExtMapper.insert(devDetectRadarExt);
        }
        return detectNum > 0;
    }


    /**
     * 删除探测设备信息
     */
    @Override
    public boolean deleteById(String id) {
        return devDetectInfoMapper.deleteById(id) > 0;
    }

    /**
     * 更新探测设备信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateDevInfo(EditDevDetectInfoDto dto) {
        DevDetectInfo info = new DevDetectInfo();
        DevDetectCameraExt cameraExt = dto.getDevDetectCameraExt();
        if (ObjectUtil.isNotEmpty(cameraExt)) {
            devDetectCameraExtMapper.updateById(cameraExt);
        }
        DevDetectRadarExt radarExt = dto.getDevDetectRadarExt();
        if (ObjectUtil.isNotEmpty(radarExt)) {
            devDetectRadarExtMapper.updateById(radarExt);
        }
        BeanUtil.copyProperties(dto, info);
        return devDetectInfoMapper.updateById(info) > 0;
    }

    /**
     * 继电器开关控制
     */
    @Override
    public boolean switchRelay(String devNum, int status) {
        boolean result = devRelayManger.switchRelay(devNum, status);
        if (!result) {
            return false;
        }
        // 控制成功后更新继电器状态
        return devDetectInfoMapper.update(
                Wrappers.<DevDetectInfo>lambdaUpdate()
                        .set(DevDetectInfo::getSwitchStatus, status)
                        .eq(DevDetectInfo::getDevNum, devNum)
        ) > 0;
    }


}




