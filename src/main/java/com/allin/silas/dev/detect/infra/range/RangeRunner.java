package com.allin.silas.dev.detect.infra.range;

import com.allin.silas.dev.detect.constant.DevConstants;
import com.allin.silas.dev.detect.infra.range.decoder.RangeMessageDecoder;
import com.allin.view.tcp.server.TcpServerV2;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.messaging.MessageChannel;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 低空围栏设备TCP服务端
 *
 * <AUTHOR>
 * @since 2025/7/12
 */
@Component
@Slf4j
public class RangeRunner implements CommandLineRunner {



    private final MessageChannel rangeInputChannel;

    private final RangeHandlerAdapter rangeHandlerAdapter;

    public RangeRunner(MessageChannel rangeInputChannel,
                       RangeHandlerAdapter rangeHandlerAdapter) {
        this.rangeInputChannel = rangeInputChannel;
        this.rangeHandlerAdapter = rangeHandlerAdapter;
    }

    @Override
    @Async
    public void run(String... args) {
        TcpServerV2.bootstrap(DevConstants.rangePort, 2, new ChannelInitializer<>() {
            @Override
            protected void initChannel(Channel channel) {
                // 注册连接生命周期 handler
                channel.pipeline()
                        .addLast(rangeHandlerAdapter)
                        // 粘包处理、协议解析
                        .addLast(new RangeMessageDecoder(rangeInputChannel));
            }
        });

    }
}
