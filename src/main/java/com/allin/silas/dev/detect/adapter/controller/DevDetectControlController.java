package com.allin.silas.dev.detect.adapter.controller;

import com.allin.silas.dev.detect.adapter.dto.DevDetectControlDto;
import com.allin.silas.dev.detect.adapter.vo.DevDetectModeControlVo;
import com.allin.silas.dev.detect.app.service.DevDetectControlCommandService;
import com.allin.view.base.domain.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 探测设备控制管理
 * <AUTHOR>
 * @since 2025/7/28
 */
@Validated
@RestController
@RequestMapping("/dev/dev_detect_control")
public class DevDetectControlController {

    private final DevDetectControlCommandService devDetectControlService;

    public DevDetectControlController(DevDetectControlCommandService devDetectControlService) {
        this.devDetectControlService = devDetectControlService;
    }


    /**
     * 探测设备控制
     */
    @PostMapping("/dev_control")
    public Result<DevDetectModeControlVo> control(@Validated @RequestBody DevDetectControlDto devControl) {
        return Result.ok(devDetectControlService.control(devControl));
    }





}
