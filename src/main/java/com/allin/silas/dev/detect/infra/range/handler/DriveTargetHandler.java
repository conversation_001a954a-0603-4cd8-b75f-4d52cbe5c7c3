package com.allin.silas.dev.detect.infra.range.handler;

import com.allin.silas.common.util.tcp.DevByteBufUtils;
import com.allin.silas.dev.detect.app.entity.DevDetectInfo;
import com.allin.silas.dev.detect.app.manager.DevDetectInfoManager;
import com.allin.silas.dev.detect.infra.range.dto.TcpReceiveDataDto;
import com.allin.silas.dev.detect.infra.range.dto.TcpTargetHeadDto;
import com.allin.silas.dev.utils.VisualTargetPositionUtils;
import com.allin.silas.map.client.MapRunWayFacade;
import com.allin.silas.visual.client.VisualTargetFacade;
import com.allin.silas.visual.client.dto.VisualTargetReceiveDto;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 探驱设备目标信息处理
 *
 * <AUTHOR>
 * @since 2025/7/12
 */
@Component
@Slf4j
public class DriveTargetHandler implements RangeDataHandler {

    private final DevDetectInfoManager devDetectInfoManager;

    private final VisualTargetFacade visualTargetFacade;

    private final MapRunWayFacade mapRunWayFacade;

    public DriveTargetHandler(DevDetectInfoManager devDetectInfoManager, VisualTargetFacade visualTargetFacade, MapRunWayFacade mapRunWayFacade) {
        this.devDetectInfoManager = devDetectInfoManager;
        this.visualTargetFacade = visualTargetFacade;
        this.mapRunWayFacade = mapRunWayFacade;
    }

    /**
     * 解析探驱设备目标数据
     */
    @ServiceActivator(inputChannel = "driveTargetChannel")
    @Override
    public void dealData(TcpReceiveDataDto payload) {
        String devNum = payload.getDevNum();
        // 解析data的数据
        ByteBuf in = payload.getData();
        // 批次
        in.readUnsignedByte();
        // 当前分割位
        int splitBit = in.readUnsignedByte();
        // 图像帧序号
        long panoramaId = in.readLongLE();
        // 系统时间
        in.readLongLE();
        // 图像方位角
        float azimuth = in.readFloatLE();
        // 图像俯仰
        float pitch = in.readFloatLE();
        // 目标数
        long targetNum = in.readLongLE();
        TcpTargetHeadDto targetHead = new TcpTargetHeadDto();
        targetHead.setPanoramaId(panoramaId);
        targetHead.setDevNum(devNum);
        targetHead.setSplitBit(splitBit);
        targetHead.setPanoAzimuth((double) azimuth);
        targetHead.setPanoPitch((double) pitch);
        targetHead.setTargetNum(targetNum);
        visualTargetFacade.saveVisualTarget(readTargetData(in, targetHead));
    }


    public List<VisualTargetReceiveDto> readTargetData(ByteBuf byteBuf, TcpTargetHeadDto targetHead) {
        List<VisualTargetReceiveDto> targets = new ArrayList<>();
        String devNum = targetHead.getDevNum();

        for (int i = 0; i < targetHead.getTargetNum(); i++) {
            VisualTargetReceiveDto targetData = new VisualTargetReceiveDto();
            targetData.setDevNum(devNum);
            // 是否为面目标
            int isArea = byteBuf.readUnsignedByte();
            long batchNum = byteBuf.readLongLE();
            // 左目约束批次号
            byteBuf.readLongLE();
            long matrixWidth = byteBuf.readUnsignedIntLE();
            long matrixHeight = byteBuf.readUnsignedIntLE();
            long area = byteBuf.readUnsignedIntLE();
            long type = byteBuf.readUnsignedIntLE();
            float rate = byteBuf.readFloatLE();
            // 目标聚类数量
            int birdNum = byteBuf.readIntLE();
            // 目标飞行方向角度
            float angle = byteBuf.readFloatLE();
            float speed = byteBuf.readFloatLE();
            float wingSpan = byteBuf.readFloatLE();
            long distance = byteBuf.readUnsignedIntLE();
            long height = byteBuf.readUnsignedIntLE();
            // 帧数
            long relatedFrame = byteBuf.readUnsignedIntLE();
            long imgLen = byteBuf.readUnsignedIntLE();
            String xStr = DevByteBufUtils.parseRelatedFrameData(byteBuf, relatedFrame, 0);
            String yStr = DevByteBufUtils.parseRelatedFrameData(byteBuf, relatedFrame, 0);
            String azimuthStr = DevByteBufUtils.parseRelatedFrameData(byteBuf, relatedFrame, 1);
            String pitchStr = DevByteBufUtils.parseRelatedFrameData(byteBuf, relatedFrame, 1);
            // 解析图片数据
            byte[] imgData = new byte[(int) imgLen];
            byteBuf.readBytes(imgData);
            targetData.setImgData(imgData);
            targetData.setArea(Float.parseFloat(area + ""));
            targetData.setAzimuth(VisualTargetPositionUtils.parseAzimuthOrPitch(azimuthStr));
            targetData.setPitch(VisualTargetPositionUtils.parseAzimuthOrPitch(pitchStr));
            targetData.setPanoX((double) VisualTargetPositionUtils.parseAzimuthOrPitch(xStr));
            targetData.setPanoY((double) VisualTargetPositionUtils.parseAzimuthOrPitch(yStr));
            targetData.setDistance(Float.parseFloat(distance + ""));
            targetData.setHeight(Float.parseFloat(height + ""));
            // 保留2位小数处理
            targetData.setSpeed(VisualTargetPositionUtils.roundFloat(speed, 2));
            targetData.setWingSpan(wingSpan);
            targetData.setFlightDirection(angle);
            targetData.setTargetCount(birdNum);
            targetData.setDetectType(type);
            targetData.setSplitBit(targetHead.getSplitBit());
            targetData.setAzimuthTrajectory(azimuthStr);
            targetData.setPitchTrajectory(pitchStr);
            targetData.setFrontendBatchNumber(String.valueOf(batchNum));
            targetData.setBackendBatchNumber(String.valueOf(batchNum));
            targetData.setTargetSize(isArea);
            targetData.setConfidenceLevel(rate);
            targetData.setImgWidth((int) matrixWidth);
            targetData.setImgHeight((int) matrixHeight);
            targetData.setPanoramaId(targetHead.getPanoramaId() + "");
            targetData.setTrajectoryX(xStr);
            targetData.setTrajectoryY(yStr);
            targets.add(targetData);
        }

        DevDetectInfo devDetectInfo = devDetectInfoManager.getInfoFromCache(devNum);
        double[] devPosition = new double[2];

        if (devDetectInfo == null) {
            log.error("设备信息不存在：{}", devNum);
            devPosition[0] = 0;
            devPosition[1] = 0;
        } else {
            devPosition[0] = Double.parseDouble(devDetectInfo.getLongitude());
            devPosition[1] = Double.parseDouble(devDetectInfo.getLatitude());
        }

        for (VisualTargetReceiveDto targetData : targets) {
            targetData.setProjectId(devDetectInfo != null ? devDetectInfo.getProjectId() : "-1");
            targetData.setDevType(devDetectInfo != null ? devDetectInfo.getDevType() : "-1");

            if (devDetectInfo != null) {
                // 根据设备经纬度获取目标经纬度
                double[] targetPosition = VisualTargetPositionUtils.getTargetPosition(devPosition,
                        targetData.getDistance(),
                        targetData.getAzimuth(),
                        devDetectInfo.getDevNorthOffset());
                targetData.setLongitude(targetPosition[0]);
                targetData.setLatitude(targetPosition[1]);
                // 计算目标到跑道中心线距离
                targetData.setRunwayDistance(mapRunWayFacade.calculateDistance(
                        targetPosition[0], targetPosition[1], devDetectInfo.getRunwayId()));
                // 计算目标到跑道中心线距离
                targetData.setRunwayCenterDistance(mapRunWayFacade.calculateCenterDistance(
                        targetPosition[0], targetPosition[1], devDetectInfo.getRunwayId()));
            } else {
                targetData.setLongitude(0D);
                targetData.setLatitude(0D);
            }
        }

        return targets;
    }


}
