package com.allin.silas.dev.detect.adapter.dto;


import com.allin.silas.dev.detect.infra.range.dto.TcpDevServoDto;
import lombok.Data;


/**
 * 探测设备状态信息
 * <AUTHOR>
 * @since 2025/7/16
 */

@Data
public class DevDetectStatusPayloadDto {

    /**
     * 设备编号
     */
    private String devNum;
    /**
     * 设备名称
     */
    private String devName;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 状态类型
     */
    private String messageType;

   /**
     * 设备伺服状态信息
     */
    private TcpDevServoDto devServoDto;


    /**
     * 设备状态信息
     */
    private DevDetectStatusDto devDetectStatusDto;
}
