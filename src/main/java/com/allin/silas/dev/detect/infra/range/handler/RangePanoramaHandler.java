package com.allin.silas.dev.detect.infra.range.handler;


import com.allin.silas.dev.detect.client.DevDetectPanoramaFacade;
import com.allin.silas.dev.detect.infra.range.dto.TcpPanoramaDto;
import com.allin.silas.dev.detect.infra.range.dto.TcpReceiveDataDto;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.stereotype.Component;


/**
 * 低空围栏全景图信息处理
 * <AUTHOR>
 * @since 2025/7/12
 */
@Component
@Slf4j
public class RangePanoramaHandler implements RangeDataHandler {

    private final DevDetectPanoramaFacade devDetectPanoramaFacade;

    public RangePanoramaHandler(DevDetectPanoramaFacade devDetectPanoramaFacade) {
        this.devDetectPanoramaFacade = devDetectPanoramaFacade;
    }

    @ServiceActivator(inputChannel = "rangePanoramaChannel")
    @Override
    public void dealData(TcpReceiveDataDto payload) {
        String devNum = payload.getDevNum();
        ByteBuf in =payload.getData();
        //相机编号
        int cameraNum = in.readUnsignedByte();
        int curCamera = in.readUnsignedByte();
        in.readLongLE();
        //系统时间
        in.readLongLE();
        float azimuth = in.readFloatLE();
        float pitch = in.readFloatLE();
        long imgLen = in.readLongLE();
        //存储全景图片信息
        byte[] imgData = new byte[(int)imgLen];
        in.readBytes(imgData);
        TcpPanoramaDto dto =TcpPanoramaDto.builder()
                .azimuth(azimuth)
                .pitch(pitch)
                .channelType(cameraNum+"")
                .currentPosition(curCamera)
                .imgData(imgData)
                .devNum(devNum)
                .build();
        //存储全景图信息
        devDetectPanoramaFacade.savDevPanorama(dto);
    }
}
