package com.allin.silas.dev.detect.client.annotation;

import com.allin.silas.dev.detect.client.annotation.serializer.DevNumAsNullSerializer;
import com.allin.silas.dev.detect.client.annotation.serializer.DevNumToNameSerializer;
import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 设备编号转设备名称
 *
 * <AUTHOR>
 * @since 2025/7/3
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonSerialize(nullsUsing = DevNumAsNullSerializer.class, using = DevNumToNameSerializer.class)
public @interface DevNumToName {

    /**
     * 如果配置了key就会将转换后的值放入新的key中，被标记的字段的值依然不变
     *
     * @see com.allin.view.config.serialize.annotation.ApiFoxNoIgnore
     */
    String key() default "";
}
