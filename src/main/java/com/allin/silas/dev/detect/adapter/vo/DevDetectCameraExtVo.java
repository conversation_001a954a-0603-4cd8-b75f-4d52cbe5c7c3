package com.allin.silas.dev.detect.adapter.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 探测设备光电信息
 * @TableName dev_detect_camera_ext
 */
@Data
public class DevDetectCameraExtVo {


    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备分割位数量
     */
    private Integer devSeparatorsNums;

    /**
     * 设备俯仰角
     */
    private Float devPitch;

    /**
     * 设备俯仰分隔位数量
     */
    private Integer devPitchSeparatorsNums;

    /**
     * 设备水平视场角
     */
    private Float devHorizontalAngle;

    /**
     * 设备俯仰视场角
     */
    private Float devPitchAngle;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;


}