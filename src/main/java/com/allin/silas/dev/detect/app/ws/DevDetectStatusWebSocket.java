package com.allin.silas.dev.detect.app.ws;

import com.allin.silas.dev.detect.adapter.dto.DevDetectStatusDto;
import com.allin.silas.dev.detect.client.event.DevDetectStatusEvent;
import com.allin.silas.dev.detect.infra.range.dto.TcpDevServoDto;
import com.allin.view.ws.entity.WsMessage;
import com.allin.view.ws.handler.CustomParamWebSocketHandler;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 低空围栏设备状态信息推送前端
 * <AUTHOR>
 * @since 2025/7/17
 */
@Component
public class DevDetectStatusWebSocket {


    private final CustomParamWebSocketHandler webSocketHandler;

    public DevDetectStatusWebSocket(CustomParamWebSocketHandler webSocketHandler) {
        this.webSocketHandler = webSocketHandler;
    }



    /**
     * 发送设备心跳状态到前端
     *
     */
    @Async
    @EventListener
    public void sendDevStatusToWeb(DevDetectStatusEvent event) {
        String messageType =event.getPayload().getMessageType();
        switch (messageType){
            case "devDetectStatus":
                DevDetectStatusDto devDetectStatusDto = event.getPayload().getDevDetectStatusDto();
                webSocketHandler.getSender().sendToParam(event.getPayload().getProjectId(),
                        WsMessage.of(messageType, devDetectStatusDto));
                break;
            case "servoStatus":
                TcpDevServoDto devServoDto = event.getPayload().getDevServoDto();
                webSocketHandler.getSender().sendToParam(event.getPayload().getProjectId(),
                        WsMessage.of(messageType, devServoDto));
                break;
            default:
                throw new IllegalStateException("未知消息类型: " + messageType);
        }
    }
}

