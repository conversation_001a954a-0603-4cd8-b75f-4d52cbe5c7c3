package com.allin.silas.dev.detect.adapter.dto;


import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 探测设备扩展信息DTO
 * @TableName dev_detect_camera_ext
 */

@Data
public class DevDetectCameraExtDto {


    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    private String devNum;

    /**
     * 设备分割位数量
     */
    private Integer devSeparatorsNums;

    /**
     * 设备俯仰角
     */
    private Float devPitch;

    /**
     * 设备俯仰分隔位数量
     */
    private Integer devPitchSeparatorsNums;

    /**
     * 设备水平视场角
     */
    private Float devHorizontalAngle;

    /**
     * 设备俯仰视场角
     */
    private Float devPitchAngle;



}