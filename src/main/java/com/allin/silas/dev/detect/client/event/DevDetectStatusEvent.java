package com.allin.silas.dev.detect.client.event;

import com.allin.silas.dev.detect.adapter.dto.DevDetectStatusPayloadDto;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 探测设备状态信息事件
 *
 * <AUTHOR>
 * @since 2025/7/17
 */
@Getter
public class DevDetectStatusEvent extends ApplicationEvent {

    private final transient DevDetectStatusPayloadDto payload;

    public DevDetectStatusEvent(Object source, DevDetectStatusPayloadDto payload) {
        super(source);
        this.payload = payload;
    }

}
