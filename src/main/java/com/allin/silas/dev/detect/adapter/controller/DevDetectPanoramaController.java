package com.allin.silas.dev.detect.adapter.controller;

import com.allin.silas.dev.detect.adapter.dto.DevDetectPanoramaDto;
import com.allin.silas.dev.detect.adapter.dto.EditDevDetectPanoramaDto;
import com.allin.silas.dev.detect.adapter.query.DevDetectPanoramaQuery;
import com.allin.silas.dev.detect.adapter.vo.DevDetectPanoramaVo;
import com.allin.silas.dev.detect.app.service.DevDetectPanoramaCommandService;
import com.allin.silas.dev.detect.app.service.DevDetectPanoramaQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 探测设备全景图信息
 *
 * <AUTHOR>
 * @since 2025/4/28
 */
@Validated
@RestController
@RequestMapping("/dev/dev_detect_panorama")
class DevDetectPanoramaController {


    private final DevDetectPanoramaCommandService devDetectPanoramaCommandService;

    private final DevDetectPanoramaQueryService devDetectPanoramaQueryService;

    DevDetectPanoramaController(DevDetectPanoramaCommandService devDetectPanoramaCommandService, DevDetectPanoramaQueryService devDetectPanoramaQueryService) {
        this.devDetectPanoramaCommandService = devDetectPanoramaCommandService;
        this.devDetectPanoramaQueryService = devDetectPanoramaQueryService;
    }

    /**
     * 新增探测设备全景图信息
     *
     */
    @PostMapping("/add")
    public Result<Void> add(@Validated @RequestBody DevDetectPanoramaDto dto){
        dto.setProjectId(SecurityContextHolder.getProjectId());
        return devDetectPanoramaCommandService.addPanorama(dto)? Result.ok() : Result.fail();
    }


    /**
     * 修改探测设备全景图信息
     *
     */
    @PutMapping("/update")
    public Result<Void> update(@Validated @RequestBody EditDevDetectPanoramaDto dto){
        return devDetectPanoramaCommandService.updatePanorama(dto)? Result.ok() : Result.fail();
    }


    /**
     * 删除探测设备全景图信息
     *
     */
    @DeleteMapping("/delete/{id}")
    public Result<Void> delete(@PathVariable("id")String id){
        return devDetectPanoramaCommandService.deleteById(id)? Result.ok() : Result.fail();
    }


    /**
     * 查询探测设备全景图信息列表
     */
    @GetMapping("/find_list")
    public Result<List<DevDetectPanoramaVo>> findList(DevDetectPanoramaQuery param){
        return Result.ok(devDetectPanoramaQueryService.findList(param));
    }







}
