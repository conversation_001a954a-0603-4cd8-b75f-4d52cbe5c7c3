package com.allin.silas.dev.detect.app.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 雷达扩展信息表
 * @TableName dev_detect_radar_ext
 */
@TableName(value ="dev_detect_radar_ext")
@Data
public class DevDetectRadarExt {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备分割位数量
     */
    private Integer devSeparatorsNums;

    /**
     * 设备俯仰角
     */
    private Float devPitch;

    /**
     * 设备俯仰分隔位数量
     */
    private Integer devPitchSeparatorsNums;

    /**
     * 
     */
    private Float devHorizontalAngle;

    /**
     * 
     */
    private Float devPitchAngle;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;
}