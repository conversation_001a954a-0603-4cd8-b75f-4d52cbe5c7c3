package com.allin.silas.dev.detect.infra.range.handler;


import com.allin.silas.dev.detect.infra.range.dto.TcpReceiveDataDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.stereotype.Component;

/**
 * 低空围栏单帧图图信息处理
 * <AUTHOR>
 * @since 2025/7/12
 */
@Component
@Slf4j
public class RangeSingleFrameHandler implements RangeDataHandler {

    @ServiceActivator(inputChannel = "rangeSignalFrameChannel")
    @Override
    public void dealData(TcpReceiveDataDto payload) {
        log.info("低空围栏设备单帧图信息：{}", payload);

    }
}
