package com.allin.silas.dev.detect.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 探测设备全景图信息
 * dev_detect_panorama
 */
@Data
public class DevDetectPanoramaDto {


    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    private String devNum;

    /**
     * 通道名称
     */
    @NotBlank(message = "通道名称不能为空")
    private String channelName;

    /**
     * 通道类型
     */
    @NotBlank(message = "通道类型不能为空")
    private String channelType;

    /**
     * 全景图偏移角度
     */
    @NotNull(message = "全景图偏移角度不能为空")
    private Float panoramaOffset;

    /**
     * 全景图图片ID
     */
    private String panoramaImg;




}