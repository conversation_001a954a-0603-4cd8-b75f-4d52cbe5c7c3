package com.allin.silas.dev.detect.app.enums;


import com.allin.view.base.enums.base.IEnums;
import jakarta.annotation.Nullable;

import java.util.List;

/**
 * 探测设备类型枚举
 *
 * <AUTHOR>
 * @date 2025/5/02 14:01
 */
public enum DevDetectTypeEnums implements IEnums {

    CAMERA_RANGE_VIS("低空围栏-白光版", "camera_range_vis"),
    CAMERA_RANGE_INF("低空围栏-红外版", "camera_range_inf"),
    CAMERA_DRIVE("探驱一体设备", "camera_drive"),
    CAMERA_BOUNDARY("围界设备", "camera_boundary"),

    // 搜跟设备
    CAMERA_SEARCH_405("光电搜跟-405", "camera_search_405"),
    CAMERA_SEARCH_380("光电搜跟-380", "camera_search_380"),
    CAMERA_SEARCH_340("光电搜跟-340", "camera_search_340"),
    CAMERA_SEARCH_HGD_VIS("光电搜跟-哈工大白光版", "camera_search_hgd_vis"),
    CAMERA_SEARCH_HGD_INF("光电搜跟-哈工大红外版", "camera_search_hgd_inf"),

    // 雷达
    RADAR_JRX("吉瑞祥雷达", "radar_jrx"),
    RADAR_BW("宝威雷达", "radar_bw"),
    RADAR_RASOC("雷索克雷达", "radar_rasoc");

    private final String devTypeName;

    private final String devTypeCode;

    DevDetectTypeEnums(String devTypeName, String devTypeCode) {
        this.devTypeName = devTypeName;
        this.devTypeCode = devTypeCode;
    }

    /**
     * 根据code获取枚举
     *
     * @param devTypeCode
     * @see IEnums#tryFindByCode(Class, Object)
     */
    @Nullable
    public static DevDetectTypeEnums fromCode(String devTypeCode) {
        return IEnums.tryFindByCode(DevDetectTypeEnums.class, devTypeCode).orElse(null);
    }

    /**
     * 搜跟设备列表
     */
    public static List<String> listSearch() {
        return List.of(CAMERA_SEARCH_405.getCode(),
                CAMERA_SEARCH_380.getCode(),
                CAMERA_SEARCH_340.getCode(),
                CAMERA_SEARCH_HGD_VIS.getCode(),
                CAMERA_SEARCH_HGD_INF.getCode());
    }

    /**
     * 搜跟设备列表
     */
    public static List<String> listCamera() {
        return List.of(CAMERA_RANGE_VIS.getCode(),
                CAMERA_RANGE_INF.getCode(),
                CAMERA_DRIVE.getCode(),
                CAMERA_BOUNDARY.getCode());
    }

    @Override
    public String getCode() {
        return devTypeCode;
    }

    public String getDevTypeName() {
        return devTypeName;
    }

    public String getDevTypeCode() {
        return devTypeCode;
    }
}
