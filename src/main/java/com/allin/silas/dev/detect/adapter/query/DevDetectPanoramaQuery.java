package com.allin.silas.dev.detect.adapter.query;

import lombok.Data;


/**
 *  探测设备全景图信息查询实体
 *  dev_detect_panorama
 */
@Data
public class DevDetectPanoramaQuery {
    /**
     * 主键ID（雪花ID）
     */
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 通道类型
     */
    private String channelType;


    /**
     * 是否删除：0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 是否默认：0-否，1-是
     */
    private Integer isDefault;
}