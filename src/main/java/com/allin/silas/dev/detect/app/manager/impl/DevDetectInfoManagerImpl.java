package com.allin.silas.dev.detect.app.manager.impl;

import com.allin.silas.dev.detect.app.entity.DevDetectInfo;
import com.allin.silas.dev.detect.app.manager.DevDetectInfoManager;
import com.allin.silas.dev.detect.infra.repository.DevDetectInfoMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/4/28
 */

@Service
public class DevDetectInfoManagerImpl implements DevDetectInfoManager {

    private final DevDetectInfoMapper devDetectInfoMapper;

    public DevDetectInfoManagerImpl(DevDetectInfoMapper devDetectInfoMapper) {
        this.devDetectInfoMapper = devDetectInfoMapper;
    }

    /**
     * 根据设备编号获取探测设备信息
     */
    @Cacheable(cacheNames = "devDetectInfo#60", key = "#devNum", unless = "#result == null")
    @Override
    public DevDetectInfo getInfoFromCache(String devNum) {
        LambdaQueryWrapper<DevDetectInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DevDetectInfo::getDevNum, devNum);
        return devDetectInfoMapper.selectOne(queryWrapper);
    }
}
