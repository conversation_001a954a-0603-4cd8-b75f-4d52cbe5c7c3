package com.allin.silas.dev.detect.infra.range.enums;


import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 前端发送数据指令类型枚举
 */
@Getter
public enum TcpMessageTypeEnums {

    // 心跳
    HEARTBEAT(41377, "rangeHeartBeatChannel"),

    // 低空围栏目标信息
    TARGET(129, "rangeTargetChannel"),

    // 探驱设备目标信息
    DRIVE(130, "driveTargetChannel"),

    // 伺服启停信息
    SERVO(449, "rangeDevServoChannel"),


    // 全景图数据
    PANORAMA(65, "rangePanoramaChannel"),



    //全景图灰度图数据
    PANORAMA_GRAY(66, "rangePanoramaChannel"),

    //单帧图数据
    SINGLE_FRAME(67, "rangeSingleFrameChannel"),


    // 未知数据
    UNKNOWN(-1, "unknownDataChannel"),

    ;

    private final Integer type;

    private final String channelName;



    TcpMessageTypeEnums(Integer type, String channelName) {
        this.type = type;
        this.channelName = channelName;
    }


    // 缓存 Map
    private static final Map<Integer, TcpMessageTypeEnums> TYPE_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(TcpMessageTypeEnums::getType, e -> e));

    /**
     * 根据指令类型获取对应通道名
     */
    public static String getChannelName(Integer type) {
        return TYPE_MAP.getOrDefault(type, UNKNOWN).getChannelName();
    }


}
