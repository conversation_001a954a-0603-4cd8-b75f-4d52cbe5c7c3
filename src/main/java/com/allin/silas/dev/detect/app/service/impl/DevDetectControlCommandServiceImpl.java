package com.allin.silas.dev.detect.app.service.impl;

import com.allin.silas.dev.detect.adapter.dto.DevDetectControlDto;
import com.allin.silas.dev.detect.adapter.vo.DevDetectModeControlVo;
import com.allin.silas.dev.detect.app.service.DevDetectControlCommandService;
import com.allin.silas.dev.detect.infra.range.control.RangeControlGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 探测设备控制实现
 * <AUTHOR>
 * @since 2025/7/28
 */
@Slf4j
@Service
public class DevDetectControlCommandServiceImpl implements DevDetectControlCommandService {

    private final RangeControlGateway rangeControlGateway;

    public DevDetectControlCommandServiceImpl(RangeControlGateway rangeControlGateway) {
        this.rangeControlGateway = rangeControlGateway;
    }


    /**
     * 探测设备控制
     */
    @Override
    public DevDetectModeControlVo control(DevDetectControlDto devControl) {
        return rangeControlGateway.control(devControl);
    }


}
