package com.allin.silas.dev.detect.infra.repository;


import com.allin.silas.dev.detect.adapter.query.DevDetectInfoQuery;
import com.allin.silas.dev.detect.adapter.vo.DevDetectInfoVo;
import com.allin.silas.dev.detect.app.entity.DevDetectInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DevDetectInfoMapper extends BaseMapper<DevDetectInfo> {

    /**
     * 分页查询
     */
    List<DevDetectInfoVo> findList(@Param("param") DevDetectInfoQuery param);
}




