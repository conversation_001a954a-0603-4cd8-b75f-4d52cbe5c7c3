package com.allin.silas.dev.detect.app.entity;


import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 探测设备状态信息
 * @TableName dev_detect_status
 */
@TableName(value ="dev_detect_status")
@Data
public class DevDetectStatus {
    /**
     * 主键ID（雪花ID）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 探测设备
     */
    private String devNum;

    /**
     * 设备状态：0：离线；1：在线；2：异常
     */
    private Integer devStatus;

    /**
     * 设备运行状态详情
     */
    private String devRunStatus;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
}