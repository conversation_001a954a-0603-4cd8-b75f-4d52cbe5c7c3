package com.allin.silas.dev.detect.app.manager.impl;

import com.allin.silas.dev.detect.app.manager.DevRelayManager;
import com.allin.silas.dev.detect.constant.RelayConstants;
import com.allin.silas.dev.detect.infra.relay.RelayRunner;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/5/8
 */
@Slf4j
@Service
public class DevRelayManagerImpl implements DevRelayManager {

    /**
     * 继电器开关控制
     */
    @Override
    public boolean switchRelay(String devNum, int status) {
        final Channel channel = RelayRunner.getChannels().get(devNum);
        if(channel != null && channel.isActive()){
            String message = RelayConstants.TCPKPC1_OFF;
            if (1 == status) {
                message = RelayConstants.TCPKPC1_OPEN;
            }
            channel.writeAndFlush(message);
            return true;
        }
        return false;
    }


}
