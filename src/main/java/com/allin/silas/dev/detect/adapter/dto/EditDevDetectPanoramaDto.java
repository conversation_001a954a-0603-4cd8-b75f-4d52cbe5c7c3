package com.allin.silas.dev.detect.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 修改探测设备全景图信息
 * dev_detect_panorama
 */
@Data
public class EditDevDetectPanoramaDto {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    private String devNum;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 通道类型
     */
    private String channelType;

    /**
     * 全景图偏移角度
     */
    private Float panoramaOffset;

    /**
     * 全景图图片ID
     */
    private String panoramaImg;




}