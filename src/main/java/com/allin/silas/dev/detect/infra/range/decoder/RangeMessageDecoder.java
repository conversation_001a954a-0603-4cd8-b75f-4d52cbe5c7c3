package com.allin.silas.dev.detect.infra.range.decoder;

import com.allin.silas.common.util.tcp.DevByteBufUtils;
import com.allin.silas.dev.detect.constant.TcpMessageConstants;
import com.allin.silas.dev.detect.infra.range.dto.TcpReceiveDataDto;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;

import java.net.InetSocketAddress;
import java.util.List;

/**
 * 低空围栏设备信息解码器
 *
 * <AUTHOR>
 * @since 2025/7/12
 */
@Slf4j
public class RangeMessageDecoder extends ByteToMessageDecoder {

    private final MessageChannel rangeInputChannel;

    public RangeMessageDecoder(MessageChannel rangeInputChannel) {
        this.rangeInputChannel = rangeInputChannel;
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) {
        in.markReaderIndex();
        TcpReceiveDataDto frame =null;
        try {
            frame = tryDecodeFrame(in,ctx);
            // 数据不足，跳出循环等待更多输入
            if (frame == null) {
                return;
            }
            boolean sent = rangeInputChannel.send(MessageBuilder.withPayload(frame).build());
            // 正常解析后推进 readerIndex
            if (!sent) {
                // 数据路由失败,释放数据
                frame.release();
                log.error("低空围栏数据路由失败，丢弃帧: {}", frame);
            }
        } catch (Exception e) {
            log.error("低空围栏数据解析异常，跳过一个字节继续：{}", e.getMessage(), e);
            in.resetReaderIndex();
            in.readByte();
            if (frame != null) {
                frame.release();
            }
        }

    }

    /**
     *消息数据解析校验
     */
    private TcpReceiveDataDto tryDecodeFrame(ByteBuf in,ChannelHandlerContext ctx) {
        InetSocketAddress socket = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = socket.getAddress().getHostAddress();
        int port = socket.getPort();
        TcpReceiveDataDto data = new TcpReceiveDataDto();
        data.setIp(ip);
        data.setPort(port);
        int index = in.readerIndex();

        // 数据长度不够，直接返回等待更多数据
        if (in.readableBytes() < TcpMessageConstants.RECEIVE_HEAD_LEN) {
            return null;
        }
        // 校验帧头
        String head = DevByteBufUtils.getHeadOrTail(in, index, 2);
        if (!TcpMessageConstants.HEAD_SIGN.equals(head)) {
            return null;
        }
        // 设备编号
        String devNum = DevByteBufUtils.getStringFromByteBuf(in, index + 2, 100).trim();
        // uuid
        String uuid = DevByteBufUtils.getStringFromByteBuf(in, index + 102, 100).trim();
        // 指令标识
        int type = in.getUnsignedShortLE(index + 202);
        // 数据包大小
        long dataLen = in.getLongLE(index + 204);
        // 数据校验和
        Short checkSum = in.getUnsignedByte(index + 212);
        // 校验帧尾
        String tail = DevByteBufUtils.getHeadOrTail(in, index + 213, 2);
        if (!TcpMessageConstants.TAIL_SIGN.equals(tail)) {
            return null;
        }
        // 如果数据长度异常直接重新丢弃重新解析
        if (dataLen >= TcpMessageConstants.TCP_MAX_LENGTH) {
            // 异常数据直接清空处理，重新读取
            log.error("数据解析异常，数据长度：{}", dataLen);
            // 跳过异常起始位置，防止丢弃后续粘包
            in.skipBytes(1);
            return null;
        }
        int actualFrameLength = TcpMessageConstants.RECEIVE_HEAD_LEN + (int) dataLen;
        // 读取数据长度字段
        if (in.readableBytes() < actualFrameLength) {
            return null;
        }
        // 数据校验完开始数据解析
        data.setMessageHead(head);
        data.setDataLength(dataLen);
        data.setCheckSum(checkSum);
        data.setDevNum(devNum);
        data.setUuid(uuid);
        data.setMessageEnd(tail);
        data.setType(type);
        // 读取有效负载数据：推荐使用 slice + retain（比 copy 更高效）
        ByteBuf dataByte = in.retainedSlice(index+TcpMessageConstants.RECEIVE_HEAD_LEN, (int)dataLen);
        data.setData(dataByte);
        in.readerIndex(index + actualFrameLength);
        return data;
    }

}
