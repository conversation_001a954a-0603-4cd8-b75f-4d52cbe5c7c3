package com.allin.silas.dev.detect.app.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 探测设备全景图信息
 * @TableName dev_detect_panorama
 */
@TableName(value ="dev_detect_panorama")
@Data
public class DevDetectPanorama {
    /**
     * 主键ID（雪花ID）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 通道类型
     */
    private String channelType;

    /**
     * 全景图偏移角度
     */
    private Float panoramaOffset;

    /**
     * 全景图图片ID
     */
    private String panoramaImg;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除：0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 是否默认：0-否，1-是
     */
    private Integer isDefault;
}