package com.allin.silas.dev.detect.constant;

/**
 * 设备常量类
 *
 * <AUTHOR>
 * @since 2025/3/24
 */
public class DevConstants {

    /**
     * 低空围栏设备TCP端口
     */
    public static final Integer rangePort =9002;

    /**
     * 继电器设备TCP端口
     */
    public static final Integer relayPort =6000;

    /**
     * 搜跟TCP端口
     */
    public static final Integer search340Port =9888;


    /**
     * 球机设备TCP端口
     */
    public static final Integer ballCameraPort = 9990;

    /**
     * 驱鸟设备端口
     */
    public static final Integer driveBirdPort = 8100;

    /**
     * 电磁炮驱鸟设备端口
     */
    public static final Integer shellDriveBirdPort = 8101;

    /**
     * 西藏驱鸟设备端口
     */
    public static final Integer xzDriveBirdPort = 12500;


    /**
     * 迪瑞泰克驱鸟设备端口
     */
    public static final Integer dRTKDriveBirdPort = 8102;

    /**
     * 海口驱鸟设备端口
     */
    public static final Integer hkDriveBirdPort = 8103;

    /**
     * 平板控制驱鸟设备端口
     */
    public static final Integer iPadPort = 8104;





}
