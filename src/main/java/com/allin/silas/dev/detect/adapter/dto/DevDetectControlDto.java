package com.allin.silas.dev.detect.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/7/28
 */
@Data
public class DevDetectControlDto {
    /**
     * 探测设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    String devNum;

    /**
     * 控制指令编码
     */
    @NotNull(message = "控制指令编码不能为空")
    Integer code;


    /**
     * 步进模式参数
     */
    private DevDetectStepParamDto devDetectStepParamDto;

    /**
     * 步进模式2参数
     */
    private DevDetectStep2ParamDto devDetectStep2ParamDto;


    /**
     *  摇杆模式参数
     */
    private DevDetectJoyStickParamDto devDetectControlJoyStickParamDto;


    /**
     * 指引模式参数
     */
    private DevDetectGuideParamDto devDetectControlGuideParamDto;

    /**
     * 巡航模式参数
     */
    private DevDetectCruiseParamDto devDetectCruiseParamDto;


    /**
     * 雷达模式参数
     */
    private DevDetectRadarParamDto devDetectRadarParamDto;


    /**
     *  探驱模式参数
     */
    private DevDetectDriveParamDto devDetectDriveParamDto;


    /**
     * 扇形分扫模式参数
     */
    private DevDetectSplitParamDto devDetectSplitParamDto;

    /**
     * 位置模式参数
     */
    private DevDetectPositionParamDto devDetectPositionParamDto;



}
