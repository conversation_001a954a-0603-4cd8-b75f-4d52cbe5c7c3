package com.allin.silas.dev.detect.app.manager;

import com.allin.silas.dev.detect.app.entity.DevDetectInfo;
import jakarta.annotation.Nullable;

/**
 * <AUTHOR>
 * @since 2025/4/28
 */
public interface DevDetectInfoManager {

    /**
     * 根据设备编号获取设备信息
     *
     * @param devNum 设备编号
     * @return 设备信息
     */
    @Nullable
    DevDetectInfo getInfoFromCache(String devNum);

    /**
     * 根据设备编号获取设备名称
     */
    @Nullable
    default String getDevNameFromCache(String devNum) {
        final DevDetectInfo devDetectInfo = getInfoFromCache(devNum);
        if (devDetectInfo == null) {
            return null;
        }
        return devDetectInfo.getDevName();
    }

}
