package com.allin.silas.dev.detect.infra.range.enums;


import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 后台发送数据指令类型枚举
 */
@Getter
public enum TcpDevControlEnums {


    // 空闲模式
    FREE(0, "freeModeChannel"),

    //全景拼图模式
    PANORAMA(1, "panoramaModeChannel"),

    //步进模式
    STEP(2, "stepModeChannel"),

    //查询步进模式
    QUERY_STEP(3, "queryStepModeChannel"),


    //设置上一次参数步进模式
    SET_LAST_STEP(6, "setLastStepModeChannel"),


    //设置凝视模式
    SET_GAZE(7, "setGazeModeChannel"),


    //设置凝视当前位置
    SET_GAZE_CURRENT_POSITION(9, "setGazeCurrentPositionModeChannel"),


    //设置凝视上一次参数模式
    SET_GAZE_LAST_PARAM(10, "setGazeLastParamModeChannel"),


    //设置步进模式2
    SET_STEP_2(11, "setStepMode2Channel"),

    //查询步进模式2
    QUERY_STEP_2(12, "queryStepMode2Channel"),

    //设置扇形分时周扫模式
    SET_SECTOR_SCAN(13, "setSectorScanModeChannel"),


    //查询扇形分时周扫模式
    QUERY_SECTOR_SCAN(14, "querySectorScanModeChannel"),

    //设置扇形周扫模式上一次参数
    SET_SECTOR_SCAN_LAST_PARAM(15, "setSectorScanLastParamModeChannel"),
    
    
    //雷达引导跟踪指定角度
    RADAR_GUIDE_TRACK_ANGLE(16, "radarGuideTrackAngleChannel"),

    //设置巡航模式
    SET_COURSE(17, "courseModeChannel"),

    //查询巡航模式参数
    QUERY_COURSE(18, "queryCourseModeChannel"),

    //设置续航模式上一次参数
    SET_COURSE_LAST_PARAM(19, "setCourseParamModeChannel"),



    //设置自动打击开关
    SET_AUTO_SHOOT(20, "setAutoShootModeChannel"),

    //打击设备控制
    DRIVE_SHOOT(21, "driveShootModeChannel"),

    //联动模式
    LINKAGE(22, "linkageModeChannel"),

    //设置联动模式
    SET_LINKAGE(23, "setLinkageModeChannel"),

    //定速转动模式
    SPEED_TURN(24, "speedTurnModeChannel"),

    //设置跑道探鸟模式
    SET_TRACK_BIRD(25, "setTrackBirdModeChannel"),

    //设置指引模式
    SET_GUIDE_MODE(26, "setGuideModeChannel"),

    //恢复工作模式
    RECOVER_WORK_MODE(27, "recoverWorkModeChannel"),

    //设置跟踪起飞1（北端起飞）
    SET_TRACK_TAKE_OFF_1(28, "setTrackTakeOff1ModeChannel"),

    //设置跟踪降落1（北端降落）
    SET_TRACK_LANDING_1(29, "setTrackLanding1ModeChannel"),

    //设置跟踪起飞2（南端起飞）
    SET_TRACK_TAKE_OFF_2(30, "setTrackTakeOff2ModeChannel"),

    //设置跟踪降落2（南端降落）
    SET_TRACK_LANDING_2(31, "setTrackLanding2ModeChannel"),

    //设置空飘物模式
    SET_FLOATER_MODE(32, "setFloaterModeChannel"),


    //设置全景灰度ROI
    SET_GRAY_ROI(65, "setGrayRoiModeChannel"),

    //查询全景灰度ROI
    QUERY_GRAY_ROI(66, "queryGrayRoiModeChannel"),

    //设置JPG自动打击灰度ROI
    SET_JPG_AUTO_SHOOT_GRAY_ROI(67, "setJpgAutoShootGrayRoiModeChannel"),

    //查询JPG自动打击灰度ROI
    QUERY_JPG_AUTO_SHOOT_GRAY_ROI(68, "setJpgAutoShootGrayRoiModeChannel"),

    //确认相机聚焦+
    CONFIRM_FOCUS(130, "confirmFocusModeChannel"),


    //确认相机聚焦-
    CONFIRM_FOCUS_MINUS(131, "confirmFocusMinusModeChannel"),


    //确认相机变倍+
    CONFIRM_ZOOM_PLUS(132, "confirmZoomPlusModeChannel"),

    //确认相机变倍-
    CONFIRM_ZOOM_MINUS(133, "confirmZoomMinusModeChannel"),

    //确认相机聚焦停
    CONFIRM_FOCUS_STOP(134, "confirmFocusStopModeChannel"),

    //确认相机变倍停
    CONFIRM_ZOOM_STOP(135, "confirmZoomStopModeChannel"),







    ;

    private final Integer type;

    private final String channelName;



    TcpDevControlEnums(Integer type, String channelName) {
        this.type = type;
        this.channelName = channelName;
    }


    // 缓存 Map
    private static final Map<Integer, TcpDevControlEnums> TYPE_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(TcpDevControlEnums::getType, e -> e));

    /**
     * 根据指令类型获取对应通道名
     */
    public static String getChannelName(Integer type) {
        return TYPE_MAP.getOrDefault(type, FREE).getChannelName();
    }


}
