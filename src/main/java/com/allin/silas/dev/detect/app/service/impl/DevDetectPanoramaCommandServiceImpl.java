package com.allin.silas.dev.detect.app.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.common.enums.IsDeletedEnums;
import com.allin.silas.dev.detect.adapter.dto.DevDetectPanoramaDto;
import com.allin.silas.dev.detect.adapter.dto.EditDevDetectPanoramaDto;
import com.allin.silas.dev.detect.app.entity.DevDetectPanorama;
import com.allin.silas.dev.detect.app.service.DevDetectPanoramaCommandService;
import com.allin.silas.dev.detect.infra.repository.DevDetectPanoramaMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* 针对表【dev_detect_panorama】的数据库操作Service实现
* @date 2025-04-29 17:12:59
*/
@Service
public class DevDetectPanoramaCommandServiceImpl implements DevDetectPanoramaCommandService {

    private final DevDetectPanoramaMapper devDetectPanoramaMapper;

    public DevDetectPanoramaCommandServiceImpl(DevDetectPanoramaMapper devDetectPanoramaMapper) {
        this.devDetectPanoramaMapper = devDetectPanoramaMapper;
    }

    @Override
    public boolean addPanorama(DevDetectPanoramaDto dto) {
        DevDetectPanorama devDetectPanorama = new DevDetectPanorama();
        BeanUtil.copyProperties(dto,devDetectPanorama);
        return devDetectPanoramaMapper.insert(devDetectPanorama)>0;
    }

    @Override
    public boolean updatePanorama(EditDevDetectPanoramaDto dto) {
        DevDetectPanorama devDetectPanorama = new DevDetectPanorama();
        BeanUtil.copyProperties(dto,devDetectPanorama);
        return devDetectPanoramaMapper.updateById(devDetectPanorama)>0;
    }

    @Override
    public boolean deleteById(String id) {
        DevDetectPanorama devDetectPanorama = new DevDetectPanorama();
        devDetectPanorama.setIsDeleted(IsDeletedEnums.TRUE.getCode());
        devDetectPanorama.setId(id);
        //修改全景图为删除状态
        return devDetectPanoramaMapper.updateById(devDetectPanorama)>0;
    }

}




