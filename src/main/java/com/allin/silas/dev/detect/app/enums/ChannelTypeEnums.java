package com.allin.silas.dev.detect.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 通道类型枚举，0:左目，1:右目
 *
 * <AUTHOR>
 * @date 2025/05/07
 */
public enum ChannelTypeEnums implements IEnums {
    /**
     * 0 左目
     */
    LEFT_CHANNEL(0),
    /**
     * 1 右目
     */
    RIGHT_CHANNEL(1);

    private final Integer code;

    ChannelTypeEnums(Integer code) {
        this.code = code;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
