package com.allin.silas.dev.detect.adapter.controller;

import com.allin.silas.dev.detect.adapter.dto.AddDevDetectInfoDto;
import com.allin.silas.dev.detect.adapter.dto.EditDevDetectInfoDto;
import com.allin.silas.dev.detect.adapter.query.DevDetectInfoQuery;
import com.allin.silas.dev.detect.adapter.vo.DevDetectInfoVo;
import com.allin.silas.dev.detect.app.service.DevDetectInfoCommandService;
import com.allin.silas.dev.detect.app.service.DevDetectInfoQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import com.allin.view.base.i18n.I18nUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 探测设备信息管理
 *
 * <AUTHOR>
 * @since 2025/4/28
 */
@Validated
@RestController
@RequestMapping("/dev/dev_detect_info")
class DevDetectInfoController {

    private final DevDetectInfoQueryService devDetectInfoQueryService;

    private final DevDetectInfoCommandService devDetectInfoCommandService;


    public DevDetectInfoController(DevDetectInfoQueryService devDetectInfoService,
                                   DevDetectInfoCommandService devDetectInfoCommandService) {
        this.devDetectInfoQueryService = devDetectInfoService;
        this.devDetectInfoCommandService = devDetectInfoCommandService;
    }


    /**
     * 添加设备信息
     */
    @PostMapping("/add")
    public Result<Void> add(@Validated @RequestBody AddDevDetectInfoDto devDetectInfo) {
        String devNum = devDetectInfo.getDevNum();
        if (devDetectInfoQueryService.isExistDevNum(devNum)) {
            return Result.fail(I18nUtil.isExist("devNum"));
        }
        // 获取项目ID
        return devDetectInfoCommandService.addDevInfo(devDetectInfo) ? Result.ok() : Result.fail();
    }


    /**
     * 查询设备信息列表
     */
    @GetMapping("/find_list")
    public Result<List<DevDetectInfoVo>> findList(@Validated DevDetectInfoQuery param) {
        // 获取项目ID
        param.setProjectId(SecurityContextHolder.getProjectId());
        return Result.ok(devDetectInfoQueryService.findList(param));
    }

    /**
     * 删除设备信息
     */
    @DeleteMapping("/delete/{id}")
    public Result<Void> delete(@PathVariable("id") String id) {
        return devDetectInfoCommandService.deleteById(id) ? Result.ok() : Result.fail();
    }

    /**
     * 探测信息修改
     */
    @PutMapping("/update")
    public Result<Void> update(@Validated @RequestBody EditDevDetectInfoDto devDetectInfo) {
        return devDetectInfoCommandService.updateDevInfo(devDetectInfo) ? Result.ok() : Result.fail();
    }


    /**
     * 设备继电器开关控制
     */
    @PostMapping("/dev_switch")
    public Result<Void> devSwitch(@RequestParam String devNum, @RequestParam int status) {
        return devDetectInfoCommandService.switchRelay(devNum, status) ? Result.ok() : Result.fail();
    }


}
