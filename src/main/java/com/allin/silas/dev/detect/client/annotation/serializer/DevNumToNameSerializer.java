package com.allin.silas.dev.detect.client.annotation.serializer;

import cn.hutool.extra.spring.SpringUtil;
import com.allin.silas.dev.detect.app.manager.DevDetectInfoManager;
import com.allin.silas.dev.detect.client.annotation.DevNumToName;
import com.allin.view.base.jackson.IJacksonSerializer;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 设备编号转名称
 *
 * <AUTHOR>
 * @since 2024/3/12
 */
@Slf4j
@NoArgsConstructor
public class DevNumToNameSerializer extends JsonSerializer<Object> implements ContextualSerializer, IJacksonSerializer {

    protected DevDetectInfoManager devDetectInfoManager;

    protected String key;

    public DevNumToNameSerializer(DevDetectInfoManager devDetectInfoManager, String key) {
        this.devDetectInfoManager = devDetectInfoManager;
        this.key = key;
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public void serialize(Object devNumObj, JsonGenerator json, SerializerProvider serializerProvider) throws IOException {
        Object newObj = null;
        if (Objects.isNull(devNumObj)) {
            return;
        }
        try {
            if (devNumObj instanceof String devNum) {
                newObj = devDetectInfoManager.getDevNameFromCache(devNum);
            } else if (devNumObj instanceof List devNums) {
                final List<String> devNames = new ArrayList<>();
                for (Object num : devNums) {
                    final String devName = devDetectInfoManager.getDevNameFromCache(String.valueOf(num));
                    if (devName != null) {
                        devNames.add(devName);
                    }
                }
                newObj = devNames;
            }

        } catch (Exception e) {
            log.error("无法将devNum转换为devName", e);
        } finally {
            doSerialize(json, devNumObj, newObj);
        }
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider provider, BeanProperty property) throws JsonMappingException {
        if (property == null) {
            return provider.findNullValueSerializer(null);
        }

        Class<?> rawClass = property.getType().getRawClass();
        if (handledType(rawClass)) {
            DevNumToName annotation = property.getAnnotation(DevNumToName.class);
            if (annotation == null) {
                annotation = property.getContextAnnotation(DevNumToName.class);
            }
            return new DevNumToNameSerializer(SpringUtil.getBean(DevDetectInfoManager.class), annotation.key());
        }

        return provider.findValueSerializer(property.getType(), property);
    }


    public boolean handledType(Class<?> rawClass) {
        return rawClass == String.class || List.class.isAssignableFrom(rawClass);
    }
}

