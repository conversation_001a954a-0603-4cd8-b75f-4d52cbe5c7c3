package com.allin.silas.dev.drive.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 
 * 驱鸟设备信息实体
 */
@Data
public class DevDriveInfoDto {
    /**
     * ID主键（雪花ID）
     */
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空")
    private String devName;

    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    private String devNum;

    /**
     * 设备类型
     */
    @NotBlank(message = "设备类型不能为空")
    private String devType;

    /**
     * 驱鸟设备厂商
     */
    private String devFactory;

    /**
     * 设备ip地址
     */
    @NotBlank(message = "设备ip地址不能为空")
    private String devIp;

    /**
     * 设备端口
     */
    @NotNull(message = "设备端口不能为空")
    private Integer devPort;

    /**
     * 驱鸟设备覆盖范围
     */
    private Integer devCoverage;

    /**
     * 经度
     */
    @NotBlank(message = "设备经度不能为空")
    private String longitude;

    /**
     * 纬度
     */
    @NotBlank(message = "设备纬度不能为空")
    private String latitude;

    /**
     * 所属跑道
     */
    private String runwayId;

    /**
     * 设备正北偏转角
     */
    @NotNull(message = "设备正北偏转角不能为空")
    private Float devNorthOffset;

    /**
     * 设备使用年限
     */
    private Float devUseAge;

    /**
     * 安装时间
     */
    private LocalDateTime installTime;

    /**
     * 备注信息
     */
    private String devRemarks;





}