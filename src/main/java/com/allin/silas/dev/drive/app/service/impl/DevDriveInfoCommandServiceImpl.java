package com.allin.silas.dev.drive.app.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.dev.drive.adapter.dto.DevDriveInfoDto;
import com.allin.silas.dev.drive.app.entity.DevDriveInfo;
import com.allin.silas.dev.drive.app.service.DevDriveInfoCommandService;
import com.allin.silas.dev.drive.infra.repository.DevDriveInfoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* 针对表【dev_drive_info】的数据库操作Service实现
* @date 2025-04-29 18:01:10
*/
@Service
public class DevDriveInfoCommandServiceImpl implements DevDriveInfoCommandService {

    private final DevDriveInfoMapper devDriveInfoMapper;

    public DevDriveInfoCommandServiceImpl(DevDriveInfoMapper devDriveInfoMapper) {
        this.devDriveInfoMapper = devDriveInfoMapper;
    }

    @Override
    public boolean addDevInfo(DevDriveInfoDto dto) {
        DevDriveInfo devDriveInfo = new DevDriveInfo();
        BeanUtil.copyProperties(dto,devDriveInfo);
        return devDriveInfoMapper.insert(devDriveInfo)>0;
    }

    @Override
    public boolean updateDevInfo(DevDriveInfoDto dto) {
        DevDriveInfo devDriveInfo = new DevDriveInfo();
        BeanUtil.copyProperties(dto,devDriveInfo);
        return devDriveInfoMapper.updateById(devDriveInfo)>0;
    }

    @Override
    public boolean deleteDevInfo(String id) {
        return devDriveInfoMapper.deleteById(id)>0;
    }


}




