package com.allin.silas.dev.drive.infra.repository;

import com.allin.silas.dev.drive.adapter.query.DevDriveStatusQuery;
import com.allin.silas.dev.drive.adapter.vo.DevDriveStatusVo;
import com.allin.silas.dev.drive.app.entity.DevDriveStatus;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
*  针对表【dev_drive_status】的数据库操作Mapper
*/
public interface DevDriveStatusMapper extends BaseMapper<DevDriveStatus> {

    List<DevDriveStatusVo> findList(@Param("param")DevDriveStatusQuery param);
}




