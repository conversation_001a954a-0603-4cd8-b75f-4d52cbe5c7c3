package com.allin.silas.dev.drive.app.service;


import com.allin.silas.dev.drive.adapter.query.DevDriveStatusQuery;
import com.allin.silas.dev.drive.adapter.vo.DevDriveInfoVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**
* <AUTHOR>
* 针对表【dev_drive_info】的数据库操作Service
* @date 2025-04-29 18:01:10
*/
public interface DevDriveInfoQueryService {


    boolean isExistDevNum(String devNum);

    Page<DevDriveInfoVo> findPage(DevDriveStatusQuery param, Page<DevDriveInfoVo> page);
}
