package com.allin.silas.dev.drive.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 
 * 驱鸟设备曲目信息
 */
@Data
public class DevDriveSongDto {


    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 驱鸟设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    private String devNum;

    /**
     * 曲目序号
     */
    private Integer songNum;

    /**
     * 曲目名称
     */
    private String songName;

}