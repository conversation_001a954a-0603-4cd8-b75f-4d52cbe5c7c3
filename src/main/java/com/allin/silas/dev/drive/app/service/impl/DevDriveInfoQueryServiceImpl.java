package com.allin.silas.dev.drive.app.service.impl;


import com.allin.silas.dev.drive.adapter.query.DevDriveStatusQuery;
import com.allin.silas.dev.drive.adapter.vo.DevDriveInfoVo;
import com.allin.silas.dev.drive.app.manager.DevDriveInfoManager;
import com.allin.silas.dev.drive.app.service.DevDriveInfoQueryService;
import com.allin.silas.dev.drive.infra.repository.DevDriveInfoMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* 针对表【dev_drive_info】的数据库操作Service实现
* @date 2025-04-29 18:01:10
*/
@Service
public class DevDriveInfoQueryServiceImpl implements DevDriveInfoQueryService {

    private final DevDriveInfoMapper devDriveInfoMapper;

    private final DevDriveInfoManager devDriveInfoManager;

    public DevDriveInfoQueryServiceImpl(DevDriveInfoMapper devDriveInfoMapper, DevDriveInfoManager devDriveInfoManager) {
        this.devDriveInfoMapper = devDriveInfoMapper;
        this.devDriveInfoManager = devDriveInfoManager;
    }


    @Override
    public boolean isExistDevNum(String devNum) {
        return devDriveInfoManager.isExistDevNum(devNum);
    }

    @Override
    public Page<DevDriveInfoVo> findPage(DevDriveStatusQuery param,Page<DevDriveInfoVo> page) {
        return devDriveInfoMapper.findPage(param,page);
    }
}




