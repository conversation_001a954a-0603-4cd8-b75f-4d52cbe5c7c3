package com.allin.silas.dev.drive.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 驱鸟设备状态枚举类
 */
public enum DevDriveStatusEnums implements IEnums {
    /**
     * 关闭
     */
    OFF_LINE(0, "离线"),
    /**
     * 开启
     */
    ONLINE(1, "在线"),
    /**
     * 异常
     */
    ABNORMAL(2, "异常");


    private final Integer code;

    private final String desc;

    DevDriveStatusEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
