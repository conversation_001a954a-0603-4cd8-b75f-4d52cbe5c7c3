package com.allin.silas.dev.drive.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 驱鸟设备类型：
 *
 * <AUTHOR>
 * @date 2025/05/06
 */
public enum DevDriveTypeEnums implements IEnums {
    /**
     * 全向声波炮-omni
     */
    OMNI("omni", "全向声波"),
    /**
     * 定向声波炮-directional
     */
    DIRECTIONAL("directional", "定向声波"),
    /**
     * 煤气炮-gas
     */
    GAS("gas", "煤气炮"),

    /**
     * 冲击波炮-ethylene
     */
    ETHYLENE("ethylene", "冲击波炮"),

    /**
     * 蓝光灯-blue
     */
    BLUE("blue", "蓝光灯"),

    /**
     * 电磁炮-shell
     */
    SHELL("shell", "电磁炮");

    private final String code;

    private final String name;

    DevDriveTypeEnums(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
