package com.allin.silas.dev.drive.adapter.controller;

import com.allin.silas.dev.drive.adapter.query.DevDriveStatusQuery;
import com.allin.silas.dev.drive.adapter.vo.DevDriveStatusVo;
import com.allin.silas.dev.drive.app.service.DevDriveStatusQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 驱鸟设备状态信息
 *
 * <AUTHOR>
 * @since 2025/4/28
 */
@Validated
@RestController
@RequestMapping("/dev/dev_drive_status")
class DevDriveStatusController {

    private final DevDriveStatusQueryService devDriveStatusService;


    DevDriveStatusController(DevDriveStatusQueryService devDriveStatusService) {
        this.devDriveStatusService = devDriveStatusService;
    }

    /**
     * 获取驱鸟设备状态信息列表
     */
    @GetMapping("/find_list")
    public Result<List<DevDriveStatusVo>> findList(DevDriveStatusQuery param) {
        param.setProjectId(SecurityContextHolder.getProjectId());
        return Result.ok(devDriveStatusService.findList(param));
    }


}
