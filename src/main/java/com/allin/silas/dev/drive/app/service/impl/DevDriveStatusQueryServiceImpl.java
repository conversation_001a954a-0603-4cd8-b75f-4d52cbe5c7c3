package com.allin.silas.dev.drive.app.service.impl;


import com.allin.silas.dev.drive.adapter.query.DevDriveStatusQuery;
import com.allin.silas.dev.drive.adapter.vo.DevDriveStatusVo;
import com.allin.silas.dev.drive.app.service.DevDriveStatusQueryService;
import com.allin.silas.dev.drive.infra.repository.DevDriveStatusMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* 针对表【dev_drive_status】的数据库操作Service实现
* @date 2025-05-05 17:31:05
*/
@Service
public class DevDriveStatusQueryServiceImpl implements DevDriveStatusQueryService {

    private final DevDriveStatusMapper devDriveStatusMapper;

    public DevDriveStatusQueryServiceImpl(DevDriveStatusMapper devDriveStatusMapper) {
        this.devDriveStatusMapper = devDriveStatusMapper;
    }

    @Override
    public List<DevDriveStatusVo> findList(DevDriveStatusQuery param) {
       return  devDriveStatusMapper.findList(param);
    }
}




