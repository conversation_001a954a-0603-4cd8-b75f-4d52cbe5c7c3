package com.allin.silas.dev.drive.app.manager.impl;

import com.allin.silas.dev.drive.app.entity.DevDriveInfo;
import com.allin.silas.dev.drive.app.manager.DevDriveInfoManager;
import com.allin.silas.dev.drive.infra.repository.DevDriveInfoMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;

/**
 * 驱鸟设备信息管理
 * <AUTHOR>
 * @since 2025/5/9
 */
@Service
public class DevDriveInfoManagerImpl implements DevDriveInfoManager {

    private final DevDriveInfoMapper devDriveInfoMapper;

    public DevDriveInfoManagerImpl(DevDriveInfoMapper devDriveInfoMapper) {
        this.devDriveInfoMapper = devDriveInfoMapper;
    }


    @Override
    public boolean isExistDevNum(String devNum) {
        return devDriveInfoMapper.selectCount(
                Wrappers.<DevDriveInfo>lambdaQuery()
                        .eq(DevDriveInfo::getDevNum, devNum)
        ) > 0;
    }
}
