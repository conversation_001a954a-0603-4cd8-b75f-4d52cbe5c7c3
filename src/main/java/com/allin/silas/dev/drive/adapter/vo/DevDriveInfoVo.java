package com.allin.silas.dev.drive.adapter.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 
 * @TableName dev_drive_info
 */
@Data
public class DevDriveInfoVo {
    /**
     * ID主键（雪花ID）
     */
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备类型
     */
    private String devType;

    /**
     * 驱鸟设备厂商
     */
    private String devFactory;

    /**
     * 设备ip地址
     */
    private String devIp;

    /**
     * 设备端口
     */
    private Integer devPort;

    /**
     * 驱鸟设备覆盖范围
     */
    private Integer devCoverage;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 所属跑道
     */
    private String runwayId;

    /**
     * 设备正北偏转角
     */
    private Float devNorthOffset;

    /**
     * 设备使用年限
     */
    private Float devUseAge;

    /**
     * 安装时间
     */
    private LocalDateTime installTime;

    /**
     * 备注信息
     */
    private String devRemarks;


    /**
     * 设备状态：0：离线；1：在线；2：异常
     */
    private Integer devStatus;

    /**
     * 设备运行状态详情
     */
    private String devRunStatus;





}