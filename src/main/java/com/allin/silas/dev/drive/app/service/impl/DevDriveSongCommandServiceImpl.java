package com.allin.silas.dev.drive.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.dev.drive.adapter.dto.DevDriveSongDto;
import com.allin.silas.dev.drive.app.service.DevDriveSongCommandService;
import com.allin.silas.dev.drive.app.entity.DevDriveSong;
import com.allin.silas.dev.drive.infra.repository.DevDriveSongMapper;
import org.apache.ibatis.executor.BatchResult;
import org.springframework.stereotype.Service;
import java.util.List;

/**
* <AUTHOR>
* 针对表【dev_drive_song】的数据库操作Service实现
* @date 2025-05-05 17:30:50
*/
@Service
public class DevDriveSongCommandServiceImpl implements DevDriveSongCommandService {

    private final DevDriveSongMapper devDriveSongMapper;

    public DevDriveSongCommandServiceImpl(DevDriveSongMapper devDriveSongMapper) {
        this.devDriveSongMapper = devDriveSongMapper;
    }

    @Override
    public boolean add(DevDriveSongDto dto) {
        DevDriveSong devDriveSong = new DevDriveSong();
        devDriveSong.setProjectId(dto.getProjectId());
        return devDriveSongMapper.insert(devDriveSong) > 0;
    }

    @Override
    public boolean deleteById(String id) {
        return devDriveSongMapper.deleteById(id)>0;
    }


    @Override
    public boolean batchAdd(List<DevDriveSongDto> songDtoList, String projectId) {
        List<DevDriveSong> devDriveSongs = songDtoList.stream()
                .map(dto -> {
                    DevDriveSong entity = new DevDriveSong();
                    BeanUtil.copyProperties(dto, entity);
                    entity.setProjectId(projectId);
                    return entity;
                }).toList();
        List<BatchResult> lists = devDriveSongMapper.insert(devDriveSongs);
        return lists.size() == devDriveSongs.size();
    }

}




