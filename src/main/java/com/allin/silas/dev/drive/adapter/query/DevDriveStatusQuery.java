package com.allin.silas.dev.drive.adapter.query;

import lombok.Data;


/**
 * 驱鸟设备状态信息
 *
 */
@Data
public class DevDriveStatusQuery {

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 驱鸟设备编号
     */
    private String devNum;

    /**
     * 驱鸟设备名称
     */
    private String devName;

    /**
     * 设备状态：0：离线；1：在线；2：异常
     */
    private Integer devStatus;

    /**
     * 驱鸟设备类型：全向声波炮-omni； 定向声波炮-directional；煤气炮-gas；冲击波炮-ethylene；蓝光灯-blue；电磁炮-shell
     */
    private String devType;




}