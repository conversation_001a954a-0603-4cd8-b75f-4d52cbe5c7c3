package com.allin.silas.dev.drive.app.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 
 * @TableName dev_drive_status
 */
@TableName(value ="dev_drive_status")
@Data
public class DevDriveStatus {
    /**
     * 主键ID（雪花ID）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 驱鸟设备编号
     */
    private String devNum;

    /**
     * 设备状态：0：离线；1：在线；2：异常
     */
    private Integer devStatus;

    /**
     * 设备运行状态详情
     */
    private String devRunStatus;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;
}