package com.allin.silas.dev.drive.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 
 * @TableName dev_drive_info
 */
@TableName(value ="dev_drive_info")
@Data
public class DevDriveInfo {
    /**
     * ID主键（雪花ID）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 设备类型
     */
    private String devType;

    /**
     * 驱鸟设备厂商
     */
    private String devFactory;

    /**
     * 设备ip地址
     */
    private String devIp;

    /**
     * 设备端口
     */
    private Integer devPort;

    /**
     * 驱鸟设备覆盖范围
     */
    private Integer devCoverage;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 所属跑道
     */
    private String runwayId;

    /**
     * 设备正北偏转角
     */
    private Float devNorthOffset;

    /**
     * 设备使用年限
     */
    private Float devUseAge;

    /**
     * 安装时间
     */
    private LocalDateTime installTime;

    /**
     * 备注信息
     */
    private String devRemarks;



    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;
}