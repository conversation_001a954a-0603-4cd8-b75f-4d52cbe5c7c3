package com.allin.silas.dev.drive.adapter.controller;

import com.allin.silas.dev.drive.adapter.dto.DevDriveSongDto;
import com.allin.silas.dev.drive.adapter.query.DevDriveSongQuery;
import com.allin.silas.dev.drive.app.service.DevDriveSongCommandService;
import com.allin.silas.dev.drive.app.entity.DevDriveSong;
import com.allin.silas.dev.drive.app.service.DevDriveSongQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 驱鸟设备曲目信息
 *
 * <AUTHOR>
 * @date 2025/5/5
 */
@Validated
@RestController
@RequestMapping("/dev/dev_drive_song")
class DevDriveSongController {

    private final DevDriveSongCommandService devDriveSongCommandService;
    private final DevDriveSongQueryService devDriveSongQueryService;

    public DevDriveSongController(DevDriveSongCommandService devDriveSongService, DevDriveSongQueryService devDriveSongQueryService) {
        this.devDriveSongCommandService = devDriveSongService;
        this.devDriveSongQueryService = devDriveSongQueryService;
    }

    /**
     * 新增驱鸟设备曲目信息
     */
    @PostMapping("/add")
    public Result<Void> add(@Validated @RequestBody DevDriveSongDto dto) {
        dto.setProjectId(SecurityContextHolder.getProjectId());
        return devDriveSongCommandService.add(dto) ? Result.ok() : Result.fail();
    }

    /**
     * 删除驱鸟设备曲目信息
     */
    @DeleteMapping("/delete/{id}")
    public Result<Void> delete(@PathVariable("id")String id) {
        return devDriveSongCommandService.deleteById(id) ? Result.ok() : Result.fail();
    }

    /**
     * 查询驱鸟设备曲目列表
     */
    @GetMapping("/find_list")
    public Result<List<DevDriveSong>> findList(DevDriveSongQuery param) {
        param.setProjectId(SecurityContextHolder.getProjectId());
        return Result.ok(devDriveSongQueryService.findList(param));
    }


    /**
     * 驱鸟设备曲目批量新增
     */
    @PostMapping("/batch_add")
    public Result<Void> add(@Validated @RequestBody List<DevDriveSongDto> songDtoList) {
        return devDriveSongCommandService.batchAdd(songDtoList,  SecurityContextHolder.getProjectId()) ? Result.ok() : Result.fail();
    }







}
