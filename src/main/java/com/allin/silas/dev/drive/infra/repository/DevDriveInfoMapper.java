package com.allin.silas.dev.drive.infra.repository;

import com.allin.silas.dev.drive.adapter.query.DevDriveStatusQuery;
import com.allin.silas.dev.drive.adapter.vo.DevDriveInfoVo;
import com.allin.silas.dev.drive.app.entity.DevDriveInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* 针对表【dev_drive_info】的数据库操作Mapper
* @date 2025-04-29 18:01:10
*/
public interface DevDriveInfoMapper extends BaseMapper<DevDriveInfo> {

    Page<DevDriveInfoVo> findPage(@Param("param")DevDriveStatusQuery param,@Param("page")Page<DevDriveInfoVo> page);
}




