package com.allin.silas.dev.drive.adapter.controller;

import com.allin.silas.dev.drive.adapter.dto.DevDriveInfoDto;
import com.allin.silas.dev.drive.adapter.query.DevDriveStatusQuery;
import com.allin.silas.dev.drive.adapter.vo.DevDriveInfoVo;
import com.allin.silas.dev.drive.app.service.DevDriveInfoCommandService;
import com.allin.silas.dev.drive.app.service.DevDriveInfoQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 驱鸟设备信息
 *
 * <AUTHOR>
 * @since 2025/4/28
 */
@Validated
@RestController
@RequestMapping("/dev/dev_drive_info")
class DevDriveInfoController {

    private final DevDriveInfoQueryService devDriveInfoQueryService;
    private final DevDriveInfoCommandService devDriveInfoCommandService;


    public DevDriveInfoController(DevDriveInfoQueryService devDriveInfoService, DevDriveInfoCommandService devDriveInfoCommandService) {
        this.devDriveInfoQueryService = devDriveInfoService;
        this.devDriveInfoCommandService = devDriveInfoCommandService;
    }

    /**
     * 新增驱鸟设备信息
     */
    @PostMapping("/add")
    public Result<Void> add(@Validated @RequestBody DevDriveInfoDto dto){
        //判断设备编号是否存在
        if (devDriveInfoQueryService.isExistDevNum(dto.getDevNum())) {
            return Result.fail("驱鸟设备编号已存在！");
        }
        dto.setProjectId(SecurityContextHolder.getProjectId());
        return devDriveInfoCommandService.addDevInfo(dto) ?  Result.ok() : Result.fail();
    }


    /**
     * 修改驱鸟设备信息
     */
    @PutMapping("/update")
    public Result<Void> update(@Validated @RequestBody DevDriveInfoDto dto){
        dto.setProjectId(SecurityContextHolder.getProjectId());
        return devDriveInfoCommandService.updateDevInfo(dto) ?  Result.ok() : Result.fail();
    }


    /**
     * 删除驱鸟设备信息
     */
    @DeleteMapping("/delete/{id}")
    public Result<Void> delete(@PathVariable("id")String id) {
        return devDriveInfoCommandService.deleteDevInfo(id) ?  Result.ok() : Result.fail();
    }


    /**
     * 获取驱鸟设备列表分页
     */
    @GetMapping("/find_page")
    public Result<Page<DevDriveInfoVo>> findPage(DevDriveStatusQuery param, Page<DevDriveInfoVo> page) {
        param.setProjectId(SecurityContextHolder.getProjectId());
        return Result.ok(devDriveInfoQueryService.findPage(param,page));
    }

}
