package com.allin.silas.dev.drive.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.dev.drive.adapter.query.DevDriveSongQuery;
import com.allin.silas.dev.drive.app.entity.DevDriveSong;
import com.allin.silas.dev.drive.app.service.DevDriveSongQueryService;
import com.allin.silas.dev.drive.infra.repository.DevDriveSongMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* 针对表【dev_drive_song】的数据库操作Service实现
* @date 2025-05-05 17:30:50
*/
@Service
public class DevDriveSongQueryServiceImpl implements DevDriveSongQueryService {

    private final DevDriveSongMapper devDriveSongMapper;

    public DevDriveSongQueryServiceImpl(DevDriveSongMapper devDriveSongMapper) {
        this.devDriveSongMapper = devDriveSongMapper;
    }


    @Override
    public List<DevDriveSong> findList(DevDriveSongQuery param) {
        LambdaQueryWrapper<DevDriveSong> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(param.getProjectId()),DevDriveSong::getProjectId,param.getProjectId());
        queryWrapper.eq(StrUtil.isNotEmpty(param.getDevNum()),DevDriveSong::getDevNum,param.getDevNum());
        queryWrapper.eq(ObjectUtil.isNotEmpty(param.getSongNum()),DevDriveSong::getSongName,param.getSongName());
        return devDriveSongMapper.selectList(queryWrapper);
    }


}




