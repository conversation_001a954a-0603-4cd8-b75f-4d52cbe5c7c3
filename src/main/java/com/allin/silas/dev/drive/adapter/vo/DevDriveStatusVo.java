package com.allin.silas.dev.drive.adapter.vo;

import lombok.Data;


/**
 * 驱鸟设备状态信息
 *
 */
@Data
public class DevDriveStatusVo {
    /**
     * 主键ID（雪花ID）
     */
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 驱鸟设备编号
     */
    private String devNum;

    /**
     * 设备状态：0：离线；1：在线；2：异常
     */
    private Integer devStatus;

    /**
     * 设备运行状态详情
     */
    private String devRunStatus;


}