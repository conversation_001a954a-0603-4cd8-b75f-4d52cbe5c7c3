package com.allin.silas.dev.drive.app.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 
 * @TableName dev_drive_song
 */
@TableName(value ="dev_drive_song")
@Data
public class DevDriveSong {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 驱鸟设备编号
     */
    private String devNum;

    /**
     * 曲目序号
     */
    private Integer songNum;

    /**
     * 曲目名称
     */
    private String songName;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;
}