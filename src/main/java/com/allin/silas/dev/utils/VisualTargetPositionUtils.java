package com.allin.silas.dev.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 目标位置换算工具类
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Slf4j
public class VisualTargetPositionUtils {

    // 地球平均半径（单位：米）
    private static final double EARTH_RADIUS = 6371e3;

    /**
     * 根据设备的经纬度信息，计算目标的经纬度信息
     *
     * @param devPosition 设备位置
     * @param dis         距离
     * @param azimuth     相对正北的绝对角度
     * @param devOffset   设备偏移角度
     * @return 目标位置
     */
    public static double[] getTargetPosition(double[] devPosition, float dis, double azimuth, double devOffset) {
        double distance = dis / EARTH_RADIUS; // 归一化距离
        double[] targetPosition = new double[2];

        // 获取设备的经纬度（假设顺序为：经度、纬度）
        double lonRad = Math.toRadians(devPosition[0]);
        double latRad = Math.toRadians(devPosition[1]);

        // 目标相对正北的绝对方向（单位为弧度）
        double bearingRad = Math.toRadians(azimuth + devOffset);

        // 计算目标的纬度
        double latTarget = Math.asin(Math.sin(latRad) * Math.cos(distance) +
                                     Math.cos(latRad) * Math.sin(distance) * Math.cos(bearingRad));

        // 计算目标的经度
        double lonTarget = lonRad + Math.atan2(
                Math.sin(bearingRad) * Math.sin(distance) * Math.cos(latRad),
                Math.cos(distance) - Math.sin(latRad) * Math.sin(latTarget));

        // 转换为角度并返回（仍使用 [经度, 纬度] 的顺序）
        targetPosition[0] = Math.toDegrees(lonTarget);
        targetPosition[1] = Math.toDegrees(latTarget);

        return targetPosition;
    }

    /**
     * 获取目标的方位或俯仰信息
     */
    public static Float parseAzimuthOrPitch(String str) {
        if (StrUtil.isEmpty(str)) {
            return 0f;
        }
        return Float.parseFloat(str.split(",")[0]);
    }


    /**
     * 保留2位小数处理
     */
    public static float roundFloat(float value, int scale) {
        return new BigDecimal(Float.toString(value))
                .setScale(scale, RoundingMode.HALF_UP)
                .floatValue();
    }


}
