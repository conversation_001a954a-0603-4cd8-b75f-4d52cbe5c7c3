package com.allin.silas.dev.utils;

import cn.hutool.core.io.FileUtil;
import jakarta.annotation.Nullable;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * @author: 郭国勇
 * @Date: 2025/6/23 17:57
 * @Description
 */
public class EvalUtils {
    private String classname;
    private boolean isCompiler = false;
    private final String devNum;
    private static final String FX = ".java";
    private static final String CLASS_HEAD = "public class ";
    private final Charset charset = StandardCharsets.UTF_8;
    private final StringBuilder code = new StringBuilder();
    private String path = System.getProperty("user.dir") + File.separator + "/eval/";

    private Integer y;

    private static final String JAVA_HOME = System.getProperty("java.home").replace("jre", "bin");

    public EvalUtils(String code, String devNum) {
        this.devNum = devNum;
        // 只保留字母
        init(code);
    }

    public EvalUtils(String path, String name, String devNum) {
        this.devNum = devNum;
        this.path = path;
        int doc = name.lastIndexOf(".");
        if (name.substring(doc).equals(FX)) {
            this.classname = name.substring(0, doc);
        } else {
            init(getCode(name));
        }
    }


    private void init(String code) {
        if (code.contains(CLASS_HEAD)) {
            this.code.append(code);
            initClassname();
        } else {
            initCode();
            this.code.append(code);
            endCode();
        }
        writeFile();
    }

    public void compiler() {
        try {
            Process exec = Runtime.getRuntime().exec( JAVA_HOME + "/bin/javac " + classname + FX, null, new File(path));
            StringBuilder compilerInfo = new StringBuilder();
            try (BufferedReader bf = new BufferedReader(
                    new InputStreamReader(exec.getErrorStream(), charset))) {
                String line;
                while ((line = bf.readLine()) != null) {
                    compilerInfo.append(line);
                    compilerInfo.append("\n");
                }
                if (compilerInfo.length() == 0) {
                    isCompiler = true;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Nullable
    public String run() {
        if (!isCompiler) {
            return null;
        }
        try {
            Process exec = Runtime.getRuntime().exec( JAVA_HOME + "/bin/java " + classname, null, new File(path));
            StringBuilder runInfo = new StringBuilder();
            try (BufferedReader bf = new BufferedReader(
                    new InputStreamReader(exec.getInputStream(), charset))) {
                String line;
                while ((line = bf.readLine()) != null) {
                    runInfo.append(line);
                }
                return runInfo.toString();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private String getCode(String name) {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader bf =
                     new BufferedReader(
                             new InputStreamReader(Files.newInputStream(Paths.get(path + name))));) {
            String line;
            while ((line = bf.readLine()) != null) {
                sb.append(line);
                sb.append("\n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    private void writeFile() {
        FileUtil.mkdir(new File(path));
        try (PrintStream ps = new PrintStream(path + classname + FX, String.valueOf(charset));) {
            ps.println(code.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void initClassname() {
        String s = "public class ";
        int classnameStart = code.indexOf(s) + s.length();
        int classnameEnd = code.indexOf(" ", classnameStart);
        classname = code.substring(classnameStart, classnameEnd);
    }

    private void initCode() {
        code.append("public class Test"+this.devNum+" {");
        code.append("    public static void main(String [] args){");
        classname = "Test" + this.devNum;
    }

    private void endCode() {
        code.append("}");
        code.append("}");
    }
}
