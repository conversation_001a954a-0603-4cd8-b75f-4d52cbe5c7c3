package com.allin.silas.dev.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.dev.ipc.app.operate.pojo.PtzDto;

import java.util.Optional;

/**
 * @author: 郭国勇
 * @Date: 2025/6/23 17:59
 * @Description
 */
public class Eval {

    private EvalUtils evalUtils;

    private Integer y;

    public Eval(Integer x, Integer y, String param, String ipcNum) {
        String code = "int x = " + x + ";int y = " + y + ";" +
                "double p = 1;double t = 2;" + param +
                "System.out.print(p+\";\"+t);";
        this.y = y;
        evalUtils = new EvalUtils(code, ipcNum);
    }

    /**
     * 返回ptz对象
     *
     * <AUTHOR>
     * @date 2023/6/15
     */
    public Optional<PtzDto> process() {
        evalUtils.compiler();
        String result = evalUtils.run();
        if (StrUtil.isBlank(result)){
            return Optional.empty();
        }
        Double[] pt = Convert.toDoubleArray(result.split(";"));
        double p = pt[0];
        double t = pt[1];
        if (p < 0) {
            p = p + 360;
        }
        if (p > 360) {
            p = p - 360;
        }
        if (t < -15) {
            t = -15;
        }
        int z = Convert.toInt(10 + 50 * ((750 - this.y) / 500.0));
        int pInt = Convert.toInt(p * 10);
        int tInt = Convert.toInt(t * 10);
        final PtzDto ptz = new PtzDto();
        ptz.setPan(pInt);
        ptz.setTilt(tInt);
        ptz.setZoom(z);
        return Optional.ofNullable(ptz);
    }

    public static void main(String[] args) {
        System.out.println( System.getProperty("java.home"));
        String evalScript =   " if((x>(1-1)*6*597+1)&&(x<1*6*597)) {p=((0.00001206)*x*x+(0.00113644)*y*y+(-0.00001496)*x*y+(1.04748580)*x+(-0.44524130)*y+(1359.91803991))*0.01645833+(271.20000000); t=((-0.00000850)*x*x+(0.00008142)*y*y+(0.00001245)*x*y+(-0.00538419)*x+(1.44634499)*y+(66.39952758))*0.01750000+(-3.45000000);}; if((x>(2-1)*6*597+1)&&(x<2*6*597)) {p=((0.00001570)*x*x+(0.00069611)*y*y+(-0.00019073)*x*y+(0.95316985)*x+(0.98995177)*y+(1552.04436506))*0.01645833+(271.20000000); t=((-0.00002171)*x*x+(-0.00045252)*y*y+(0.00020850)*x*y+(0.22777396)*x+(-0.02013054)*y+(-539.76409934))*0.01750000+(-3.45000000);}; if((x>(3-1)*6*597+1)&&(x<3*6*597)) {p=((0.00001337)*x*x+(-0.00022141)*y*y+(-0.00004016)*x*y+(0.88267850)*x+(0.54256554)*y+(2121.57758461))*0.01645833+(271.20000000)+8; t=((0.00000200)*x*x+(-0.00043062)*y*y+(0.00000711)*x*y+(0.00657062)*x+(1.17482873)*y+(-93.99967265))*0.01750000+(-3.45000000);}; if((x>(4-1)*6*597+1)&&(x<4*6*597)) {p=((-0.00000026)*x*x+(0.00073733)*y*y+(0.00010583)*x*y+(1.10137245)*x+(-1.67447983)*y+(1418.45523935))*0.01645833+(271.20000000)+9; t=((-0.00000964)*x*x+(-0.00013008)*y*y+(-0.00004579)*x*y+(0.26561670)*x+(1.60901760)*y+(-1521.63075632))*0.01750000+(-3.45000000);}; if((x>(5-1)*6*597+1)&&(x<5*6*597)) {p=((0.00002486)*x*x+(-0.00016185)*y*y+(0.00005940)*x*y+(0.29272938)*x+(-0.93838596)*y+(7953.13498777))*0.01645833+(271.20000000)+9; t=((0.00003315)*x*x+(0.00050173)*y*y+(-0.00015250)*x*y+(-1.07182457)*x+(3.28129877)*y+(8855.27066545))*0.01750000+(-3.45000000);}; if((x>5*6*597-1)) {p=((0.00002486)*x*x+(-0.00016185)*y*y+(0.00005940)*x*y+(0.29272938)*x+(-0.93838596)*y+(7953.13498777))*0.01645833+(271.20000000); t=((0.00003315)*x*x+(0.00050173)*y*y+(-0.00015250)*x*y+(-1.07182457)*x+(3.28129877)*y+(8855.27066545))*0.01750000 +(-3.45000000);};  ";
        final Eval eval = new Eval(100, 100, evalScript, "a" + 11232312);
        final Optional<PtzDto> optional = eval.process();
        if (optional.isPresent()) {
            System.out.println(optional.get().getPan());
            System.out.println(optional.get().getTilt());
        }
    }
}