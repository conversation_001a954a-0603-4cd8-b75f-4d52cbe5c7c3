package com.allin.silas.dev.server.app.service;

import com.allin.silas.dev.server.adapter.query.DevServerQuery;
import com.allin.silas.dev.server.adapter.vo.DevServerVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @author: 郭国勇
 * @Date: 2025/7/17 10:13
 * @Description
 */
public interface DevServerQueryService {

    void getDevServerPage(Page<DevServerVo> page, DevServerQuery devServerQuery);

    DevServerVo getDevServer(String id);
}
