package com.allin.silas.dev.server.infra.repository;


import com.allin.silas.dev.server.adapter.query.DevServerQuery;
import com.allin.silas.dev.server.adapter.vo.DevServerVo;
import com.allin.silas.dev.server.app.entity.DevServer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * @description 针对表【dev_server(系统设备)】的数据库操作Mapper
 * <AUTHOR>
 * @since 2024/04/20 13:31:08
 */
public interface DevServerMapper extends BaseMapper<DevServer> {

    Page<DevServerVo> getDevServerPage(Page<DevServerVo> page, @Param("query") DevServerQuery query);
}




