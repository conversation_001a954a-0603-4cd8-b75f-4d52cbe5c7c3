package com.allin.silas.dev.server.adapter.query;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * DevServer 系统设备查询Qo
 *
 * <AUTHOR>
 * @since 2024/04/20 13:31:08
 */
@Data
public class DevServerQuery {

    /**
     * 设备名称
     */
    private String serverName;
    /**
     * 设备编号
     */
    private String serverNumber;
    /**
     * 设备类型(0：应用服务器, 1：增强识别服务器, 2：流媒体服务器, 3：NCE设备, 4：IVS设备, 5：文件服务器, 6：NVR设备)
     */
    private Integer serverType;

    /**
     * 设备类型集合
     */
    private List<Integer> serverTypes;
    /**
     * IP地址
     */
    private String ipAddress;
    /**
     * 通信端口
     */
    private Integer communicationPort;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户密码
     */
    private String userPassword;
    /**
     * 状态(1启用, 0禁用)
     */
    private Integer status;
    /**
     * 项目主键
     */
    @NotBlank
    private String projectId;

}
