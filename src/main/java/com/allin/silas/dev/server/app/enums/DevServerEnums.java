package com.allin.silas.dev.server.app.enums;


import com.allin.view.base.enums.base.IEnums;

/**
 * 设备类型(0：应用服务器, 1：增强识别服务器(ONVIF), 2：流媒体服务器, 3：NCE设备, 4：IVS设备, 5：文件服务器, 6：NVR设备, 7: 增强识别服务器(RTSP/GB28181))
 */
public enum DevServerEnums implements IEnums {
    /**
     * 应用服务器
     */
    APPLICATION_SERVER(0, "应用服务器"),
    /**
     * 增强识别服务器
     */
    ENHANCED_IDENTIFICATION_SERVER(1, "增强识别服务器(基于图片)"),
    /**
     * 流媒体服务器
     */
    STREAMING_MEDIA_SERVER(2, "流媒体服务器"),
    /**
     * NCE设备
     */
    NCE_DEVICE(3, "NCE设备"),
    /**
     * IVS设备
     */
    IVS_DEVICE(4, "IVS设备"),
    /**
     * 文件服务器
     */
    FILE_SERVER(5, "文件服务器"),
    /**
     * NVR设备
     */
    NVR_DEVICE(6, "NVR设备"),
    /**
     * 增强识别服务器(RTSP/GB28181)
     */
    ENHANCED_IDENTIFICATION_SERVER_2(7, "增强识别服务器(基于视频流)");

    private final Integer code;
    private final String desc;

    DevServerEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}
