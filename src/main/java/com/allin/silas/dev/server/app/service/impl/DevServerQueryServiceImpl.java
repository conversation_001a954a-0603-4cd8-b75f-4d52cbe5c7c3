package com.allin.silas.dev.server.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.dev.server.adapter.query.DevServerQuery;
import com.allin.silas.dev.server.adapter.vo.DevServerVo;
import com.allin.silas.dev.server.app.entity.DevServer;
import com.allin.silas.dev.server.app.service.DevServerQueryService;
import com.allin.silas.dev.server.infra.repository.DevServerMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Component;

/**
 * @author: 郭国勇
 * @Date: 2025/7/17 10:13
 * @Description
 */
@Component
public class DevServerQueryServiceImpl implements DevServerQueryService {

    private final DevServerMapper devServerMapper;

    public DevServerQueryServiceImpl(DevServerMapper devServerMapper) {
        this.devServerMapper = devServerMapper;
    }

    @Override
    public void getDevServerPage(Page<DevServerVo> page, DevServerQuery devServerQuery) {
        devServerMapper.getDevServerPage(page, devServerQuery);
    }

    @Override
    public DevServerVo getDevServer(String id) {
        DevServer devServer = devServerMapper.selectById(id);
        return BeanUtil.toBean(devServer, DevServerVo.class);
    }
}
