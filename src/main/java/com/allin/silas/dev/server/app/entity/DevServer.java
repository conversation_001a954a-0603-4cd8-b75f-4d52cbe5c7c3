package com.allin.silas.dev.server.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统设备
 *
 * <AUTHOR>
 * @TableName dev_server
 * @since 2024/04/20 13:31:08
 */
@Data
@TableName(value = "dev_server", autoResultMap = true)
public class DevServer implements Serializable {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 设备名称
     */
    private String serverName;
    /**
     * 设备编号
     */
    private String serverNumber;
    /**
     * 设备类型(0：应用服务器, 1：增强识别服务器, 2：流媒体服务器, 3：NCE设备, 4：IVS设备, 5：文件服务器, 6：NVR设备)
     */
    private Integer serverType;
    /**
     * IP地址
     */
    private String ipAddress;
    /**
     * 通信端口
     */
    private Integer communicationPort;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户密码
     */
    private String userPassword;
    /**
     * 状态(1启用, 0禁用)
     */
    private Integer status;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private java.time.LocalDateTime createdTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.time.LocalDateTime updatedTime;
    /**
     * 删除标志(0表示存在，1表示删除)
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;
    /**
     * 项目主键
     */
    private String projectId;

    @TableField(exist = false)
    @Serial
    private static final long serialVersionUID = 1L;

}
