package com.allin.silas.dev.server.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.allin.silas.dev.server.adapter.dto.AddDevServerDto;
import com.allin.silas.dev.server.adapter.dto.EditDevServerDto;
import com.allin.silas.dev.server.app.entity.DevServer;
import com.allin.silas.dev.server.app.service.DevServerCommandService;
import com.allin.silas.dev.server.infra.repository.DevServerMapper;
import com.allin.view.base.constant.I18nConstants;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.config.i18n.I18nMessageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @author: 郭国勇
 * @Date: 2025/7/17 10:14
 * @Description
 */
@Component
public class DevServerCommandServiceImpl implements DevServerCommandService {

    private final DevServerMapper devServerMapper;

    public DevServerCommandServiceImpl(DevServerMapper devServerMapper) {
        this.devServerMapper = devServerMapper;
    }

    @Override
    public boolean createDevServer(AddDevServerDto addDevServerDto) {
        DevServer devServer = BeanUtil.toBean(addDevServerDto, DevServer.class);
        // 设备名称唯一
        if (devServerMapper.selectCount(new LambdaQueryWrapper<DevServer>().eq(DevServer::getServerName, devServer.getServerName())
                .eq(DevServer::getDeleted, 0)) > 0) {
            throw new ValidationFailureException("名称重复");
        }
        // 设备编号唯一
        if (devServerMapper.selectCount(new LambdaQueryWrapper<DevServer>().eq(DevServer::getServerNumber, devServer.getServerNumber())
                .eq(DevServer::getDeleted, 0)) > 0) {
            throw new ValidationFailureException("编码重复");
        }
        return devServerMapper.insert(devServer) > 0;
    }

    @Override
    public boolean updateDevServer(EditDevServerDto editDevServerDto) {
        String id = editDevServerDto.getId();
        DevServer old = devServerMapper.selectById(id);
        if (old == null) {
            throw new ValidationFailureException(I18nMessageUtil.getMessage(I18nConstants.DATA_NOT_EXIST));
        }
        // 设备名称唯一
        if (devServerMapper.selectCount(
                new LambdaQueryWrapper<DevServer>()
                .ne(DevServer::getId, id)
                .eq(DevServer::getDeleted, 0)
                .eq(DevServer::getServerName, editDevServerDto.getServerName())) > 0) {
            throw new ValidationFailureException(I18nMessageUtil.getMessage("名称重复"));
        }
        // 设备编号唯一
        if (devServerMapper.selectCount(new LambdaQueryWrapper<DevServer>()
                .ne(DevServer::getId, id)
                .eq(DevServer::getDeleted, 0)
                .eq(DevServer::getServerNumber, editDevServerDto.getServerNumber())) > 0) {
            throw new ValidationFailureException(I18nMessageUtil.getMessage("编码重复"));
        }
        BeanUtil.copyProperties(editDevServerDto, old, true);
        return devServerMapper.updateById(old) > 0;
    }

    @Override
    public boolean deleteDevServer(String id) {
        return devServerMapper.deleteById(id) > 0;
    }

    @Override
    public boolean updateDevServerStatus(String id, Integer status) {
        DevServer devServer = devServerMapper.selectById(id);
        devServer.setStatus(status);
        return devServerMapper.updateById(devServer) > 0;
    }
}
