package com.allin.silas.dev.server.adapter.controller;

import com.allin.silas.dev.server.adapter.dto.AddDevServerDto;
import com.allin.silas.dev.server.adapter.dto.EditDevServerDto;
import com.allin.silas.dev.server.adapter.query.DevServerQuery;
import com.allin.silas.dev.server.adapter.vo.DevServerVo;
import com.allin.silas.dev.server.app.service.DevServerCommandService;
import com.allin.silas.dev.server.app.service.DevServerQueryService;
import com.allin.view.base.constant.I18nConstants;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.EasyExcelOperUtils;
import com.allin.view.config.i18n.I18nMessageUtil;
import com.allin.view.config.redis.annotation.PreventDuplicateSubmit;
import com.allin.view.log.annotation.Log;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统配置-系统设备
 * <AUTHOR>
 * @since 2024/04/20 13:31:08
 */
@RestController
@Validated
@Slf4j
@RequestMapping("dev_server")
public class DevServerController {

    private final DevServerCommandService devServerCommandService;

    private final DevServerQueryService devServerQueryService;

    public DevServerController(DevServerCommandService devServerCommandService, DevServerQueryService devServerQueryService) {
        this.devServerCommandService = devServerCommandService;
        this.devServerQueryService = devServerQueryService;
    }

    /**
     * 新增系统设备
     */
    @PostMapping
    @PreventDuplicateSubmit
    @Log(title = "系统设备", operDesc = "新增系统设备")
    public Result<String> createDevServer(@Valid @RequestBody AddDevServerDto addDevServerDto) {
        boolean save = devServerCommandService.createDevServer(addDevServerDto);
        if (!save) {
            return Result.fail(I18nMessageUtil.getMessage(I18nConstants.OPERATE_FAIL));
        }
        return Result.ok();
    }

    /**
     * 修改系统设备
     */
    @PutMapping
    @Log(title = "系统设备", operDesc = "修改系统设备")
    public Result<String> updateDevServer(@Valid @RequestBody EditDevServerDto editDevServerDto) {
        boolean save = devServerCommandService.updateDevServer(editDevServerDto);
        if (!save) {
            return Result.fail(I18nMessageUtil.getMessage(I18nConstants.OPERATE_FAIL));
        }
        return Result.ok();
    }

    /**
     * 删除系统设备
     */
    @DeleteMapping("{id}")
    @Log(title = "系统设备", operDesc = "删除系统设备")
    public Result<String> deleteDevServer(@PathVariable String id) {
        boolean delete = devServerCommandService.deleteDevServer(id);
        if (!delete) {
            return Result.fail(I18nMessageUtil.getMessage(I18nConstants.OPERATE_FAIL));
        }
        return Result.ok();
    }

    /**
     * 分页查询系统设备
     */
    @GetMapping
    @Log(title = "系统设备", operDesc = "分页查询系统设备")
    public Result<PageData<DevServerVo>> getDevServerPage(
            PageParam pageParam,
            @Valid DevServerQuery devServerQuery) {
        Page<DevServerVo> page = pageParam.toPage();
        devServerQueryService.getDevServerPage(page, devServerQuery);
        PageData<DevServerVo> instance = PageData.getInstance(page);
        return Result.ok(instance);
    }

    /**
     * 查询详情系统设备
     */
    @GetMapping("{id}")
    @Log(title = "系统设备", operDesc = "查询系统设备")
    public Result<DevServerVo> getDevServer(@PathVariable String id) {
        DevServerVo devServerVo = devServerQueryService.getDevServer(id);
        return Result.ok(devServerVo);
    }

    /**
     * 导出系统设备
     */
    @GetMapping("export")
    @Log(title = "系统设备", operDesc = "导出系统设备")
    public void exportDevServer(@Valid DevServerQuery devServerQuery, HttpServletResponse response) {
        Page<DevServerVo> page = Page.of(-1, -1);
        devServerQueryService.getDevServerPage(page, devServerQuery);
        EasyExcelOperUtils.exportXlsx(response, page.getRecords(), DevServerVo.class, "系统设备");
    }

    /**
     * 启用/禁用设备
     */
    @PutMapping("status/{id}")
    @Log(title = "系统设备", operDesc = "启用/禁用设备")
    public Result<String> updateDevServerStatus(@PathVariable String id, @RequestParam Integer status) {
        boolean update = devServerCommandService.updateDevServerStatus(id, status);
        if (!update) {
            return Result.fail(I18nMessageUtil.getMessage(I18nConstants.OPERATE_FAIL));
        }
        return Result.ok();
    }

}
