package com.allin.silas.dev.server.adapter.dto;

import com.allin.silas.dev.server.app.enums.DevServerEnums;
import com.allin.view.base.validator.annotation.EnumValid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;

/**
 * DevServer 系统设备新增Dto
 *
 * <AUTHOR>
 * @since 2024/04/20 13:31:08
 */
@Data
public class AddDevServerDto implements Serializable {

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空")
    @Size(max = 100, message = "设备名称长度不能超过100个字符")
    private String serverName;
    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    @Size(max = 100, message = "设备编号长度不能超过100个字符")
    private String serverNumber;
    /**
     * 设备类型(0：应用服务器, 1：增强识别服务器, 2：流媒体服务器, 3：NCE设备, 4：IVS设备, 5：文件服务器, 6：NVR设备)
     */
    @EnumValid(target = DevServerEnums.class)
    private Integer serverType;
    /**
     * IP地址
     */
    @NotBlank(message = "IP地址不能为空")
    @Size(max = 50, message = "IP地址长度不能超过50个字符")
    @Pattern(regexp = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$", message = "无效的ip地址格式")
    private String ipAddress;
    /**
     * 通信端口
     */
    @NotNull(message = "通信端口不能为空")
    private Integer communicationPort;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户密码
     */
    private String userPassword;
    /**
     * 状态(1启用, 0禁用)
     */
    @NotNull(message = "状态(1启用, 0禁用)不能为空")
    private Integer status;
    /**
     * 项目主键
     */
    @NotBlank(message = "项目主键不能为空")
    @Size(max = 19, message = "项目主键长度不能超过19个字符")
    private String projectId;

}
