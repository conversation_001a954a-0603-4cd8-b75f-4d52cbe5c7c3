package com.allin.silas.dev.server.adapter.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 * DevServer 系统设备展示Vo
 *
 * <AUTHOR>
 * @since 2024/04/20 13:31:08
 */
@Data
@ColumnWidth(25)
public class DevServerVo implements Serializable {

    /**
     * 主键
     */
    @ExcelIgnore
    private String id;
    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String serverName;
    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String serverNumber;
    /**
     * 设备类型(0：应用服务器, 1：增强识别服务器, 2：流媒体服务器, 3：NCE设备, 4：IVS设备, 5：文件服务器, 6：NVR设备)
     */
    @ExcelProperty(value = "设备类型(0：应用服务器, 1：增强识别服务器, 2：流媒体服务器, 3：NCE设备, 4：IVS设备, 5：文件服务器, 6：NVR设备)")
    private Integer serverType;
    /**
     * IP地址
     */
    @ExcelProperty(value = "IP地址")
    private String ipAddress;
    /**
     * 通信端口
     */
    @ExcelProperty(value = "通信端口")
    private Integer communicationPort;
    /**
     * 用户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;
    /**
     * 用户密码
     */
    @ExcelProperty(value = "用户密码")
    private String userPassword;
    /**
     * 状态(1启用, 0禁用)
     */
    @ExcelProperty(value = "状态(1启用, 0禁用)")
    private Integer status;
    /**
     * 项目主键
     */
    @ExcelProperty(value = "项目主键")
    private String projectId;


}
