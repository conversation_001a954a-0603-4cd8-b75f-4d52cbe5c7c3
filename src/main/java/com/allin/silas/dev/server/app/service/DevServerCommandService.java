package com.allin.silas.dev.server.app.service;

import com.allin.silas.dev.server.adapter.dto.AddDevServerDto;
import com.allin.silas.dev.server.adapter.dto.EditDevServerDto;

/**
 * @author: 郭国勇
 * @Date: 2025/7/17 10:13
 * @Description
 */
public interface DevServerCommandService {

    boolean createDevServer(AddDevServerDto addDevServerDto);

    boolean updateDevServer(EditDevServerDto editDevServerDto);

    boolean deleteDevServer(String id);

    boolean updateDevServerStatus(String id, Integer status);
}
