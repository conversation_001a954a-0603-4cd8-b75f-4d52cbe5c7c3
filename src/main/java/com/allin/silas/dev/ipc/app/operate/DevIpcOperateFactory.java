package com.allin.silas.dev.ipc.app.operate;

import cn.hutool.extra.spring.SpringUtil;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * 用来创建不同设备操作策略的工厂类
 * <AUTHOR>
 */
@Component
public class DevIpcOperateFactory implements InitializingBean {
    private final HashMap<String, IDevIpcOperateService> devIpcOperateServiceHashMap = new HashMap<>();

    public IDevIpcOperateService getDevIpcOperateService(String ipcCompany) {
        return devIpcOperateServiceHashMap.get(ipcCompany);
    }

    @Override
    public void afterPropertiesSet() {
        SpringUtil.getBeansOfType(IDevIpcOperateService.class).forEach((k, v) -> {
            devIpcOperateServiceHashMap.put(v.ipcCompany(), v);
        });
    }
}
