package com.allin.silas.dev.ipc.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
public class EditDevRecTimeDto {
    /**
     * 增强识别服务端ip
     */
    @NotBlank(message = "ip不能为空")
    private String ip;
    /**
     * 增强识别服务端端口
     */
    @NotNull(message = "端口不能为空")
    private Integer port;
    /**
     * 开始时间 格式: HH:mm
     */
    @NotBlank(message = "开始时间不能为空")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "时间格式不正确")
    private String startTime;
    /**
     * 结束时间 格式: HH:mm
     */
    @NotBlank(message = "结束时间不能为空")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "时间格式不正确")
    private String endTime;
}
