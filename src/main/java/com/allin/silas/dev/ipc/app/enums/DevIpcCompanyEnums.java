package com.allin.silas.dev.ipc.app.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 相机厂商 (ALLINTECH(傲英创视)、HIK（海康威视）、DAHUA（大华）、UNIVIEW（宇视）、HUAWEI（华为）、SUNELL（苏奈尔）、
 * CANON（佳能）、CHANGHONG（长虹）、TIANDY（天地伟业）、PANASONIC（松下）、AXIS（安讯士）
 * HPVS(和普威视)
 */
public enum DevIpcCompanyEnums implements IEnums {
    ALLIN_TECH("ALLINTECH", "傲英创视"),
    HIK("HIK", "海康威视"),
    DA_HUA("DAHUA", "大华"),
    UNI_VIEW("UNIVIEW", "宇视"),
    UNI_VIEW2("UNIVIEW2", "宇视"),
    HUAWEI("HUAWEI", "华为"),
    SU_NELL("SUNELL", "苏奈尔"),
    CANON("CANON", "佳能"),
    CHANG_HONG("CHANGHONG", "长虹"),
    TI_ANDY("TIANDY", "天地伟业"),
    PANASONIC("PANASONIC", "松下"),
    HPVS("HPVS", "和普威视"),
    AXIS("AXIS", "安讯士"),
    BOSHI("BOSHI", "博士");;
    private final String code;
    private final String desc;
    DevIpcCompanyEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
