package com.allin.silas.dev.ipc.adapter.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.*;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import cn.idev.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import org.apache.poi.ss.usermodel.Font;

import java.io.Serializable;

/**
 * DevIpc 网络相机展示Vo
 *
 * <AUTHOR>
 * @since 2024/12/11 13:08:21
 */
@Data
@ColumnWidth(50)
@HeadFontStyle(fontHeightInPoints = 12)
@HeadRowHeight(27)
@ContentRowHeight(27)
@ContentFontStyle(fontHeightInPoints = 12)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER)
public class DevIpcExcelVo implements Serializable {
    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息")
    @HeadFontStyle(fontHeightInPoints = 12, color = Font.COLOR_RED)
    private String errorMsg;
    /**
     * 网络相机名称
     */
    @ExcelProperty(value = "*网络相机名称")
    private String ipcName;
    /**
     * 网络相机编码
     */
    @ExcelProperty(value = "*网络相机编码")
    private String ipcNum;
    /**
     * 1:固定枪机 2:云台枪机 3:球机  4:180度全景  5:半球/鱼眼相机 6:其他
     */
    @ExcelProperty(value = "*相机类型")
    private String ipcType;
    /**
     * 相机厂商 (ALLINTECH(傲英创视)、HIK（海康威视）、DAHUA（大华）、UNIVIEW（宇视）、HUAWEI（华为）、SUNELL（苏奈尔）、CANON（佳能）、CHANGHONG（长虹）、TIANDY（天地伟业）、PANASONIC（松下）、AXIS（安讯士）、其他)
     */
    @ExcelProperty(value = "*相机厂商")
    private String ipcCompany;
    /**
     * 流媒体服务器域名/ip
     */
    @ExcelProperty(value = "*直播流媒体服务器IP")
    private String mediaServerIp;
    /**
     * 流媒体服务器域名/ip
     */
    @ExcelProperty(value = "*直播流媒体服务器端口")
    private String mediaServerRestApiPort;
    /**
     * ip地址
     */
    @ExcelProperty(value = "*IP地址")
    private String ip;
    /**
     * 通道号默认1
     */
    @ExcelProperty(value = "*通道号")
    private String channel;
    /**
     * 预览码流默认第一码流
     */
    @ExcelProperty(value = "*码流")
    private String stream;
    /**
     * 登录名
     */
    @ExcelProperty(value = "*登录名")
    private String userName;
    /**
     * 登录密码
     */
    @ExcelProperty(value = "*登录密码")
    private String password;
    /**
     * 安装高度
     */
    @ExcelProperty(value = "安装高度")
    private String height;
    /**
     * 安装经度
     */
    @ExcelProperty(value = "安装经度")
    private String longitude;
    /**
     * 安装纬度
     */
    @ExcelProperty(value = "安装纬度")
    private String latitude;
    /**
     * 是否AI增强识别 1是 0否
     */
    @ExcelProperty(value = "*是否AI增强识别")
    private String isAiDetection;
    /**
     * AI增强识别服务器ID
     */
    @ExcelProperty(value = "AI增强识别服务器名称（来源系统设备模块）")
    private String aiDetectionServerId;
    /**
     * 回放NVR地址
     */
    @ExcelProperty(value = "回放NVR IP地址")
    private String backIp;
    /**
     * 回放用户
     */
    @ExcelProperty(value = "回放用户名")
    private String backUserName;
    /**
     * 回放密码
     */
    @ExcelProperty(value = "回放密码")
    private String backPassword;
    /**
     * 回放通道
     */
    @ExcelProperty(value = "回放通道")
    private String backChannel;
    /**
     * 回放码流默认第一码流
     */
    @ExcelProperty(value = "回放码流")
    private String backStream;

    /**
     * 排序
     */
    @ExcelProperty(value = "*排序")
    private String devSort;


    @ExcelProperty(value = "号段")
    private String numberSegment;

    /**
     * 流媒体服务器域名/ip
     */
    @ExcelProperty(value = "回放流媒体服务器IP")
    private String backMediaServerIp;
    /**
     * 流媒体服务器域名/ip
     */
    @ExcelProperty(value = "回放流媒体服务器端口")
    private String backMediaServerRestApiPort;
}
