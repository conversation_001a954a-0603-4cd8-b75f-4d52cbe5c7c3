package com.allin.silas.dev.ipc.app.operate.hk;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.allin.silas.dev.ipc.app.entity.Digest;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;

import java.util.List;

public class HkIpcDigestClient {

    private static final String WWW_AUTHENTICATE = "WWW-Authenticate";

    private static final String USER_NAME = "admin";

    private static final String PASSWORD = "Allin2018";

    private static Digest digest;

    public static Object putData(String ip, String uri, String data, boolean auth, String userName, String password) {
        String url = StrUtil.format("http://{}{}", ip, uri);
        if (StrUtil.isEmpty(userName)) {
            userName = USER_NAME;
        }
        if (StrUtil.isEmpty(password)) {
            password = PASSWORD;
        }
        HttpRequest request = HttpRequest.put(url).body(data);
        if (auth) {
            String authorization = digest.calculateDigestAuthorization();
            request.auth(authorization);
        }
        try (HttpResponse response = request.timeout(1000).execute()) {
            if (response.getStatus() == HttpStatus.UNAUTHORIZED.value() && !auth) {
                List<String> values = response.headerList(WWW_AUTHENTICATE);
                String wwwAuth = null;
                if (CollectionUtil.isNotEmpty(values)) {
                    if (values.size() == 1) {
                        wwwAuth = values.get(0);
                    } else if (values.size() == 2) {
                        wwwAuth = values.get(0) + ", " + values.get(1);
                    }
                }
                digest = new Digest(uri, HttpMethod.PUT.name(), wwwAuth);
                digest.setUserName(userName);
                digest.setPassword(password);
                return putData(ip, uri, data, true, userName, password);
            } else if (response.getStatus() == HttpStatus.OK.value()) {
                return response.body();
            } else {
                return response.body();
            }
        }
    }

    public static byte[] getData(String url, String uri, boolean auth, String userName, String password) {
        HttpRequest request = HttpRequest.get(url);
        if (StrUtil.isEmpty(userName)) {
            userName = USER_NAME;
        }
        if (StrUtil.isEmpty(password)) {
            password = PASSWORD;
        }
        if (auth) {
            String authorization = digest.calculateDigestAuthorization();
            request.auth(authorization);
        }
        try (HttpResponse response = request.timeout(3000).execute()) {
            if (response.getStatus() == HttpStatus.UNAUTHORIZED.value() && !auth) {
                digest = new Digest(uri, HttpMethod.GET.name(), userName, password, response.header(WWW_AUTHENTICATE));
                return getData(url, uri, true, userName, password);
            } else if (response.getStatus() == HttpStatus.OK.value()) {
                return response.bodyBytes();
            } else {
                return response.bodyBytes();
            }
        }
    }

    public static void ptz() {
        String ip = "************";
        String uri = "/ISAPI/PTZCtrl/channels/1/continuous";
        /**
         * 60 放大  -60缩小   0停止
         * 水平 String data = "<PTZData><pan>60</pan><tilt>0</tilt></PTZData>";
         * 垂直 String data = "<PTZData><pan>0</pan><tilt>60</tilt></PTZData>";
         * 变倍 String data = "<PTZData><zoom>60</zoom></PTZData>";
         * 变焦 String data = "<FocusData><focus>60</focus></FocusData>";
         */
        String data = "<PTZData><pan>60</pan><tilt>0</tilt></PTZData>";
        Object result = putData(ip, uri, data, false, "admin", "Allin2018");
        System.out.println(result);
    }

    public static byte[] snapShot1() throws Exception{
        String url = "************************************/onvif-http/snapshot?Profile_1";
        String[] splitX = url.split("://")[1].split("/");
        String[] urlArray = new String[splitX.length - 1];
        System.arraycopy(splitX, 1, urlArray, 0, urlArray.length);
        String uri = "/" + String.join("/", urlArray);
        //        FileUploadUtils.uploadBinaryFile(result, "5.jpg", "test");
        return getData(url, uri, false, "admin", "admin123~");
    }

    public static byte[] snapShot2(String url) throws Exception{
        String[] splitX = url.split("://")[1].split("/");
        String[] urlArray = new String[splitX.length - 1];
        System.arraycopy(splitX, 1, urlArray, 0, urlArray.length);
        String uri = "/" + String.join("/", urlArray);
        String[] userPassword = url.split("://")[1].split("@")[0].split(":");
        //        FileUploadUtils.uploadBinaryFile(result, "2.jpg", "test");
        return getData(url, uri, false, userPassword[0], userPassword[1]);
    }

}
