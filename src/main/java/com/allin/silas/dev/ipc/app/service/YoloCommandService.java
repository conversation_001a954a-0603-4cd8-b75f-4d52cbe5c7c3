package com.allin.silas.dev.ipc.app.service;

import com.allin.silas.dev.ipc.adapter.dto.AddDevRecDto;
import com.allin.silas.dev.ipc.adapter.dto.EditDevRecTimeDto;
import com.allin.silas.dev.ipc.adapter.dto.EditMaskDto;
import com.allin.silas.dev.ipc.adapter.vo.DevRecVo;
import java.util.List;

/**
 * 增强识别服务
 * @author: 郭国勇
 * @Date: 2025/7/17 11:04
 * @Description
 */
public interface YoloCommandService {

    /**
     * 新增设备
     */
    String addDevRec(AddDevRecDto addDevRecDto);

    /**
     * 设备白天夜晚时间
     */
    void setDevTime(EditDevRecTimeDto editDevRecTimeDto);

    /**
     * 删除某设备
     */
    void removeDev(String ip, Integer port, Integer devId);

    /**
     * 设置mask
     */
    void setMask(EditMaskDto editMaskDto);
}
