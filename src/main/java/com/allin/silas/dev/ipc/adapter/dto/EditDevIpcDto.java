package com.allin.silas.dev.ipc.adapter.dto;

import com.allin.view.base.enums.validator.IEnumValid;
import com.allin.view.dict.validator.DictValid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;

/**
 * DevIpc 编辑Dto
 *
 * <AUTHOR>
 * @since 2024/03/20 16:58:36
 */
@Data
public class EditDevIpcDto implements Serializable {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空")
    @Size(max = 64, message = "ID长度不能超过64个字符")
    private String id;
    /**
     * 网络相机名称
     */
    @NotBlank(message = "网络相机名称不能为空")
    @Size(max = 100, message = "网络相机名称长度不能超过100个字符")
    private String ipcName;
    /**
     * 网络相机编码
     */
    @NotBlank(message = "网络相机编码不能为空")
    @Size(max = 100, message = "网络相机编码长度不能超过100个字符")
    private String ipcNum;
    /**
     * 1:固定枪机 2:云台枪机 3:球机  4:180度全景  5:半球/鱼眼相机 6:其他
     */
    @NotNull(message = "1:固定枪机 2:云台枪机 3:球机  4:180度全景  5:半球/鱼眼相机 6:其他不能为空")
    @DictValid(dictType = "ipc_type", message = "枪击类型参数错误")
    private Integer ipcType;
    /**
     * 相机厂商 (ALLINTECH(傲英创视)、HIK（海康威视）、DAHUA（大华）、UNIVIEW（宇视）、HUAWEI（华为）、SUNELL（苏奈尔）、CANON（佳能）、CHANGHONG（长虹）、TIANDY（天地伟业）、PANASONIC（松下）、AXIS（安讯士）、其他)
     */
    @NotBlank(message = "相机厂商 (ALLINTECH(傲英创视)、HIK（海康威视）、DAHUA（大华）、UNIVIEW（宇视）、HUAWEI（华为）、SUNELL（苏奈尔）、CANON（佳能）、CHANGHONG（长虹）、TIANDY（天地伟业）、PANASONIC（松下）、AXIS（安讯士）、其他)不能为空")
    @Size(max = 20, message = "相机厂商 (ALLINTECH(傲英创视)、HIK（海康威视）、DAHUA（大华）、UNIVIEW（宇视）、HUAWEI（华为）、SUNELL（苏奈尔）、CANON（佳能）、CHANGHONG（长虹）、TIANDY（天地伟业）、PANASONIC（松下）、AXIS（安讯士）、其他)长度不能超过20个字符")
    @DictValid(dictType = "ipc_company", message = "相机厂商参数错误")
    private String ipcCompany;
    /**
     * ip地址
     */
    @NotBlank(message = "ip地址不能为空")
    @Size(max = 20, message = "ip地址长度不能超过20个字符")
    private String ip;
    /**
     * 通道号默认1
     */
    @NotNull(message = "通道号默认1不能为空")
    private Integer channel;
    /**
     * 预览码流默认第一码流
     */
    @NotNull(message = "预览码流默认第一码流不能为空")
    private Integer stream;
    /**
     * 登录名
     */
    @NotBlank(message = "登录名不能为空")
    @Size(max = 50, message = "登录名长度不能超过50个字符")
    private String userName;
    /**
     * 登录密码
     */
    @NotBlank(message = "登录密码不能为空")
    @Size(max = 50, message = "登录密码长度不能超过50个字符")
    private String password;
    /**
     * 安装高度
     */
    private java.math.BigDecimal height;
    /**
     * 安装经度
     */
    private String longitude;
    /**
     * 安装纬度
     */
    private String latitude;
    /**
     * 是否AI增强识别 1是 0否
     */
    @NotNull(message = "是否AI增强识别 1是 0否不能为空")
    @IEnumValid(target = com.allin.view.base.enums.BooleanEnums.class, message = "是否AI增强识别参数错误")
    private Integer isAiDetection;
    /**
     * 回放NVR地址
     */
    private String backIp;
    /**
     * 回放用户
     */
    private String backUserName;
    /**
     * 回放密码
     */
    private String backPassword;
    /**
     * 回放通道
     */
    private Integer backChannel;
    /**
     * 回放码流默认第一码流
     */
    private Integer backStream;
    /**
     * 项目主键
     */
    @NotBlank(message = "项目主键不能为空")
    @Size(max = 64, message = "项目主键长度不能超过64个字符")
    private String projectId;
    /**
     * 流媒体服务器域名/ip
     */
    @NotBlank(message = "流媒体服务器IP不能为空")
    @Pattern(regexp = "^$|^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$", message = "无效的ip地址格式")
    private String mediaServerIp;
    /**
     * 流媒体服务器RestApi端口
     */
    @NotNull(message = "流媒体服务器RestApi端口不能为空")
    private Integer mediaServerRestApiPort;
    /**
     * 流媒体web端口
     */
    @NotNull(message = "流媒体web端口不能为空")
    private Integer mediaServerWebPort;

    /**
     * AI增强识别服务器ID
     */
    private String aiDetectionServerId;
    /**
     * 自定义播放链接
     */
    private String rtspUrl;

    /**
     * 自定义播放通道
     */
    private Integer mediaDevId;

    /**
     * 设备排序
     */
    @NotNull(message = "设备排序不能为空")
    private Integer devSort;

    /**
     * 直播协议
     */
    private String liveStreamType = "rtsp";

    /**
     * 回放协议
     */
    private String backStreamType  = "rtsp";

    /**
     * 抓图链接
     */
    private String onvifUrl;

    /**
     * sip认证用户
     */
    private String sipUserName;

    /**
     * sip域
     */
    private String sipDomain;

    /**
     * sip通道
     */
    private String sipChannelId;

    /**
     * 回放sip认证用户
     */
    private String backSipUserName;

    /**
     * 回放sip域
     */
    private String backSipDomain;

    /**
     * 回放sip通道
     */
    private String backSipChannelId;

    /**
     * 号段
     */
    private Integer numberSegment;

    /**
     * 增强识别协议类型 JPEG/VIDEO
     */
    private String aiDetectType = "JPEG";

    /**
     * 播放方式 MEDIA_SERVER/JANUS
     */
    private String playType;

    /**
     * 回放服务器ip
     */
    private String backMediaServerIp;

    /**
     * 回放服务器ip
     */
    private Integer backMediaServerRestApiPort;

}
