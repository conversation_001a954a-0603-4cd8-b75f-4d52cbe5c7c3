package com.allin.silas.dev.ipc.app.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.allin.silas.dev.ipc.adapter.dto.AddDevRecDto;
import com.allin.silas.dev.ipc.adapter.dto.EditDevRecTimeDto;
import com.allin.silas.dev.ipc.adapter.dto.EditMaskDto;
import com.allin.silas.dev.ipc.adapter.vo.DevRecVo;
import com.allin.silas.dev.ipc.app.constants.YoloConstants;
import com.allin.silas.dev.ipc.app.service.YoloCommandService;
import com.allin.silas.dev.ipc.app.service.YoloQueryService;
import com.allin.view.base.utils.DigestHttpUtil;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 郭国勇
 * @Date: 2025/7/17 11:06
 * @Description
 */
@Component
public class YoloCommandServiceImpl implements YoloCommandService {

    private final YoloQueryService yoloQueryService;

    public YoloCommandServiceImpl(YoloQueryService yoloQueryService) {
        this.yoloQueryService = yoloQueryService;
    }

    @Override
    public String addDevRec(AddDevRecDto addDevRecDto) {
        List<DevRecVo> devRecList = yoloQueryService.getDevRecList(addDevRecDto.getIp(), addDevRecDto.getPort());
        // 判断是否已经包含该设备
        List<String> devNumList = devRecList.stream().map(DevRecVo::getDevNum).toList();
        if (devNumList.contains(addDevRecDto.getDevNum())) {
            return null;
        }
        String addr = StrUtil.format("{}{}:{}/{}", YoloConstants.REQUEST_HTTP_PREFIX, addDevRecDto.getIp(), addDevRecDto.getPort(), YoloConstants.ADD_DEVICE);
        String response = DigestHttpUtil.requestWithDigestAuth(addr, YoloConstants.USER_NAME, YoloConstants.PASS_WORD, Method.POST, 2000, addDevRecDto);
        return JSONUtil.parseObj(response).getStr("devId");
    }

    @Override
    public void setDevTime(EditDevRecTimeDto editDevRecTimeDto) {
        String addr = StrUtil.format("{}{}:{}/{}", YoloConstants.REQUEST_HTTP_PREFIX, editDevRecTimeDto.getIp(), editDevRecTimeDto.getPort(), YoloConstants.SET_TIME_RANGE);
        DigestHttpUtil.requestWithDigestAuth(addr, YoloConstants.USER_NAME, YoloConstants.PASS_WORD, Method.POST, 2000, editDevRecTimeDto);
    }

    @Override
    public void removeDev(String ip, Integer port, Integer devId) {
        String addr = StrUtil.format("{}{}:{}/{}?dev={}",YoloConstants.REQUEST_HTTP_PREFIX, ip, port, YoloConstants.REMOVE_DEVICE, devId);
        DigestHttpUtil.requestWithDigestAuth(addr, YoloConstants.USER_NAME, YoloConstants.PASS_WORD, Method.GET, 2000, null);
    }

    @Override
    public void setMask(EditMaskDto editMaskDto) {
        String addr = StrUtil.format("{}{}:{}/{}", YoloConstants.REQUEST_HTTP_PREFIX, editMaskDto.getIp(), editMaskDto.getPort(), YoloConstants.SET_MASK);
        DigestHttpUtil.requestWithDigestAuth(addr, YoloConstants.USER_NAME, YoloConstants.PASS_WORD, Method.POST, 2000, editMaskDto);
    }
}
