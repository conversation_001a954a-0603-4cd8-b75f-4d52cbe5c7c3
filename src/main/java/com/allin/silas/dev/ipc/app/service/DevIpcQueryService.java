package com.allin.silas.dev.ipc.app.service;

import com.allin.silas.dev.ipc.adapter.dto.AddDevIpcDto;
import com.allin.silas.dev.ipc.adapter.dto.EditDevIpcDto;
import com.allin.silas.dev.ipc.adapter.query.DevIpcQuery;
import com.allin.silas.dev.ipc.adapter.vo.DevIpcVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @author: 郭国勇
 * @Date: 2025/7/17 10:44
 * @Description
 */
public interface DevIpcQueryService {

    /**
     * 获取相机
     */
    DevIpcVo getIpc(String id);

    /**
     * 通过编码获取相机
     */
    DevIpcVo getIpcByNum(String ipcNum);

    /**
     * 获取抓图地址
     */
    String getOnvifUrl(String ipcId);

    /**
     * 获取视频流播放地址
     */
    String getVideoUrl(String ipcId);

    /**
     * 分页查询
     */
    void getIpcPage(Page<DevIpcVo> page, DevIpcQuery devIpcQuery);

}
