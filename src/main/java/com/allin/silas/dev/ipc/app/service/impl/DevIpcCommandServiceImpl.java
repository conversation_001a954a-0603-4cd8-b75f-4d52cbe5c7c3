package com.allin.silas.dev.ipc.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.dev.ipc.adapter.dto.AddDevIpcDto;
import com.allin.silas.dev.ipc.adapter.dto.AddDevRecDto;
import com.allin.silas.dev.ipc.adapter.dto.EditDevIpcDto;
import com.allin.silas.dev.ipc.adapter.vo.DevRecVo;
import com.allin.silas.dev.ipc.app.entity.DevIpc;
import com.allin.silas.dev.ipc.app.service.DevIpcCommandService;
import com.allin.silas.dev.ipc.app.service.DevIpcQueryService;
import com.allin.silas.dev.ipc.app.service.YoloCommandService;
import com.allin.silas.dev.ipc.app.service.YoloQueryService;
import com.allin.silas.dev.ipc.infra.repository.DevIpcMapper;
import com.allin.silas.dev.server.adapter.vo.DevServerVo;
import com.allin.silas.dev.server.app.service.DevServerQueryService;
import com.allin.view.base.constant.I18nConstants;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.config.i18n.I18nMessageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: 郭国勇
 * @Date: 2025/7/17 10:45
 * @Description
 */
@Component
@Slf4j
public class DevIpcCommandServiceImpl implements DevIpcCommandService {

    private final DevIpcMapper devIpcMapper;

    private final YoloCommandService yoloCommandService;

    private final YoloQueryService yoloQueryService;

    private final DevServerQueryService devServerQueryService;

    private final DevIpcQueryService devIpcQueryService;

    public DevIpcCommandServiceImpl(DevIpcMapper devIpcMapper, YoloCommandService yoloCommandService, YoloQueryService yoloQueryService, DevServerQueryService devServerQueryService, DevIpcQueryService devIpcQueryService) {
        this.devIpcMapper = devIpcMapper;
        this.yoloCommandService = yoloCommandService;
        this.yoloQueryService = yoloQueryService;
        this.devServerQueryService = devServerQueryService;
        this.devIpcQueryService = devIpcQueryService;
    }

    @Override
    public boolean createIpc(AddDevIpcDto addDevIpcDto) {
        DevIpc devIpc = BeanUtil.toBean(addDevIpcDto,  DevIpc.class);
        // 相机名称唯一性校验
        if (devIpcMapper.selectCount(new LambdaQueryWrapper<DevIpc>()
                .eq(DevIpc::getDeleted, 0)
                .eq(DevIpc::getChannel, devIpc.getChannel())
                .eq(DevIpc::getIpcName, devIpc.getIpcName())) > 0) {
            throw new ValidationFailureException(I18nMessageUtil.getMessage("相机名称重复"));
        }
        // 相机编号唯一性校验
        if (devIpcMapper.selectCount(new LambdaQueryWrapper<DevIpc>()
                .eq(DevIpc::getDeleted, 0)
                .eq(DevIpc::getIpcNum, devIpc.getIpcNum())
                .eq(DevIpc::getChannel, devIpc.getChannel())) > 0) {
            throw new ValidationFailureException(I18nMessageUtil.getMessage("相机编码重复"));
        }
        // 假如是AI增强识别 必须得选择AI服务器
        if (addDevIpcDto.getIsAiDetection() == 1 && StrUtil.isEmpty(addDevIpcDto.getAiDetectionServerId())) {
            throw new ValidationFailureException(I18nMessageUtil.getMessage("增强识别服务器不能为空"));
        }

        if (addDevIpcDto.getIsAiDetection() == 1) {
            devIpc.setIsYoloSynchronized(0);
        }

        return devIpcMapper.insert(devIpc) > 0;
    }

    @Override
    public boolean updateIpc(EditDevIpcDto editDevIpcDto) {
        String id = editDevIpcDto.getId();
        DevIpc old = devIpcMapper.selectById(id);
        if (old == null) {
            throw new ValidationFailureException(I18nMessageUtil.getMessage(I18nConstants.DATA_NOT_EXIST));
        }
        // 相机名称唯一性校验
        if (devIpcMapper.selectCount(new LambdaQueryWrapper<DevIpc>()
                .ne(DevIpc::getId, id)
                .eq(DevIpc::getChannel, editDevIpcDto.getChannel())
                .eq(DevIpc::getIpcName, editDevIpcDto.getIpcName())
                .eq(DevIpc::getDeleted, 0)) > 0) {
            throw new ValidationFailureException(I18nMessageUtil.getMessage("相机名称重复"));
        }
        // 相机编号唯一性校验
        if (devIpcMapper.selectCount(new LambdaQueryWrapper<DevIpc>()
                .ne(DevIpc::getId, id)
                .eq(DevIpc::getChannel, editDevIpcDto.getChannel())
                .eq(DevIpc::getIpcNum, editDevIpcDto.getIpcNum())
                .eq(DevIpc::getDeleted, 0)) > 0) {
            throw new ValidationFailureException(I18nMessageUtil.getMessage("相机编码重复"));
        }
        // 假如是AI增强识别 必须得选择AI服务器
        if (editDevIpcDto.getIsAiDetection() == 1 && StrUtil.isEmpty(editDevIpcDto.getAiDetectionServerId())) {
            throw new ValidationFailureException(I18nMessageUtil.getMessage("增强识别服务器不能为空"));
        }

        handleAiServer(editDevIpcDto, old);

        return devIpcMapper.updateById(old) > 0;
    }

    private void handleAiServer(EditDevIpcDto newIpc, DevIpc oldIpc) {
        // 判断是否从 AI 检测状态变为非 AI 检测状态
        if (oldIpc.getIsAiDetection() == 1 && newIpc.getIsAiDetection() != 1) {
            removeOldDevice(oldIpc);
            oldIpc.setIsYoloSynchronized(0);
        }

        // 判断是否启用 AI 检测
        if (newIpc.getIsAiDetection() == 1) {
            handleAiDetectionEnabled(newIpc, oldIpc);
        }

        // 合并新旧数据（忽略 aiDetectionServerId 字段）
        BeanUtil.copyProperties(newIpc, oldIpc, "aiDetectionServerId", "isYoloSynchronized", "id", "aiDetectionDevId");
    }

    private void handleAiDetectionEnabled(EditDevIpcDto newIpc, DevIpc oldIpc) {
        // 如果从非 AI 检测切换到 AI 检测，初始化相关字段
        if (oldIpc.getIsAiDetection() == 0) {
            oldIpc.setAiDetectionServerId(newIpc.getAiDetectionServerId());
            oldIpc.setIsYoloSynchronized(0); // 设置为未同步状态
        }

        // 如果 AI 检测已经启用，检查服务器 ID 或设备信息是否变化
        if (oldIpc.getIsAiDetection() == 1) {
            boolean isServerChanged = !newIpc.getAiDetectionServerId().equals(oldIpc.getAiDetectionServerId());
            boolean isDeviceChanged = isDeviceInfoChanged(newIpc, oldIpc);

            if (isServerChanged || isDeviceChanged) {
                removeOldDevice(oldIpc);
                oldIpc.setAiDetectionServerId(newIpc.getAiDetectionServerId());
                oldIpc.setIsYoloSynchronized(0); // 更新同步状态
            }
        }
    }

    private void removeOldDevice(DevIpc oldIpc) {
        try {
            // 获取设备服务器信息
            DevServerVo devServer = devServerQueryService.getDevServer(oldIpc.getAiDetectionServerId());
            List<DevRecVo> devRecList = yoloQueryService.getDevRecList(devServer.getIpAddress(), devServer.getCommunicationPort());
            // 把异常设备恢复正常
            if (CollectionUtil.isNotEmpty(devRecList)) {
                Optional<DevRecVo> first = devRecList.stream().filter(e -> e.getDevNum().equals(oldIpc.getId())).findFirst();
                first.ifPresent(devRecVo -> {
                    try {
                        // 处理异常
                        oldIpc.setAiDetectionDevId(-1);

                        yoloCommandService.removeDev(devServer.getIpAddress(), devServer.getCommunicationPort(), devRecVo.getDevId());
                    } catch (Exception e) {
                        log.error("Failed to remove old device (Dev ID: {}): {}", oldIpc.getId(), e.getMessage(), e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("Failed to retrieve or remove device (Dev ID: {}): {}", oldIpc.getId(), e.getMessage(), e);
        }
    }


    private boolean isDeviceInfoChanged(EditDevIpcDto newIpc, DevIpc oldIpc) {
        // 检查设备 IP、用户名、密码、通道是否变化
        return !newIpc.getIp().equals(oldIpc.getIp()) ||
                !newIpc.getUserName().equals(oldIpc.getUserName()) ||
                !newIpc.getPassword().equals(oldIpc.getPassword()) ||
                !newIpc.getChannel().equals(oldIpc.getChannel())||
                !newIpc.getAiDetectType().equals(oldIpc.getAiDetectType());
    }

    @Override
    public boolean deleteIpc(String id) {
        return devIpcMapper.deleteById(id) > 0;
    }

    @Override
    public void batchSaveOrUpdate(List<DevIpc> successList, String projectId, String userId) {
        if (CollectionUtil.isEmpty(successList)) {
            return;
        }
        // 去重：过滤出 ipcNum 唯一的 DevIpc 列表
        List<DevIpc> uniqueList = successList.stream()
                .collect(Collectors.toMap(
                        DevIpc::getIpcNum, // 使用 ipcNum 作为键
                        Function.identity(), // 使用 DevIpc 对象作为值
                        (existing, replacement) -> existing // 保留第一个重复的对象
                ))
                .values()
                .stream()
                .toList();

        // 查询数据库中未删除且属于指定项目的 DevIpc 列表
        Map<String, DevIpc> ipcMap =
                devIpcMapper.selectList(new LambdaQueryWrapper<DevIpc>()
                                .eq(DevIpc::getDeleted, false)
                                .eq(DevIpc::getProjectId, projectId)
                        )
                .stream()
                .collect(Collectors.toMap(
                        DevIpc::getIpcNum,
                        Function.identity(),
                        (existingValue, newValue) -> existingValue));

        // 处理保存或更新的列表
        List<DevIpc> saveOrUpdateList = uniqueList.stream().map(devIpc -> {
            DevIpc old = ipcMap.get(devIpc.getIpcNum());
            if (old != null) {
                // 处理增强识别相关逻辑
                handleAiServer(BeanUtil.toBean(devIpc, EditDevIpcDto.class), old);
                return old;
            } else {
                return devIpc;
            }
        }).collect(Collectors.toList());

        // 批量保存或更新
        devIpcMapper.insertOrUpdate(saveOrUpdateList);
    }

    @Override
    public String addAiChannel(DevIpc devIpc) {
        DevServerVo devServer = devServerQueryService.getDevServer(devIpc.getAiDetectionServerId());
        if (ObjectUtil.isEmpty(devIpc)) return null;
        AddDevRecDto addDevRecDto = new AddDevRecDto();
        addDevRecDto.setIp(devServer.getIpAddress());
        addDevRecDto.setPort(devServer.getCommunicationPort());
        addDevRecDto.setDevNum(devIpc.getId());
        String aiDetectType = devIpc.getAiDetectType();
        if ("JPEG".equalsIgnoreCase(aiDetectType)) {
            String url = devIpcQueryService.getOnvifUrl(devIpc.getId());
            addDevRecDto.setDevUrl(url);
        } else {
            String url = devIpcQueryService.getVideoUrl(devIpc.getId());
            addDevRecDto.setDevUrl(url);
        }
        addDevRecDto.setDevId(0);
        addDevRecDto.setStatus(1);
        return yoloCommandService.addDevRec(addDevRecDto);
    }
}
