package com.allin.silas.dev.ipc.app.operate.pojo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: 郭国勇
 * @Date: 2025/7/16 13:46
 * @Description
 */
@Data
public class IpcConfigDto {
    @NotNull
    private Integer videoResolutionWidth;
    @NotNull
    private Integer videoResolutionHeight;
    @NotNull
    private Integer govLength;
    @NotNull
    private Integer constantBitRate;
    @NotBlank
    private String videoCodecType;
    @NotNull
    private Integer maxFrameRate = 2500;
}
