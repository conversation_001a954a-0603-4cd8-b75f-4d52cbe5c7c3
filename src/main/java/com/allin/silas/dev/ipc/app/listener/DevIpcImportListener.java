package com.allin.silas.dev.ipc.app.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.allin.silas.dev.ipc.adapter.vo.DevIpcExcelVo;
import com.allin.silas.dev.ipc.app.entity.DevIpc;
import com.allin.silas.dev.ipc.app.service.DevIpcCommandService;
import com.allin.silas.dev.ipc.app.service.DevIpcQueryService;
import com.allin.silas.dev.server.app.entity.DevServer;
import com.allin.silas.dev.server.infra.repository.DevServerMapper;
import com.allin.view.base.utils.ip.IpV4Utils;
import com.allin.view.dict.pojo.vo.SysDictDataVo;
import com.allin.view.dict.service.SysDictTypeService;
import com.allin.view.file.pojo.vo.FileVo;
import com.allin.view.file.util.ImportExcelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 网络相机导入监听器
 *
 * <AUTHOR>
 * @since 2024/12/11 13:08:21
 */
@Slf4j
public class DevIpcImportListener extends AnalysisEventListener<DevIpcExcelVo> {

    @Getter
    @Setter
    private String projectId;

    List<DevIpc> successList;

    // 失败的数据
    List<DevIpcExcelVo> errorVoList;

    // 成功数量
    @Getter
    private Long successCount;

    // 失败数量
    @Getter
    private Long errorCount;

    @Getter
    private String errorLogId;

    List<SysDictDataVo> ipcTypeList;

    List<SysDictDataVo> ipcCompanyList;

    List<DevServer> devServers;

    private final DevIpcQueryService devIpcQueryService = SpringUtil.getBean(DevIpcQueryService.class);

    private final DevIpcCommandService devIpcCommandService = SpringUtil.getBean(DevIpcCommandService.class);

    @Getter
    @Setter
    private String userId;

    public DevIpcImportListener(String projectId, String userId) {
        this.projectId = projectId;
        this.userId = userId;
        successList = new ArrayList<>();
        errorVoList = new ArrayList<>();
        successCount = 0L;
        errorCount = 0L;
        SysDictTypeService sysDictTypeService = SpringUtil.getBean(SysDictTypeService.class);
        ipcTypeList = sysDictTypeService.listDictDataByType("ipc_type");
        ipcCompanyList = sysDictTypeService.listDictDataByType("ipc_company");
        DevServerMapper devServerService = SpringUtil.getBean(DevServerMapper.class);
        devServers = devServerService.selectList(new LambdaQueryWrapper<DevServer>().in(DevServer::getServerType, 1, 7)
                .eq(DevServer::getProjectId, projectId)
                .eq(DevServer::getDeleted, false));
    }

    @Override
    public void invoke(DevIpcExcelVo devIpcExcelVo, AnalysisContext analysisContext) {
        StringBuilder error = new StringBuilder();
        DevIpc saveModel = new DevIpc();
        // 校验网络相机名称
        if (ObjectUtil.isEmpty(devIpcExcelVo.getIpcName())) {
            error.append("网络相机名称不能为空;");
        } else {
            saveModel.setIpcName(devIpcExcelVo.getIpcName());
        }
        // 校验网络相机编码
        if (ObjectUtil.isEmpty(devIpcExcelVo.getIpcNum())) {
            error.append("网络相机编码不能为空;");
        } else {
            saveModel.setIpcNum(devIpcExcelVo.getIpcNum());
        }
        // 校验1:固定枪机 2:云台枪机 3:球机  4:180度全景  5:半球/鱼眼相机 6:其他
        if (ObjectUtil.isEmpty(devIpcExcelVo.getIpcType())) {
            error.append("相机类型不能为空;");
        } else {
            List<SysDictDataVo> list = ipcTypeList.stream().filter(e -> e.getLabel().equals(devIpcExcelVo.getIpcType())).toList();
            if (CollectionUtil.isEmpty(list)) {
                error.append("相机类型在字典管理ipc_type类型中不存在;");
            } else {
                saveModel.setIpcType(Integer.valueOf(list.get(0).getValue()));
            }
        }
        // 校验相机厂商 (ALLINTECH(傲英创视)、HIK（海康威视）、DAHUA（大华）、UNIVIEW（宇视）、HUAWEI（华为）、SUNELL（苏奈尔）、CANON（佳能）、CHANGHONG（长虹）、TIANDY（天地伟业）、PANASONIC（松下）、AXIS（安讯士）、其他)
        if (ObjectUtil.isEmpty(devIpcExcelVo.getIpcCompany())) {
            error.append("相机厂商");
        } else {
            List<SysDictDataVo> list = ipcCompanyList.stream().filter(e -> e.getLabel().equals(devIpcExcelVo.getIpcCompany())).toList();
            if (CollectionUtil.isEmpty(list)) {
                error.append("相机厂商在字典管理ipc_company类型中不存在;");
            } else {
                saveModel.setIpcCompany(list.get(0).getValue());
            }
        }
        // 校验ip地址
        if (ObjectUtil.isEmpty(devIpcExcelVo.getIp())) {
            error.append("相机IP地址能为空;");
        } else {
            if (!IpV4Utils.isValidIPv4(devIpcExcelVo.getIp())) {
                error.append("非法相机IP地址;");
            } else {
                saveModel.setIp(devIpcExcelVo.getIp());
            }
        }
        // 校验通道号默认1
        if (ObjectUtil.isEmpty(devIpcExcelVo.getChannel())) {
            error.append("通道号不能为空;");
        } else {
            try {
                Integer integer = Integer.valueOf(devIpcExcelVo.getChannel());
                saveModel.setChannel(integer);
            } catch (Exception e) {
                error.append("通道号格式错误;");
            }
        }
        // 校验预览码流默认第一码流
        if (ObjectUtil.isEmpty(devIpcExcelVo.getStream())) {
            error.append("码流不能为空;");
        } else {
            try {
                Integer integer = Integer.valueOf(devIpcExcelVo.getStream());
                saveModel.setStream(integer);
            } catch (Exception e) {
                error.append("码流格式错误;");
            }
        }
        // 校验登录名
        if (ObjectUtil.isEmpty(devIpcExcelVo.getUserName())) {
            error.append("登录名不能为空;");
        } else {
            saveModel.setUserName(devIpcExcelVo.getUserName());
        }
        // 校验登录密码
        if (ObjectUtil.isEmpty(devIpcExcelVo.getPassword())) {
            error.append("登录密码不能为空;");
        } else {
            saveModel.setPassword(devIpcExcelVo.getPassword());
        }
        if (ObjectUtil.isNotEmpty(devIpcExcelVo.getHeight())) {
            try {
                BigDecimal bigDecimal = new BigDecimal(devIpcExcelVo.getHeight());
                saveModel.setHeight(bigDecimal);
            } catch (Exception e) {
                error.append("安装高度格式错误;");
            }
        }
        // 校验是否AI增强识别 1是 0否
        if (ObjectUtil.isEmpty(devIpcExcelVo.getIsAiDetection())) {
            error.append("是否AI增强识别不能为空");
        } else {
            saveModel.setIsAiDetection("是".equals(devIpcExcelVo.getIsAiDetection()) ? 1 : 0);

            if (saveModel.getIsAiDetection() == 1) {
                if (StrUtil.isEmpty(devIpcExcelVo.getAiDetectionServerId())) {
                    error.append("是AI增强识别，必须填写增强识别服务器名称");
                } else {
                    List<DevServer> list = devServers.stream().filter(e -> e.getServerName().equals(devIpcExcelVo.getAiDetectionServerId())).toList();
                    if (CollectionUtil.isEmpty(list)) {
                        error.append("AI增强识别服务器名称不存在");
                    } else {
                        saveModel.setAiDetectionServerId(list.get(0).getId());
                        saveModel.setIsYoloSynchronized(0);
                    }
                }
            }
        }
        if (ObjectUtil.isNotEmpty(devIpcExcelVo.getBackChannel())) {
            try {
                Integer integer = Integer.valueOf(devIpcExcelVo.getBackChannel());
                saveModel.setBackChannel(integer);
            } catch (Exception e) {
                error.append("回放通道格式错误;");
            }
        }
        if (ObjectUtil.isNotEmpty(devIpcExcelVo.getBackStream())) {
            try {
                Integer integer = Integer.valueOf(devIpcExcelVo.getBackStream());
                saveModel.setBackStream(integer);
            } catch (Exception e) {
                error.append("回放码流格式错误;");
            }
        }
        // 校验流媒体服务器域名/ip
        if (ObjectUtil.isEmpty(devIpcExcelVo.getMediaServerIp())) {
            error.append("流媒体服务器ip不能为空;");
        } else {
            if (!IpV4Utils.isValidIPv4(devIpcExcelVo.getMediaServerIp())) {
                error.append("非法流媒体服务器IP;");
            } else {
                saveModel.setMediaServerIp(devIpcExcelVo.getMediaServerIp());
            }
        }
        // 校验流媒体服务器端口
        if (ObjectUtil.isEmpty(devIpcExcelVo.getMediaServerRestApiPort())) {
            error.append("流媒体服务器端口不能为空;");
        } else {
            try {
                Integer integer = Integer.valueOf(devIpcExcelVo.getMediaServerRestApiPort());
                saveModel.setMediaServerRestApiPort(integer);
            } catch (Exception e) {
                error.append("流媒体服务器端口格式错误;");
            }
        }
        if (StrUtil.isNotEmpty(devIpcExcelVo.getBackIp())) {
            if (!IpV4Utils.isValidIPv4(devIpcExcelVo.getBackIp())) {
                error.append("非法回放IP;");
            } else {
                saveModel.setBackIp(devIpcExcelVo.getBackIp());
            }
        }
        if (ObjectUtil.isNotEmpty(devIpcExcelVo.getDevSort())) {
            try {
                Integer integer = Integer.valueOf(devIpcExcelVo.getDevSort());
                saveModel.setDevSort(integer);
            } catch (Exception e) {
                error.append("排序格式错误;");
            }
        } else {
            saveModel.setDevSort(1);
        }
        if (ObjectUtil.isNotEmpty(devIpcExcelVo.getNumberSegment())) {
            try {
                saveModel.setNumberSegment(Integer.valueOf(devIpcExcelVo.getNumberSegment()));
            } catch (Exception e) {
                error.append("号段格式错误;");
            }
        }
        // 校验回放流媒体服务器域名/ip
        if (!ObjectUtil.isEmpty(devIpcExcelVo.getBackMediaServerIp())) {
            if (!IpV4Utils.isValidIPv4(devIpcExcelVo.getBackMediaServerIp())) {
                error.append("非法回放流媒体服务器IP;");
            } else {
                saveModel.setBackMediaServerIp(devIpcExcelVo.getBackMediaServerIp());
            }
        }
        // 校验回放流媒体服务器端口
        if (ObjectUtil.isEmpty(devIpcExcelVo.getBackMediaServerRestApiPort())) {
            try {
                Integer integer = Integer.valueOf(devIpcExcelVo.getBackMediaServerRestApiPort());
                saveModel.setBackMediaServerRestApiPort(integer);
            } catch (Exception e) {
                error.append("回放流媒体服务器端口格式错误;");
            }
        }

        saveModel.setBackUserName(devIpcExcelVo.getBackUserName());
        saveModel.setBackPassword(devIpcExcelVo.getBackPassword());
        saveModel.setLongitude(devIpcExcelVo.getLongitude());
        saveModel.setLatitude(devIpcExcelVo.getLatitude());
        saveModel.setProjectId(getProjectId());
        saveModel.setMediaServerWebPort(9194);
        if (StrUtil.isEmpty(error.toString())) {
            successList.add(saveModel);
            if (successList.size() == 2000) {
                saveBatch(successList);
                successList.clear();
            }
        } else {
            devIpcExcelVo.setErrorMsg(error.toString());
            errorVoList.add(devIpcExcelVo);
        }
    }

    private void saveBatch(List<DevIpc> successList) {
        if (!successList.isEmpty()) {
            successCount += successList.size();
            devIpcCommandService.batchSaveOrUpdate(successList, getProjectId(), getUserId());
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 例如，可以在这里触发一些后续的处理逻辑
        if (!successList.isEmpty()) {
            saveBatch(successList);
        }
        if (!errorVoList.isEmpty()) {
            errorCount = (long) errorVoList.size();
            final FileVo fileVo = ImportExcelUtils.createErrorExcelAndUpload(errorVoList);
            errorLogId = fileVo.getId();
        }
    }
}
