package com.allin.silas.dev.ipc.adapter.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.allin.view.base.enums.BooleanEnums;
import com.allin.view.base.enums.serializer.IEnumsChangeDesc;
import com.allin.view.dict.serializer.annotation.DictToLabel;
import lombok.Data;

import java.io.Serializable;

/**
 * DevIpc 展示Vo
 *
 * <AUTHOR>
 * @since 2024/03/20 16:58:36
 */
@Data
@ColumnWidth(25)
public class DevIpcVo implements Serializable {

    /**
     * ID
     */
    @ExcelIgnore
    private String id;
    /**
     * 网络相机名称
     */
    @ExcelProperty(value = "网络相机名称")
    private String ipcName;
    /**
     * 网络相机编码
     */
    @ExcelProperty(value = "网络相机编码")
    private String ipcNum;
    /**
     * 1:固定枪机 2:云台枪机 3:球机  4:180度全景  5:半球/鱼眼相机 6:其他
     */
    @ExcelProperty(value = "1:固定枪机 2:云台枪机 3:球机  4:180度全景  5:半球/鱼眼相机 6:其他")
    @DictToLabel(dictType = "ipc_type", key = "ipcTypeValue")
    private Integer ipcType;
    /**
     * 相机厂商 (ALLINTECH(傲英创视)、HIK（海康威视）、DAHUA（大华）、UNIVIEW（宇视）、HUAWEI（华为）、SUNELL（苏奈尔）、CANON（佳能）、CHANGHONG（长虹）、TIANDY（天地伟业）、PANASONIC（松下）、AXIS（安讯士）、其他)
     */
    @ExcelProperty(value = "相机厂商 (ALLINTECH(傲英创视)、HIK（海康威视）、DAHUA（大华）、UNIVIEW（宇视）、HUAWEI（华为）、SUNELL（苏奈尔）、CANON（佳能）、CHANGHONG（长虹）、TIANDY（天地伟业）、PANASONIC（松下）、AXIS（安讯士）、其他)")
    @DictToLabel(dictType = "ipc_company", key = "ipcCompanyValue")
    private String ipcCompany;
    /**
     * 关联设备编号
     */
    @ExcelIgnore
    private String devViewId;
    /**
     * 关联设备名称
     */
    @ExcelProperty(value = "关联设备名称")
    private String devViewName;

    /**
     * 关联设备编码
     */
    @ExcelProperty(value="关联设备编码")
    private String devViewNum;

    /**
     * ip地址
     */
    @ExcelProperty(value = "ip地址")
    private String ip;
    /**
     * 通道号默认1
     */
    @ExcelProperty(value = "通道号默认1")
    private Integer channel;
    /**
     * 预览码流默认第一码流
     */
    @ExcelProperty(value = "预览码流默认第一码流")
    private Integer stream;
    /**
     * 登录名
     */
    @ExcelProperty(value = "登录名")
    private String userName;
    /**
     * 登录密码
     */
    @ExcelProperty(value = "登录密码")
    private String password;
    /**
     * 安装高度
     */
    @ExcelProperty(value = "安装高度")
    private java.math.BigDecimal height;
    /**
     * 安装经度
     */
    @ExcelProperty(value = "安装经度")
    private String longitude;
    /**
     * 安装纬度
     */
    @ExcelProperty(value = "安装纬度")
    private String latitude;
    /**
     * 是否AI增强识别 1是 0否
     */
    @ExcelProperty(value = "是否AI增强识别 1是 0否")
    @IEnumsChangeDesc(enums = BooleanEnums.class, key="isAiDetectionDesc")
    private Integer isAiDetection;
    /**
     * 回放NVR地址
     */
    @ExcelProperty(value = "回放NVR地址")
    private String backIp;
    /**
     * 回放用户
     */
    @ExcelProperty(value = "回放用户")
    private String backUserName;
    /**
     * 回放密码
     */
    @ExcelProperty(value = "回放密码")
    private String backPassword;
    /**
     * 回放通道
     */
    @ExcelProperty(value = "回放通道")
    private Integer backChannel;
    /**
     * 回放码流默认第一码流
     */
    @ExcelProperty(value = "回放码流默认第一码流")
    private Integer backStream;
    /**
     * 项目主键
     */
    @ExcelProperty(value = "项目主键")
    private String projectId;

    /**
     * 流媒体服务器域名/ip
     */
    @ExcelProperty(value = "流媒体服务器域名/ip")
    private String mediaServerIp;
    /**
     * 流媒体服务器RestApi端口
     */
    @ExcelProperty(value = "流媒体服务器RestApi端口")
    private Integer mediaServerRestApiPort;
    /**
     * 流媒体web端口
     */
    @ExcelProperty(value = "流媒体web端口")
    private Integer mediaServerWebPort;

    /**
     * 增强识别服务器名称
     */
    @ExcelProperty(value = "增强识别服务器名称")
    private String aiDetectionServerName;

    /**
     * AI增强识别服务器ID
     */
    @ExcelIgnore
    private String aiDetectionServerId;

    /**
     * ivs服务器ID
     */
    @ExcelIgnore
    private String ivsServerId;

    /**
     * 同步给增强识别状态（1 已同步 0 未同步 -1: 后期围界更新）
     */
    @ExcelIgnore
    private Integer isYoloSynchronized;

    /**
     * 增强识别devId
     */
    @ExcelIgnore
    private Integer aiDetectionDevId;

    /**
     * 增强识别服务id
     */
    @ExcelIgnore
    private String aiDetectionServerIp;

    /**
     * rtspUrl
     */
    @ExcelIgnore
    private String rtspUrl;

    /**
     * 自定义播放通道
     */
    @ExcelIgnore
    private Integer mediaDevId;

    /**
     * 设备排序
     */
    @ExcelIgnore
    private Integer devSort;

    /**
     * 是否存在ROI
     */
    @ExcelIgnore
    private Boolean hasRoi;

    /**
     * 直播协议
     */
    @ExcelProperty("直播协议")
    private String liveStreamType;

    /**
     * 回放协议
     */
    @ExcelProperty("回放协议")
    private String backStreamType;

    /**
     * 抓图链接
     */
    @ExcelIgnore
    private String onvifUrl;

    /**
     * sip认证用户
     */
    @ExcelProperty("sip认证用户")
    private String sipUserName;

    /**
     * sip域
     */
    @ExcelProperty("sip域")
    private String sipDomain;

    /**
     * sip通道
     */
    @ExcelProperty("sip通道")
    private String sipChannelId;

    /**
     * 回放sip认证用户
     */
    @ExcelProperty("回放sip认证用户")
    private String backSipUserName;

    /**
     * 回放sip域
     */
    @ExcelProperty("回放sip域")
    private String backSipDomain;

    /**
     * 回放sip通道
     */
    @ExcelProperty("回放sip通道")
    private String backSipChannelId;

    /**
     * 分组名称
     */
    @ExcelProperty("分组名称")
    private String groupName;

    /**
     * 号段
     */
    @ExcelProperty("号段")
    private Integer numberSegment;

    /**
     * 增强识别协议类型 JPEG/VIDEO
     */
    private String aiDetectType = "JPEG";
    /**
     * 播放方式 MEDIA_SERVER/JANUS
     */
    private String playType;

    /**
     * 直播视频流
     */
    private String streamUrl;

    /**
     * 回放服务器ip
     */
    private String backMediaServerIp;

    /**
     * 回放服务器ip
     */
    private Integer backMediaServerRestApiPort;

}
