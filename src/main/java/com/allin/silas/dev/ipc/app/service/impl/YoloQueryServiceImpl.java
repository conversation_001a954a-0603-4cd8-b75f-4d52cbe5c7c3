package com.allin.silas.dev.ipc.app.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.allin.silas.dev.ipc.adapter.vo.DevRecVo;
import com.allin.silas.dev.ipc.app.constants.YoloConstants;
import com.allin.silas.dev.ipc.app.service.YoloQueryService;
import com.allin.view.base.utils.DigestHttpUtil;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 郭国勇
 * @Date: 2025/7/17 11:17
 * @Description
 */
@Component
public class YoloQueryServiceImpl implements YoloQueryService {

    @Override
    public List<DevRecVo> getDevRecList(String ip, Integer port) {
        String addr = StrUtil.format("{}{}:{}/{}", YoloConstants.REQUEST_HTTP_PREFIX, ip, port, YoloConstants.GET_DEVICES);
        String response = DigestHttpUtil.requestWithDigestAuth(addr, YoloConstants.USER_NAME, YoloConstants.PASS_WORD, Method.GET, 2000, null);
        return JSONUtil.parseArray(response).toList(DevRecVo.class);
    }

}
