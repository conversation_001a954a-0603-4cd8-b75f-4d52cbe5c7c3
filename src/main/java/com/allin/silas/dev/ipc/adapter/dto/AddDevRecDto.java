package com.allin.silas.dev.ipc.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AddDevRecDto {

    /**
     * 增强识别服务端ip
     */
    @NotBlank(message = "ip不能为空")
    private String ip;

    /**
     * 增强识别服务端端口
     */
    @NotNull(message = "端口不能为空")
    private Integer port;

    /**
     * onvif抓图地址
     */
    @NotBlank(message = "onvif抓图地址不能为空")
    private String devUrl;

    /**
     * 设备编号(传相机主键)
     */
    @NotBlank(message = "设备编号不能为空")
    private String devNum;


    /**
     * 如果为0，则自动生成一个唯一的ID，否则，直接使用该ID。
     */
    private Integer devId;

    /**
     * 状态 0：禁用，1：启用
     */
    private Integer status;

}
