package com.allin.silas.dev.ipc.app.operate.hk;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.Method;
import com.allin.silas.dev.ipc.app.entity.DevIpc;
import com.allin.silas.dev.ipc.app.enums.DevIpcCompanyEnums;
import com.allin.silas.dev.ipc.app.enums.PtzMoveActionEnums;
import com.allin.silas.dev.ipc.app.operate.IDevIpcOperateService;
import com.allin.silas.dev.ipc.app.operate.pojo.*;
import com.allin.silas.dev.utils.Eval;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.utils.DigestHttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;

import java.util.HashMap;
import java.util.Optional;

/**
 * 海康网络相机操作服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HkService implements IDevIpcOperateService {

    private static final String POSITION_URI = "/ISAPI/PTZCtrl/channels/";


    @Override
    public String ipcCompany() {
        return DevIpcCompanyEnums.HIK.getCode();
    }

    /**
     * 根据PTZ直接定位到位置
     *
     * <AUTHOR>
     */
    @Override
    public boolean ptzLocate(PanoramaPointDto point, String evalScript, DevIpc devIpc) {
        final Eval eval = new Eval(point.getX(), point.getY(), evalScript,  devIpc.getId());
        final Optional<PtzDto> optional = eval.process();
        if (optional.isEmpty()) {
            return false;
        }
        PtzDto ptz = optional.get();
        return ptzLocate(ptz, devIpc);
    }

    @Override
    public boolean ptzLocate(PtzDto ptz, DevIpc devIpc) {
        String data = StrUtil.format("<PTZData version=\"2.0\" xmlns=\"http://www.isapi.org/ver20/XMLSchema\"><AbsoluteHigh>" +
                        "<azimuth>{}</azimuth><elevation>{}</elevation><absoluteZoom>{}</absoluteZoom></AbsoluteHigh></PTZData>",
                ptz.getPan(), ptz.getTilt(), ptz.getZoom());
        String url = POSITION_URI + devIpc.getChannel() + "/absolute";
        try {
            HkIpcDigestClient.putData(devIpc.getIp(), url, data,
                    false, devIpc.getUserName(), devIpc.getPassword());
            return true;
        } catch (Exception e) {
            log.error("球机跟踪失败", e);
            return false;
        }
    }

    @Override
    public boolean ptzMove(PtzMoveActionEnums action, DevIpc devIpc) {
        String data;
        switch (action) {
            case TOP:
                data = "<PTZData><pan>0</pan><tilt>60</tilt><zoom>0</zoom></PTZData>";
                break;
            case BOTTOM:
                data = "<PTZData><pan>0</pan><tilt>-60</tilt><zoom>0</zoom></PTZData>";
                break;
            case LEFT:
                data = "<PTZData><pan>-60</pan><tilt>0</tilt><zoom>0</zoom></PTZData>";
                break;
            case RIGHT:
                data = "<PTZData><pan>60</pan><tilt>0</tilt><zoom>0</zoom></PTZData>";
                break;
            case TOP_LEFT:
                data = "<PTZData><pan>-60</pan><tilt>60</tilt><zoom>0</zoom></PTZData>";
                break;
            case TOP_RIGHT:
                data = "<PTZData><pan>60</pan><tilt>60</tilt><zoom>0</zoom></PTZData>";
                break;
            case BOTTOM_RIGHT:
                data = "<PTZData><pan>60</pan><tilt>-60</tilt><zoom>0</zoom></PTZData>";
                break;
            case BOTTOM_LEFT:
                data = "<PTZData><pan>-60</pan><tilt>-60</tilt><zoom>0</zoom></PTZData>";
                break;
            case STOP:
                data = "<PTZData><pan>0</pan><tilt>0</tilt><zoom>0</zoom></PTZData>";
                break;
            case ENLARGE:
                data = "<PTZData><pan>0</pan><tilt>0</tilt><zoom>60</zoom></PTZData>";
                break;
            case REDUCE:
                data = "<PTZData><pan>0</pan><tilt>0</tilt><zoom>-60</zoom></PTZData>";
                break;
            default:
                return false;
        }
        String url = POSITION_URI + devIpc.getChannel() + "/continuous";
        try {
            HkIpcDigestClient.putData(devIpc.getIp(), url, data, false,
                    devIpc.getUserName(), devIpc.getPassword());
        } catch (Exception e) {
            log.error("球机方向控制失败", e);
            return false;
        }
        return true;
    }

    @Override
    public boolean position3D(Position3DDto p3d, DevIpc devIpc) {
        HashMap<String, HashMap<Object, Object>> map = new HashMap<>();
        map.put("StartPoint", MapUtil.of(new String[][]{
                {"positionX", p3d.getStartX()},
                {"positionY", p3d.getStartY()}
        }));
        map.put("EndPoint", MapUtil.of(new String[][]{
                {"positionX", p3d.getEndX()},
                {"positionY", p3d.getEndY()}
        }));
        Document position3D = XmlUtil.mapToXml(map, "Position3D", "http://www.isapi.org/ver20/XMLSchema");
        position3D.getDocumentElement().setAttribute("version", "2.0");
        String data = XmlUtil.toStr(position3D, CharsetUtil.UTF_8, false, true);
        String url = POSITION_URI + devIpc.getChannel() + "/position3D";
        HkIpcDigestClient.putData(devIpc.getIp(), url, data, false,
                devIpc.getUserName(), devIpc.getPassword());
        return true;
    }

    @Override
    public PTZStatus getPtzStatus(DevIpc devIpc) {
        String url = StrUtil.format( "http://{}:{}@{}/ISAPI/PTZCtrl/channels/{}/status", devIpc.getUserName(), devIpc.getPassword(),
                devIpc.getIp(), devIpc.getChannel());
        String response = DigestHttpUtil.requestWithDigestAuth(url, devIpc.getUserName(), devIpc.getPassword(), Method.GET, 2000, null);
        XmlMapper xmlMapper = new XmlMapper();
        try {
            PTZStatus device = xmlMapper.readValue(response, PTZStatus.class);
            return device;
        } catch (JsonProcessingException e) {
            log.error(e.getMessage(), e);
        }
        throw new ValidationFailureException("获取失败");
    }


    /**
     * <StreamingChannel xmlns="http://www.hikvision.com/ver20/XMLSchema" version="2.0">
     * <Video>
     * <enabled>true</enabled>
     * <videoCodecType>H.264</videoCodecType>
     * <videoScanType>progressive</videoScanType>
     * <videoResolutionWidth>1920</videoResolutionWidth>
     * <videoResolutionHeight>1080</videoResolutionHeight>
     * <videoQualityControlType>CBR</videoQualityControlType>
     * <constantBitRate>2048</constantBitRate>
     * <fixedQuality>60</fixedQuality>
     * <maxFrameRate>2500</maxFrameRate>
     * <keyFrameInterval>2000</keyFrameInterval>
     * <snapShotImageType>JPEG</snapShotImageType>
     * <H264Profile>Main</H264Profile>
     * <GovLength>50</GovLength>
     * <PacketType>PS</PacketType>
     * <PacketType>RTP</PacketType>
     * <smoothing>50</smoothing>
     * </Video>
     * </StreamingChannel>
     *
     * GET /ISAPI/Streaming/channels/
     */
    @Override
    public void updateIpcConfig(DevIpc devIpc, IpcConfigDto ipcConfigDto) {
        String url = StrUtil.format( "/ISAPI/Streaming/channels/{}0{}",
                devIpc.getChannel(),
                devIpc.getStream());
        // constantBitRate: CBR 顶码率
        // fixedQuality 图像质量
        // maxFrameRate  视频帧率 视频帧率, desc:帧率x100，如22帧下发2200
        // H264Profile , H264编码复杂度
        // GovLength int, I帧间隔, desc:为帧的实际值
        // keyFrameInterval I帧间隔, unit:ms, unitType:时间, desc:为转化为毫秒时间单位的值
        // 1通道 默认
        String data = StrUtil.format("<StreamingChannel xmlns=\"http://www.hikvision.com/ver20/XMLSchema\" version=\"2.0\">\n" +
                "<Video>\n" +
                "<enabled>true</enabled>\n" +
                "<videoCodecType>{}</videoCodecType>\n" +
                "<videoScanType>progressive</videoScanType>\n" +
                "<videoResolutionWidth>{}</videoResolutionWidth>\n" +
                "<videoResolutionHeight>{}</videoResolutionHeight>\n" +
                "<videoQualityControlType>CBR</videoQualityControlType>\n" +
                "<constantBitRate>{}</constantBitRate>\n" +
                "<fixedQuality>60</fixedQuality>\n" +
                "<maxFrameRate>{}</maxFrameRate>\n" +
                "<keyFrameInterval>2000</keyFrameInterval>\n" +
                "<snapShotImageType>JPEG</snapShotImageType>\n" +
                "<H264Profile>Main</H264Profile>\n" +
                "<GovLength>{}</GovLength>\n" +
                "<PacketType>PS</PacketType>\n" +
                "<PacketType>RTP</PacketType>\n" +
                "<smoothing>50</smoothing>\n" +
                "</Video>\n" +
                "</StreamingChannel>",
                ipcConfigDto.getVideoCodecType(),
                ipcConfigDto.getVideoResolutionWidth(),
                ipcConfigDto.getVideoResolutionHeight(),
                ipcConfigDto.getConstantBitRate(),
                ipcConfigDto.getMaxFrameRate(),
                ipcConfigDto.getGovLength());

        HkIpcDigestClient.putData(devIpc.getIp(), url, data,
                false, devIpc.getUserName(), devIpc.getPassword());
    }

    @Override
    public void updateNtpServer(DevIpc devIpc, String serverIp) {

        String data = "\n" +
                "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<Time xmlns=\"http://www.isapi.org/ver20/XMLSchema\" version=\"2.0\">\n" +
                "\t<timeMode>NTP</timeMode>\n" +
                "\t<localTime></localTime>\n" +
                "\t<timeZone>CST-8:00:00DST00:30:00,M4.1.0/02:00:00,M10.5.0/02:00:00</timeZone>\n" +
                "\t<satelliteInterval></satelliteInterval>\n" +
                "\t<isSummerTime></isSummerTime>\n" +
                "\t<platformType></platformType>\n" +
                "\t<platformNo></platformNo>\n" +
                "</Time>";

        String url = StrUtil.format( "/ISAPI/System/time");
        HkIpcDigestClient.putData(devIpc.getIp(), url, data,
                false, devIpc.getUserName(), devIpc.getPassword());

        String ntpData = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<NTPServerList xmlns=\"http://www.isapi.org/ver20/XMLSchema\" version=\"2.0\">\n" +
                "\t<NTPServer>\n" +
                "\t\t<id>1</id>\n" +
                "\t\t<addressingFormatType>ipaddress</addressingFormatType>\n" +
                "\t\t<hostName></hostName>\n" +
                "\t\t<ipAddress>"+serverIp+"</ipAddress>\n" +
                "\t\t<ipv6Address></ipv6Address>\n" +
                "\t\t<portNo>123</portNo>\n" +
                "\t\t<synchronizeInterval>1440</synchronizeInterval>\n" +
                "\t\t<enabled>true</enabled>\n" +
                "\t</NTPServer>\n" +
                "</NTPServerList>";

        String ntpUrl = StrUtil.format( "/ISAPI/System/time/ntpServers");

        HkIpcDigestClient.putData(devIpc.getIp(), ntpUrl, ntpData,
                false, devIpc.getUserName(), devIpc.getPassword());

    }

    @Override
    public void rebootIpc(DevIpc devIpc) {
        HkIpcDigestClient.putData(devIpc.getIp(), "/ISAPI/System/reboot", null,
                false, devIpc.getUserName(), devIpc.getPassword());
    }
}
