package com.allin.silas.dev.ipc.infra.task;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.dev.ipc.adapter.vo.DevRecVo;
import com.allin.silas.dev.ipc.app.entity.DevIpc;
import com.allin.silas.dev.ipc.app.service.DevIpcCommandService;
import com.allin.silas.dev.ipc.app.service.DevIpcQueryService;
import com.allin.silas.dev.ipc.app.service.YoloCommandService;
import com.allin.silas.dev.ipc.app.service.YoloQueryService;
import com.allin.silas.dev.ipc.infra.repository.DevIpcMapper;
import com.allin.silas.dev.server.adapter.vo.DevServerVo;
import com.allin.silas.dev.server.app.service.DevServerQueryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 定时向增强识别 更新ipc通道
 */
@Component
@Slf4j
public class SynchronizeIpcTask {

    private final DevIpcCommandService devIpcCommandService;
    private final DevServerQueryService devServerService;
    private final YoloCommandService yoloService;
    private final YoloQueryService yoloQueryService;
    private final DevIpcMapper devIpcMapper;

    public SynchronizeIpcTask(DevIpcCommandService devIpcCommandService,
                              DevServerQueryService devServerService,
                              YoloCommandService yoloService, YoloQueryService yoloQueryService,
                              DevIpcMapper devIpcMapper) {
        this.devIpcCommandService = devIpcCommandService;
        this.devServerService = devServerService;
        this.yoloService = yoloService;
        this.yoloQueryService = yoloQueryService;
        this.devIpcMapper = devIpcMapper;
    }

    /**
     *  同步IPC 给增强识别
     */
    @Scheduled(fixedDelay = 1000 * 30)
    public void synchronizeIpcToYoloServer() {
        LambdaQueryWrapper<DevIpc> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DevIpc::getIsAiDetection, 1);
        lambdaQueryWrapper.eq(DevIpc::getIsYoloSynchronized, 0);
        lambdaQueryWrapper.eq(DevIpc::getDeleted, false);
        lambdaQueryWrapper.isNotNull(DevIpc::getAiDetectionServerId);
        List<DevIpc> devIpcList = devIpcMapper.selectList(lambdaQueryWrapper);
        for (DevIpc devIpc : devIpcList) {
            try {
                removeAiChannel(devIpc);
                // 再进行添加操作
                String devId = devIpcCommandService.addAiChannel(devIpc);
                if (StrUtil.isNotEmpty(devId)) {
                    devIpc.setIsYoloSynchronized(1);
                    devIpc.setAiDetectionDevId(Integer.valueOf(devId));
                    devIpcMapper.updateById(devIpc);
                }
            } catch (Exception ignored) {
            }
        }
    }

    private void removeAiChannel(DevIpc devIpc) {
        DevServerVo devServer = devServerService.getDevServer(devIpc.getAiDetectionServerId());
        if (ObjectUtil.isEmpty(devServer)) return;
        List<DevRecVo> devRecList = yoloQueryService.getDevRecList(devServer.getIpAddress(), devServer.getCommunicationPort());
        Optional<DevRecVo> optional = devRecList.stream().filter(e -> e.getDevNum().equals(devIpc.getId())).findFirst();
        optional.ifPresent(devRecVo -> yoloService.removeDev(devServer.getIpAddress(), devServer.getCommunicationPort(), devRecVo.getDevId()));
    }



}
