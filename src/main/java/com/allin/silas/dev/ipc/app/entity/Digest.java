package com.allin.silas.dev.ipc.app.entity;

import cn.hutool.crypto.SecureUtil;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class Digest {

    private final String uri;

    private final String httpMethod;

    private  String userName;

    private  String password;

    private final String realm;

    private final String nonce;

    private final String opaque;

    private final String qop;

    private int nonceCount = 3;

    private String cnonce = Long.toHexString(Double.doubleToLongBits(Math.random()));

    public Digest(String uri, String httpMethod, String userName, String password, String wwwAuth) {
        Map<String, String> tupels = new HashMap<>();
        if (wwwAuth.contains("Digest realm")) {
            tupels = getTuplesDH(wwwAuth);
        }else {
            tupels = getTuples(wwwAuth);
        }
        this.uri = uri;
        this.httpMethod = httpMethod;
        this.userName = userName;
        this.password = password;
        this.realm = tupels.get("realm") !=null ? null: tupels.get("Digest realm");
        this.nonce = tupels.get("nonce");
        this.opaque = tupels.get("opaque") == null?"":tupels.get("opaque");
        this.qop = tupels.get("Digest qop")!=null ? tupels.get("Digest qop"): tupels.get("qop");
    }

    public Digest(String uri, String httpMethod, String wwwAuth) {
        Map<String, String> tupels = new HashMap<>();
        if (wwwAuth.contains("Digest realm")) {
            tupels = getTuplesDH(wwwAuth);
        }else {
            tupels = getTuples(wwwAuth);
        }
        this.uri = uri;
        this.httpMethod = httpMethod;
        this.realm = tupels.get("realm") !=null ? tupels.get("realm"): tupels.get("Digest realm");
        this.nonce = tupels.get("nonce");
        this.opaque = tupels.get("opaque") == null?"":tupels.get("opaque");
        this.qop = tupels.get("Digest qop")!=null ? tupels.get("Digest qop"): tupels.get("qop");
    }

    private Map<String, String> getTuplesDH(String wwwAuth){
        String[] split = wwwAuth.split(",");
        Map<String, String> tuples = new HashMap<>(split.length);
        for (String part : split) {
            String[] pair = part.split("=");
            tuples.put(pair[0].trim(), pair[1].substring(1, pair[1].length()-1));
        }
        return tuples;
    }

    private Map<String, String> getTuples(String wwwAuth){
        String[] split = wwwAuth.split(", ");
        Map<String, String> tuples = new HashMap<>(split.length);
        for (String part : split) {
            String[] pair = part.split("=");
            tuples.put(pair[0], pair[1].substring(1, pair[1].length()-1));
        }
        return tuples;
    }

    public String calculateDigestAuthorization(){
        String ha1 = SecureUtil.md5(userName+":"+realm+":"+password);
        String ha2 = SecureUtil.md5(httpMethod+":"+uri);
        String nonceCountStr = String.format("%08d", nonceCount++);
        String response = SecureUtil.md5(ha1+":"+nonce+":"+nonceCountStr+":"+cnonce+":"+qop+":"+ha2);
        return "Digest username=\""+ userName + "\", realm=\""+realm+"\", nonce=\""+nonce+"\", uri=\""+uri+"\", qop=\""+qop+"\", nc="+nonceCountStr+", cnonce=\""+cnonce+"\", response=\""+response+"\", opaque=\""+opaque+"\"";
    }

}
