package com.allin.silas.dev.ipc.adapter.query;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * DevIpc 查询Qo
 *
 * <AUTHOR>
 * @since 2024/03/20 16:58:36
 */
@Data
public class DevIpcQuery {

    /**
     * 网络相机名称
     */
    private String ipcName;
    /**
     * 网络相机编码
     */
    private String ipcNum;
    /**
     * 1:固定枪机 2:云台枪机 3:球机  4:180度全景  5:半球/鱼眼相机 6:其他
     */
    private Integer ipcType;
    /**
     * 相机厂商 (ALLINTECH(傲英创视)、HIK（海康威视）、DAHUA（大华）、UNIVIEW（宇视）、HUAWEI（华为）、SUNELL（苏奈尔）、CANON（佳能）、CHANGHONG（长虹）、TIANDY（天地伟业）、PANASONIC（松下）、AXIS（安讯士）、其他)
     */
    private String ipcCompany;
    /**
     * 关联设备编号
     */
    private String devViewId;
    /**
     * ip地址
     */
    private String ip;
    /**
     * 通道号默认1
     */
    private Integer channel;
    /**
     * 预览码流默认第一码流
     */
    private Integer stream;
    /**
     * 登录名
     */
    private String userName;
    /**
     * 登录密码
     */
    private String password;
    /**
     * 安装高度
     */
    private java.math.BigDecimal height;
    /**
     * 安装经度
     */
    private String longitude;
    /**
     * 安装纬度
     */
    private String latitude;
    /**
     * 是否AI增强识别 1是 0否
     */
    private Integer isAiDetection;
    /**
     * 回放NVR地址
     */
    private String backIp;
    /**
     * 回放用户
     */
    private String backUserName;
    /**
     * 回放密码
     */
    private String backPassword;
    /**
     * 回放通道
     */
    private Integer backChannel;
    /**
     * 回放码流默认第一码流
     */
    private Integer backStream;
    /**
     * 项目主键
     */
    @NotBlank
    private String projectId;

    /**
     * 当前用户主键
     */
    private String queryAuthUserId;

    /**
     * 关联设备
     */
    private List<String> devNums;

    /**
     * 类型
     */
    private List<String> ipcTypes;

    /**
     * 增强识别服务器主键
     */
    private String aiDetectionServerId;

    /**
     * 流媒体服务器IP
     */
    private String mediaServerIp;
    /**
     * 查询关联ROI
     */
    private Boolean queryRoi = false;
    /**
     * 号段
     */
    private Integer numberSegment;

    /**
     * 回放
     */
    private String backMediaServerIp;


}
