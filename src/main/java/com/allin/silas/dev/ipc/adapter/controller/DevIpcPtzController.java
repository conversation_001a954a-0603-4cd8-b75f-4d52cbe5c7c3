package com.allin.silas.dev.ipc.adapter.controller;

import cn.hutool.core.util.StrUtil;
import com.allin.silas.dev.ipc.app.entity.DevIpc;
import com.allin.silas.dev.ipc.app.enums.PtzMoveActionEnums;
import com.allin.silas.dev.ipc.app.operate.DevIpcOperateFactory;
import com.allin.silas.dev.ipc.app.operate.IDevIpcOperateService;
import com.allin.silas.dev.ipc.app.operate.pojo.CloudPlatformControlDto;
import com.allin.silas.dev.ipc.app.operate.pojo.PTZStatus;
import com.allin.silas.dev.ipc.app.operate.pojo.Position3DDto;
import com.allin.silas.dev.ipc.app.operate.pojo.PtzDto;
import com.allin.silas.dev.ipc.app.service.DevIpcQueryService;
import com.allin.silas.dev.ipc.infra.repository.DevIpcMapper;
import com.allin.view.base.domain.Result;
import com.allin.view.base.enums.base.IEnums;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 围界4.X/设备设施管理-网络相机PTZ控制器
 */
@Validated
@RestController
@RequestMapping("/dev_ipc/ptz")
public class DevIpcPtzController {

    private final DevIpcQueryService devIpcQueryService;

    private final DevIpcOperateFactory devIpcOperateFactory;

    private final DevIpcMapper devIpcMapper;

    public DevIpcPtzController(DevIpcQueryService devIpcQueryService, DevIpcOperateFactory devIpcOperateFactory, DevIpcMapper devIpcMapper) {
        this.devIpcQueryService = devIpcQueryService;
        this.devIpcOperateFactory = devIpcOperateFactory;
        this.devIpcMapper = devIpcMapper;
    }

    /**
     * 网络相机云台控制功能支持
     */
    @PostMapping("/control")
    public Result<String> ptzControl(@Validated @RequestBody CloudPlatformControlDto control) {
        final DevIpc devIpc = devIpcMapper.selectById(control.getId());
        final IDevIpcOperateService operateService = devIpcOperateFactory.getDevIpcOperateService(devIpc.getIpcCompany());
        final boolean success = operateService.ptzMove(IEnums.codeOf(PtzMoveActionEnums.class, control.getAction()), devIpc);
        return success ? Result.ok() : Result.fail("控制失败，请检查相机参数！");
    }


    /**
     * 根据PTZ值进行绝对定位
     */
    @PostMapping("/locate/by_ipc/{ipcId}")
    public Result<String> ptzLocate(@PathVariable String ipcId, @Validated @RequestBody PtzDto ptzDto) {
        DevIpc devIpc = devIpcMapper.selectById(ipcId);
        if (devIpc == null) {
            return Result.fail("网络相机查询不到！");
        }
        final IDevIpcOperateService operateService = devIpcOperateFactory.getDevIpcOperateService(devIpc.getIpcCompany());
        if (operateService == null) {
            return Result.fail("该球机类型不支持联动");
        }
        final boolean success = operateService.ptzLocate(ptzDto, devIpc);
        return success ? Result.ok() : Result.fail("控制失败，请检查相机参数！");
    }

    /**
     * 海康3d功能支持
     */
    @PostMapping("/3d")
    public Result<String> ptz3DControl(@RequestBody Position3DDto position3D) {
        final DevIpc devIpc = devIpcMapper.selectById(position3D.getId());
        final IDevIpcOperateService operateService = devIpcOperateFactory.getDevIpcOperateService(devIpc.getIpcCompany());
        final boolean success = operateService.position3D(position3D, devIpc);
        return success ? Result.ok() : Result.fail("控制失败，请检查相机参数！");
    }

    /**
     * 获取ptz
     */
    @GetMapping("{ipcId}")
    public Result<PTZStatus> getPtz(@PathVariable String ipcId) {
        DevIpc devIpc = devIpcMapper.selectById(ipcId);
        final IDevIpcOperateService operateService = devIpcOperateFactory.getDevIpcOperateService(devIpc.getIpcCompany());
        PTZStatus ptzStatus = operateService.getPtzStatus(devIpc);
        return Result.ok(ptzStatus);
    }
}
