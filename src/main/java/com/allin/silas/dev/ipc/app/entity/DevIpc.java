package com.allin.silas.dev.ipc.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 * @since 2024/03/20 16:58:36
 * @TableName dev_ipc
 */
@Data
@TableName(value ="dev_ipc")
public class DevIpc implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 网络相机名称
     */
    private String ipcName;
    /**
     * 网络相机编码
     */
    private String ipcNum;
    /**
     * 1:固定枪机 2:云台枪机 3:球机  4:180度全景  5:半球/鱼眼相机 6:其他 10: 360度全景
     */
    private Integer ipcType;
    /**
     * 相机厂商 (ALLINTECH(傲英创视)、HIK（海康威视）、DAHUA（大华）、UNIVIEW（宇视）、HUAWEI（华为）、SUNELL（苏奈尔）、CANON（佳能）、CHANGHONG（长虹）、TIANDY（天地伟业）、PANASONIC（松下）、AXIS（安讯士）、其他)
     */
    private String ipcCompany;
    /**
     * ip地址
     */
    private String ip;
    /**
     * 通道号默认1
     */
    private Integer channel;
    /**
     * 预览码流默认第一码流
     */
    private Integer stream;
    /**
     * 登录名
     */
    private String userName;
    /**
     * 登录密码
     */
    private String password;
    /**
     * 安装高度
     */
    private java.math.BigDecimal height;
    /**
     * 安装经度
     */
    private String longitude;
    /**
     * 是否AI增强识别 1是 0否
     */
    private Integer isAiDetection;
    /**
     * 安装纬度
     */
    private String latitude;
    /**
     * 回放NVR地址
     */
    private String backIp;
    /**
     * 回放用户
     */
    private String backUserName;
    /**
     * 回放密码
     */
    private String backPassword;
    /**
     * 回放通道
     */
    private Integer backChannel;
    /**
     * 回放码流默认第一码流
     */
    private Integer backStream;
    /**
     * 流媒体服务器域名/ip
     */
    private String mediaServerIp;
    /**
     * 流媒体服务器RestApi端口
     */
    private Integer mediaServerRestApiPort;
    /**
     * 流媒体web端口
     */
    private Integer mediaServerWebPort;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private java.time.LocalDateTime createdTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.time.LocalDateTime updatedTime;
    /**
     * 删除标志(0表示存在，1表示删除)
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;
    /**
     * 项目主键
     */
    private String projectId;

    /**
     * AI增强识别服务器ID
     */
    private String aiDetectionServerId;

    /**
     * ivs服务器ID
     */
    private String ivsServerId;

    /**
     * 同步给增强识别状态（1 已同步 0 未同步 -1:后期围界更新或删除）
     */
    private Integer isYoloSynchronized;


    /**
     * 增强识别的devId
     */
    private Integer aiDetectionDevId;

    /**
     * 自定义播放链接
     */
    private String rtspUrl;

    /**
     * 自定义播放通道
     */
    private Integer mediaDevId;


    @TableField(exist = false)
    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 设备排序
     */
    private Integer devSort;

    /**
     * 直播协议
     */
    private String liveStreamType;

    /**
     * 回放协议
     */
    private String backStreamType;

    /**
     * 抓图链接
     */
    private String onvifUrl;

    /**
     * sip认证用户
     */
    private String sipUserName;

    /**
     * sip域
     */
    private String sipDomain;

    /**
     * sip通道
     */
    private String sipChannelId;

    /**
     * 回放sip认证用户
     */
    private String backSipUserName;

    /**
     * 回放sip域
     */
    private String backSipDomain;

    /**
     * 回放sip通道
     */
    private String backSipChannelId;
    /**
     * 号段
     */
    private Integer numberSegment;

    /**
     * 增强识别协议类型 JPEG/VIDEO
     */
    private String aiDetectType = "JPEG";
    /**
     * 播放方式 MEDIA_SERVER/JANUS
     */
    private String playType;

    /**
     * 回放服务器ip
     */
    private String backMediaServerIp;

    /**
     * 回放服务器ip
     */
    private Integer backMediaServerRestApiPort;

}
