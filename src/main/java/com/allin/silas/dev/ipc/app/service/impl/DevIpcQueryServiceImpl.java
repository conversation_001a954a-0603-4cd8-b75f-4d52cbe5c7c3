package com.allin.silas.dev.ipc.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.silas.dev.ipc.adapter.query.DevIpcQuery;
import com.allin.silas.dev.ipc.adapter.vo.DevIpcVo;
import com.allin.silas.dev.ipc.app.entity.DevIpc;
import com.allin.silas.dev.ipc.app.enums.DevIpcCompanyEnums;
import com.allin.silas.dev.ipc.app.service.DevIpcQueryService;
import com.allin.silas.dev.ipc.infra.repository.DevIpcMapper;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Component;

/**
 * @author: 郭国勇
 * @Date: 2025/7/17 10:46
 * @Description
 */
@Component
public class DevIpcQueryServiceImpl implements DevIpcQueryService {

    private final DevIpcMapper devIpcMapper;

    public DevIpcQueryServiceImpl(DevIpcMapper devIpcMapper) {
        this.devIpcMapper = devIpcMapper;
    }

    @Override
    public DevIpcVo getIpc(String id) {
        DevIpc devIpc = devIpcMapper.selectById(id);
        if (ObjectUtil.isNotEmpty(devIpc)) {
            String videoUrl = getVideoUrl(id);
            DevIpcVo devIpcVo = BeanUtil.toBean(devIpc, DevIpcVo.class);
            devIpcVo.setStreamUrl(videoUrl);
            return devIpcVo;
        }
        return null;
    }

    @Override
    public DevIpcVo getIpcByNum(String ipcNum) {
        DevIpc devIpc = devIpcMapper.selectOne(new LambdaQueryWrapper<DevIpc>().eq(DevIpc::getIpcNum, ipcNum).eq(DevIpc::getDeleted, 0));
        if (ObjectUtil.isNotEmpty(devIpc)) return BeanUtil.toBean(devIpc, DevIpcVo.class);
        return null;
    }

    @Override
    public String getOnvifUrl(String ipcId) {
        DevIpc devIpc = devIpcMapper.selectById(ipcId);
        if (devIpc == null) {
            throw new ValidationFailureException("相机不存在");
        }
        if (StrUtil.isNotEmpty(devIpc.getOnvifUrl())) {
            return devIpc.getOnvifUrl();
        }
        String ipcCompany = devIpc.getIpcCompany();
        if (DevIpcCompanyEnums.UNI_VIEW.getCode().equals(ipcCompany) || DevIpcCompanyEnums.UNI_VIEW2.getCode().equals(ipcCompany)) {
            return StrUtil.format("http://{}:{}@{}/images/snapshot.jpg",
                    devIpc.getUserName(),
                    devIpc.getPassword(),
                    devIpc.getIp());
        } else if (DevIpcCompanyEnums.HIK.getCode().equals(ipcCompany)) {
            return StrUtil.format("http://{}:{}@{}/onvif-http/snapshot?Profile_{}0{}",
                    devIpc.getUserName(),
                    devIpc.getPassword(),
                    devIpc.getIp(),
                    devIpc.getChannel(),
                    1);
        } else if (DevIpcCompanyEnums.DA_HUA.getCode().equals(ipcCompany)) {
            return StrUtil.format("http://{}:{}@{}/onvifsnapshot/media_service/snapshot?channel={}&subtype=0",
                    devIpc.getUserName(),
                    devIpc.getPassword(),
                    devIpc.getIp(),
                    devIpc.getChannel());
        } else if (DevIpcCompanyEnums.BOSHI.getCode().equals(ipcCompany)) {
            return StrUtil.format("http://{}:{}@{}/snap.jpg?JpegCam=1",
                    devIpc.getUserName(),
                    devIpc.getPassword(),
                    devIpc.getIp());
        }
        throw new ValidationFailureException("不支持的相机类型");
    }

    @Override
    public String getVideoUrl(String ipcId) {
        DevIpc devIpc = devIpcMapper.selectById(ipcId);
        if (devIpc == null) {
            throw new ValidationFailureException("相机不存在");
        }
        if (StrUtil.isNotEmpty(devIpc.getRtspUrl())) {
            return devIpc.getRtspUrl();
        }
        String ipcCompany = devIpc.getIpcCompany();
        if (DevIpcCompanyEnums.HIK.getCode().equals(ipcCompany)) {
            return StrUtil.format("rtsp://{}:{}@{}:554/Streaming/Channels/{}0{}",
                    devIpc.getUserName(),
                    devIpc.getPassword(),
                    devIpc.getIp(),
                    devIpc.getChannel(),
                    devIpc.getStream());
        } else if (DevIpcCompanyEnums.DA_HUA.getCode().equals(ipcCompany)) {
            return StrUtil.format("rtsp://{}:{}@{}",
                    devIpc.getUserName(),
                    devIpc.getPassword(),
                    devIpc.getIp());
        }  else if (DevIpcCompanyEnums.BOSHI.getCode().equals(ipcCompany)) {
            return StrUtil.format("rtsp://{}:{}@{}",
                    devIpc.getUserName(),
                    devIpc.getPassword(),
                    devIpc.getIp());
        } else if (DevIpcCompanyEnums.UNI_VIEW.getCode().equals(ipcCompany)) {
            // rtsp://{}:{}@{}/media/video2
            return StrUtil.format("rtsp://{}:{}@{}/media/video",
                    devIpc.getUserName(),
                    devIpc.getPassword(),
                    devIpc.getIp());
        } else if (DevIpcCompanyEnums.UNI_VIEW2.getCode().equals(ipcCompany)) {
            // rtsp://{}:{}@{}/media/video2
            return StrUtil.format("rtsp://{}:{}@{}/media/video2",
                    devIpc.getUserName(),
                    devIpc.getPassword(),
                    devIpc.getIp());
        }
        return null;
    }

    @Override
    public void getIpcPage(Page<DevIpcVo> page, DevIpcQuery devIpcQuery) {
        devIpcMapper.getIpcPage(page, devIpcQuery);
    }
}
