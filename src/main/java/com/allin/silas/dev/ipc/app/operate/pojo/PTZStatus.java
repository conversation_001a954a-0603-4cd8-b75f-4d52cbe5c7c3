package com.allin.silas.dev.ipc.app.operate.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

@Data
@JacksonXmlRootElement(localName = "PTZStatus", namespace = "http://www.hikvision.com/ver20/XMLSchema")
@JsonIgnoreProperties(ignoreUnknown = true)  // 建议移到外层类
public class PTZStatus {

    @JacksonXmlProperty(isAttribute = true, localName = "version")
    private String version = "2.0";

    @JacksonXmlProperty(localName = "AbsoluteHigh")
    private AbsoluteHigh absoluteHigh;

    @Data
    public static class AbsoluteHigh {

        @JacksonXmlProperty(localName = "elevation")
        private int elevation;

        @JacksonXmlProperty(localName = "azimuth")
        private int azimuth;

        @JacksonXmlProperty(localName = "absoluteZoom")
        private int absoluteZoom;
    }
}
