package com.allin.silas.dev.ipc.app.enums;

import com.allin.view.base.enums.base.IEnums;
import lombok.Getter;

/**
 * PTZ手动控制
 * <AUTHOR>
 */
public enum PtzMoveActionEnums implements IEnums {
    TOP("TOP", "上"),
    BOTTOM("BOTTOM", "下"),
    LEFT("LEFT", "左"),
    RIGHT("RIGHT", "右"),
    TOP_LEFT("TOP_LEFT", "左上"),
    TOP_RIGHT("TOP_RIGHT", "右上"),
    BOTTOM_LEFT("BOTTOM_LEFT", "左下"),
    BOTTOM_RIGHT("BOTTOM_RIGHT", "右下"),
    STOP("STOP", "暂停"),
    ENLARGE("ENLARGE", "放大"),
    REDUCE("REDUCE", "缩小");

    private final String code;

    @Getter
    private final String desc;

    PtzMoveActionEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

}
