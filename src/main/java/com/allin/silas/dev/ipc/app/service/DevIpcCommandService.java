package com.allin.silas.dev.ipc.app.service;

import com.allin.silas.dev.ipc.adapter.dto.AddDevIpcDto;
import com.allin.silas.dev.ipc.adapter.dto.EditDevIpcDto;
import com.allin.silas.dev.ipc.app.entity.DevIpc;

import java.util.List;

/**
 * @author: 郭国勇
 * @Date: 2025/7/17 10:45
 * @Description
 */
public interface DevIpcCommandService {

    /**
     * 创建
     */
    boolean createIpc(AddDevIpcDto addDevIpcDto);

    /**
     * 更新
     */
    boolean updateIpc(EditDevIpcDto editDevIpcDto);

    /**
     * 删除
     */
    boolean deleteIpc(String id);

    /**
     * 保存或者更新
     */
    void batchSaveOrUpdate(List<DevIpc> successList, String projectId, String userId);


    /**
     * 新增AI通道
     */
    String addAiChannel(DevIpc devIpc);
}
