package com.allin.silas.dev.ipc.app.operate.pojo;

import com.allin.silas.dev.ipc.app.enums.PtzMoveActionEnums;
import com.allin.view.base.validator.annotation.EnumValid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class CloudPlatformControlDto {

    /**
     * 网络相机id
     */
    @NotBlank
    private String id;

    /**
     * 操作行为
     */
    @EnumValid(target = PtzMoveActionEnums.class, isNotNull = true)
    private String action;

    /**
     * 机器人主键 可不传
     */
    private String robotId;
}
