package com.allin.silas.dev.ipc.app.operate;


import com.allin.silas.dev.ipc.app.entity.DevIpc;
import com.allin.silas.dev.ipc.app.enums.PtzMoveActionEnums;
import com.allin.silas.dev.ipc.app.operate.pojo.*;

/**
 * 网络相机相关操作接口
 *
 * <AUTHOR>
 */
public interface IDevIpcOperateService {

    /**
     * 获取厂商
     */
    String ipcCompany();

    /**
     * 根据PTZ直接定位到位置
     */
    boolean ptzLocate(PanoramaPointDto point, String evalScript, DevIpc devIpc);

    /**
     * 根据PTZ定位
     */
    default boolean ptzLocate(PtzDto ptzDto, DevIpc devIpc) {
        throw new UnsupportedOperationException("该相机不支持ptzLocate功能");
    }

    /**
     * 通过云台手动移动相机镜头
     * @param action 行为
     * @param devIpc 网络相机
     */
    boolean ptzMove(PtzMoveActionEnums action, DevIpc devIpc);

    /**
     * PTZ3D
     * @return {@link boolean}
     * <AUTHOR>
     */
    default boolean position3D(Position3DDto position3D, DevIpc devIpc) {
        throw new UnsupportedOperationException("该相机不支持ptz3D功能");
    }

    /**
     * 获取ptz
     */
    default PTZStatus getPtzStatus(DevIpc devIpc) {
        throw new UnsupportedOperationException("该相机获取ptz功能");
    }


    /**
     * 设置编码格式
     */
    default void updateIpcConfig(DevIpc devIpc, IpcConfigDto ipcConfigDto) {
        throw new UnsupportedOperationException("该相机不支持批量设置");
    }

    /**
     * 设置时间
     */
    default void updateNtpServer(DevIpc devIpc, String serverIp) {
        throw new UnsupportedOperationException("该相机不支持设置NTP");
    };

    /**
     * 重启相机
     */
    default void rebootIpc(DevIpc devIpc) {
        throw new UnsupportedOperationException("该相机不支持重启");
    }
}
