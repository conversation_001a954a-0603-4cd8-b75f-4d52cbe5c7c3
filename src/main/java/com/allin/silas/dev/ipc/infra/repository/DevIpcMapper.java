package com.allin.silas.dev.ipc.infra.repository;

import com.allin.silas.dev.ipc.adapter.query.DevIpcQuery;
import com.allin.silas.dev.ipc.adapter.vo.DevIpcVo;
import com.allin.silas.dev.ipc.app.entity.DevIpc;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;


/**
 * @description 针对表【dev_ipc()】的数据库操作Mapper
 * <AUTHOR>
 * @since 2024/03/20 16:58:36
 */
public interface DevIpcMapper extends BaseMapper<DevIpc> {

    Page<DevIpcVo> getIpcPage(Page<DevIpcVo> page, @Param("query") DevIpcQuery query);

}




