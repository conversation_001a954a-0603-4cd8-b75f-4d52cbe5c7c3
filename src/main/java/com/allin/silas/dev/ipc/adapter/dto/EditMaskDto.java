package com.allin.silas.dev.ipc.adapter.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class EditMaskDto {

    /**
     * 增强识别服务端ip
     */
    @NotBlank(message = "ip不能为空")
    private String ip;
    /**
     * 增强识别服务端端口
     */
    @NotNull(message = "端口不能为空")
    private Integer port;


    @NotNull
    private Integer devId;

    /**
     * "100,100;300,100;1300,300;200,1200\n200,200;300,200;300,300;200,300\n"
     */
    @NotBlank
    private String maskInfo;
}
