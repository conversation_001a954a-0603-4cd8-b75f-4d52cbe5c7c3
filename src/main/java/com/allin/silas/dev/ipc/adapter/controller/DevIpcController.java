package com.allin.silas.dev.ipc.adapter.controller;

import cn.hutool.core.io.IoUtil;
import cn.idev.excel.EasyExcel;
import com.allin.silas.dev.ipc.adapter.dto.AddDevIpcDto;
import com.allin.silas.dev.ipc.adapter.dto.EditDevIpcDto;
import com.allin.silas.dev.ipc.adapter.query.DevIpcQuery;
import com.allin.silas.dev.ipc.adapter.vo.DevIpcExcelVo;
import com.allin.silas.dev.ipc.adapter.vo.DevIpcVo;
import com.allin.silas.dev.ipc.app.listener.DevIpcImportListener;
import com.allin.silas.dev.ipc.app.service.DevIpcCommandService;
import com.allin.silas.dev.ipc.app.service.DevIpcQueryService;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.constant.I18nConstants;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.execl.EasyExcelOperUtils;
import com.allin.view.base.utils.file.FileNameUtils;
import com.allin.view.config.i18n.I18nMessageUtil;
import com.allin.view.config.redis.annotation.PreventDuplicateSubmit;
import com.allin.view.config.redis.annotation.RedisLock;
import com.allin.view.file.pojo.vo.ImportResultVo;
import com.allin.view.log.annotation.Log;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * @author: 郭国勇
 * @Date: 2025/7/17 13:00
 * @Description
 */
@RestController
@Validated
@Slf4j
@RequestMapping("dev_ipc")
public class DevIpcController {

    private final DevIpcQueryService devIpcQueryService;

    private final DevIpcCommandService devIpcCommandService;

    public DevIpcController(DevIpcQueryService devIpcQueryService, DevIpcCommandService devIpcCommandService) {
        this.devIpcQueryService = devIpcQueryService;
        this.devIpcCommandService = devIpcCommandService;
    }

    /**
     * 新增网络相机
     */
    @PostMapping
    @PreventDuplicateSubmit
    @Log(title = "网络相机", operDesc = "新增")
    public Result<String> createIpc(@Valid @RequestBody AddDevIpcDto addDevIpcDto) {
        boolean save = devIpcCommandService.createIpc(addDevIpcDto);
        if (!save) {
            return Result.fail(I18nMessageUtil.getMessage(I18nConstants.OPERATE_FAIL));
        }
        return Result.ok();
    }

    /**
     * 修改网络相机
     */
    @PutMapping
    @Log(title = "网络相机", operDesc = "修改")
    public Result<String> updateIpc(@Valid @RequestBody EditDevIpcDto editDevIpcDto) {
        boolean save = devIpcCommandService.updateIpc(editDevIpcDto);
        if (!save) {
            return Result.fail(I18nMessageUtil.getMessage(I18nConstants.OPERATE_FAIL));
        }
        return Result.ok();
    }

    /**
     * 删除网络相机
     */
    @DeleteMapping("{id}")
    @Log(title = "网络相机", operDesc = "删除")
    public Result<String> deleteIpc(@PathVariable String id) {
        boolean delete = devIpcCommandService.deleteIpc(id);
        if (!delete) {
            return Result.fail(I18nMessageUtil.getMessage(I18nConstants.OPERATE_FAIL));
        }
        return Result.ok();
    }

    /*
     * 分页查询网络相机
     */
    @GetMapping
    public Result<PageData<DevIpcVo>> getIpcPage(
            PageParam pageParam,
            @Valid DevIpcQuery devIpcQuery) {
        Page<DevIpcVo> page = pageParam.toPage();
        devIpcQueryService.getIpcPage(page, devIpcQuery);
        PageData<DevIpcVo> instance = PageData.getInstance(page);
        return Result.ok(instance);
    }

    /**
     * 查询详情网络相机
     */
    @GetMapping("{id}")
    public Result<DevIpcVo> getIpc(@PathVariable String id) {
        DevIpcVo devIpcVo = devIpcQueryService.getIpc(id);
        return Result.ok(devIpcVo);
    }

    /**
     * 导出网络相机
     */
    @GetMapping("export")
    @Log(title = "网络相机", operDesc = "导出")
    public void exportIpc(@Valid DevIpcQuery devIpcQuery, HttpServletResponse response) {
        Page<DevIpcVo> page = Page.of(-1, -1);
        devIpcQuery.setQueryAuthUserId(SecurityContextHolder.getUserId());
        devIpcQueryService.getIpcPage(page, devIpcQuery);
        EasyExcelOperUtils.exportXlsx(response, page.getRecords(), DevIpcVo.class, "网络相机");
    }

    /**
     * 获取抓图地址
     */
    @GetMapping("onvif/url/{ipcId}")
    @Log(title = "网络相机", operDesc = "获取抓图地址")
    public Result<String> getOnvifUrl(@PathVariable String ipcId) {
        return Result.ok(devIpcQueryService.getOnvifUrl(ipcId));
    }


    /**
     * 获取视频播放地址
     */
    @GetMapping("video/url/{ipcId}")
    @Log(title = "网络相机", operDesc = "获取抓图地址")
    public Result<String> getVideoUrl(@PathVariable String ipcId) {
        return Result.ok(devIpcQueryService.getVideoUrl(ipcId));
    }

    /**
     * 下载导入模板网络相机
     */
    @Log(title = "网络相机", operDesc = "下载模板")
    @GetMapping("template")
    public void downloadTemplate(HttpServletResponse response) {
        try (InputStream resourceAsStream = DevIpcController.class.getClassLoader().getResourceAsStream("templates/网络相机导入.xlsx")) {
            if (resourceAsStream != null) {
                // 设置响应头
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                response.setCharacterEncoding("utf-8");
                String fileName = FileNameUtils.getUrlEncodeFileName("网络相机导入", ".xlsx");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
                // 复制输入流到响应输出流
                IoUtil.copy(resourceAsStream, response.getOutputStream());
                response.flushBuffer();
            } else {
                // 如果资源未找到，可以返回相应的错误信息
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().println("模板文件未找到");
            }
        } catch (IOException e) {
            throw new ValidationFailureException(e.getMessage());
        }
    }

    /**
     * 导入网络相机
     */
    @PostMapping("import/{projectId}")
    @RedisLock(key = "import_dev_ipc")
    public Result<ImportResultVo> importData(
            @PathVariable String projectId,
            @RequestParam("file") MultipartFile file) throws IOException {
        // 导入excel
        DevIpcImportListener listener = new DevIpcImportListener(projectId, SecurityContextHolder.getUserId());
        EasyExcel.read(file.getInputStream(), DevIpcExcelVo.class, listener).sheet().doRead();
        long failureCount = listener.getErrorCount();
        if (failureCount > 0) {
            long successCount = listener.getSuccessCount();
            String errorLogId = listener.getErrorLogId();
            ImportResultVo importResultVo = new ImportResultVo();
            importResultVo.setSuccTotal(successCount);
            importResultVo.setErrorTotal(failureCount);
            importResultVo.setFileId(errorLogId);
            return Result.of(Result.FAIL, "导入失败", importResultVo);
        }
        return Result.ok();
    }
}
