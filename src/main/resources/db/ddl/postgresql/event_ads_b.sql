create table if not exists event_ads_b
(
    id               varchar(50)        not null
        primary key,
    project_id       varchar(50)        not null,
    icao_address     varchar(20),
    flight_number    varchar(20),
    callsign         varchar(50),
    lon              double precision,
    lat              double precision,
    altitude_mm      integer,
    hor_velocity_cms integer,
    ver_velocity_cms integer,
    emitter_type     integer,
    mag_heading      integer,
    emergency_status integer,
    heading_de2      integer,
    air_ground_state integer,
    ads_b_status     smallint default 2 not null,
    created_time     timestamp(0)       not null,
    is_deleted       integer  default 0 not null
);

comment on table event_ads_b is 'ADS-B 飞机信息表';

comment on column event_ads_b.id is '主键ID';

comment on column event_ads_b.project_id is '项目id';

comment on column event_ads_b.icao_address is 'ICAO地址';

comment on column event_ads_b.flight_number is '航班号';

comment on column event_ads_b.callsign is '飞机呼号';

comment on column event_ads_b.lon is '经度';

comment on column event_ads_b.lat is '纬度';

comment on column event_ads_b.altitude_mm is '海拔高度(毫米)';

comment on column event_ads_b.hor_velocity_cms is '水平速度,(厘米/秒)';

comment on column event_ads_b.ver_velocity_cms is '垂直速度,(厘米/秒)';

comment on column event_ads_b.emitter_type is '发射器类型（3通常指的是民航客机）';

comment on column event_ads_b.mag_heading is '磁航向';

comment on column event_ads_b.emergency_status is '紧急状态';

comment on column event_ads_b.heading_de2 is '航向角';

comment on column event_ads_b.air_ground_state is '空地状态, 0通常表示在空中，1表示在地面';

comment on column event_ads_b.ads_b_status is '状态,0-降落,1-起飞,2-其他';

comment on column event_ads_b.created_time is '创建时间';

comment on column event_ads_b.is_deleted is '是否删除: 0-否 1-是';

alter table event_ads_b
    owner to postgres;

create index if not exists event_ads_b_callsign_idx
    on event_ads_b (callsign);

create index if not exists event_ads_b_created_time_idx
    on event_ads_b (created_time);

create index if not exists event_ads_b_flight_number_idx
    on event_ads_b (flight_number);

create index if not exists event_ads_b_icao_address_idx
    on event_ads_b (icao_address);