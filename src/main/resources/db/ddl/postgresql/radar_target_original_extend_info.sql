create table if not exists radar_target_original_extend_info
(
    id                   varchar(64) not null
        primary key,
    project_id           varchar(64) not null,
    dev_num              varchar(64) not null,
    dev_type             varchar(64) not null,
    backend_batch_number varchar(64) not null,
    extend_data          text        not null
);

comment on table radar_target_original_extend_info is '雷达目标扩展信息表';

comment on column radar_target_original_extend_info.id is '主键id';

comment on column radar_target_original_extend_info.project_id is '项目id';

comment on column radar_target_original_extend_info.dev_num is '雷达设备编号';

comment on column radar_target_original_extend_info.dev_type is '雷达设备类型';

comment on column radar_target_original_extend_info.backend_batch_number is '后端生成的目标批次号';

comment on column radar_target_original_extend_info.extend_data is '扩展数据';

alter table radar_target_original_extend_info
    owner to postgres;

create index if not exists idx_radar_target_original_extend_info_batch_number
    on radar_target_original_extend_info (backend_batch_number);