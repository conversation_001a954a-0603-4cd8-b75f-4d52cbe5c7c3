create table if not exists visual_target_merged_info
(
    id                         varchar(64)       not null
        primary key,
    project_id                 varchar(64)       not null,
    dev_num                    varchar(64)       not null,
    dev_type                   varchar(64)       not null,
    backend_batch_number varchar(64) not null
        constraint visual_target_merged_info_uk
            unique,
    start_longitude            double precision  not null,
    start_latitude             double precision  not null,
    end_longitude              double precision  not null,
    end_latitude               double precision  not null,
    min_azimuth                real    default 0 not null,
    max_azimuth                real    default 0 not null,
    min_pitch                  real    default 0 not null,
    max_pitch                  real    default 0 not null,
    min_distance               real    default 0 not null,
    max_distance               real    default 0 not null,
    min_height                 real    default 0 not null,
    max_height                 real    default 0 not null,
    min_speed                  real    default 0 not null,
    max_speed                  real    default 0 not null,
    min_wing_span              real    default 0 not null,
    max_wing_span              real    default 0 not null,
    start_flight_direction     real    default 0 not null,
    end_flight_direction       real    default 0 not null,
    max_area                   real    default 0 not null,
    max_confidence_level       real    default 0 not null,
    start_img_url              varchar(500)      not null,
    end_img_url                varchar(500)      not null,
    min_runway_distance        double precision,
    max_runway_distance        double precision,
    min_runway_center_distance double precision,
    max_runway_center_distance double precision,
    target_size                integer default 0 not null,
    max_target_count           integer default 0 not null,
    detect_sub_type            varchar(255),
    detect_type                varchar(255),
    mark_type                  varchar(255),
    mark_sub_type              varchar(255),
    danger_level               smallint,
    collision_possibility      smallint,
    push_status                integer default 0 not null,
    start_time                 timestamp         not null,
    end_time                   timestamp         not null,
    dev_run_model              integer,
    merged_count               integer default 1 not null,
    merged_time                timestamp         not null,
    merged_status              integer default 0 not null,
    is_deleted                 integer default 0 not null
);

comment on table visual_target_merged_info is '合并的可视目标信息表';

comment on column visual_target_merged_info.id is '主键id';

comment on column visual_target_merged_info.project_id is '项目id';

comment on column visual_target_merged_info.dev_num is '设备编号';

comment on column visual_target_merged_info.dev_type is '设备类型';

comment on column visual_target_merged_info.backend_batch_number is '后端生成的批次号';

comment on column visual_target_merged_info.start_longitude is '开始经度';

comment on column visual_target_merged_info.start_latitude is '开始纬度';

comment on column visual_target_merged_info.end_longitude is '结束经度(认定一个目标的经纬度)';

comment on column visual_target_merged_info.end_latitude is '结束纬度(认定一个目标的经纬度)';

comment on column visual_target_merged_info.min_azimuth is '最小方位值';

comment on column visual_target_merged_info.max_azimuth is '最大方位值';

comment on column visual_target_merged_info.min_pitch is '最小俯仰值';

comment on column visual_target_merged_info.max_pitch is '最大俯仰值';

comment on column visual_target_merged_info.min_distance is '最小距离';

comment on column visual_target_merged_info.max_distance is '最大距离';

comment on column visual_target_merged_info.min_height is '最小高度';

comment on column visual_target_merged_info.max_height is '最大高度';

comment on column visual_target_merged_info.min_speed is '最小速度';

comment on column visual_target_merged_info.max_speed is '最大速度';

comment on column visual_target_merged_info.min_wing_span is '最小翼展';

comment on column visual_target_merged_info.max_wing_span is '最大翼展';

comment on column visual_target_merged_info.start_flight_direction is '开始飞行方向';

comment on column visual_target_merged_info.end_flight_direction is '结束飞行方向';

comment on column visual_target_merged_info.max_area is '最大面积';

comment on column visual_target_merged_info.max_confidence_level is '置信度';

comment on column visual_target_merged_info.start_img_url is '第一帧的切片地址';

comment on column visual_target_merged_info.end_img_url is '最后一帧的切片地址';

comment on column visual_target_merged_info.min_runway_distance is '离跑道最小距离';

comment on column visual_target_merged_info.max_runway_distance is '离跑道最大距离';

comment on column visual_target_merged_info.min_runway_center_distance is '离跑道中心点最小距离';

comment on column visual_target_merged_info.max_runway_center_distance is '离跑道中心点最大距离';

comment on column visual_target_merged_info.target_size is '目标大小,0点目标,1面目标';

comment on column visual_target_merged_info.max_target_count is '最大目标数量';

comment on column visual_target_merged_info.detect_sub_type is '细分识别类型,子类';

comment on column visual_target_merged_info.detect_type is '主要识别类型,一级分类';

comment on column visual_target_merged_info.mark_type is '手动标记类型,无人机/空飘物/鸟/鸟群/飞机/违建/虚景';

comment on column visual_target_merged_info.mark_sub_type is '手动标记子类型';

comment on column visual_target_merged_info.danger_level is '危险等级';

comment on column visual_target_merged_info.collision_possibility is '碰撞可能性';

comment on column visual_target_merged_info.push_status is '推送状态,0未推送,1已推送';

comment on column visual_target_merged_info.start_time is '开始时间';

comment on column visual_target_merged_info.end_time is '结束时间';

comment on column visual_target_merged_info.dev_run_model is '设备运行模式';

comment on column visual_target_merged_info.merged_count is '合并记录总数';

comment on column visual_target_merged_info.merged_time is '合并时间';

comment on column visual_target_merged_info.merged_status is '合并状态,0未合并,1已合并';

comment on column visual_target_merged_info.is_deleted is '是否删除, 0 否 1 是';

alter table visual_target_merged_info
    owner to postgres;

create index if not exists idx_visual_target_merged_info_time_detect
    on visual_target_merged_info (start_time, dev_num, detect_type, detect_sub_type);

create index if not exists idx_visual_target_merged_info_time_mark
    on visual_target_merged_info (start_time, dev_num, mark_type, mark_sub_type);

