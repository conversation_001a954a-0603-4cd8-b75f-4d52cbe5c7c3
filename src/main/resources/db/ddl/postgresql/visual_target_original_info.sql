create table if not exists visual_target_original_info
(
    id                     varchar(64)                not null
        primary key,
    project_id             varchar(64)                not null,
    dev_num                varchar(64)                not null,
    dev_type               varchar(64)                not null,
    backend_batch_number   varchar(64)                not null,
    frontend_batch_number  varchar(64)                not null,
    panorama_id            varchar(64),
    pano_x                 double precision,
    pano_y                 double precision,
    trajectory_x           varchar(500),
    trajectory_y           varchar(500),
    azimuth_trajectory     varchar(500),
    pitch_trajectory       varchar(500),
    longitude              double precision default 0 not null,
    latitude               double precision default 0 not null,
    azimuth                real             default 0 not null,
    pitch                  real             default 0 not null,
    distance               real             default 0 not null,
    height                 real             default 0 not null,
    split_bit              integer,
    speed                  double precision default 0 not null,
    wing_span              double precision default 0 not null,
    flight_direction       real                       not null,
    target_size            integer          default 0 not null,
    detect_sub_type        varchar(255),
    detect_type            varchar(255),
    confidence_level       real             default 0 not null,
    area                   real,
    img_width              integer,
    img_height             integer,
    img_url                varchar(500)               not null,
    target_count           integer          default 0 not null,
    dev_run_model          integer,
    runway_distance        double precision,
    runway_center_distance double precision,
    created_time           timestamp                  not null,
    is_deleted             integer          default 0 not null
);

comment on table visual_target_original_info is '原始的可视目标信息表';

comment on column visual_target_original_info.id is '主键id';

comment on column visual_target_original_info.project_id is '项目id';

comment on column visual_target_original_info.dev_num is '设备编号';

comment on column visual_target_original_info.dev_type is '设备类型';

comment on column visual_target_original_info.backend_batch_number is '后端生成的批次号';

comment on column visual_target_original_info.frontend_batch_number is '前端设备发送的批次号';

comment on column visual_target_original_info.panorama_id is '全景图编号';

comment on column visual_target_original_info.pano_x is '全景图x坐标';

comment on column visual_target_original_info.pano_y is '全景图y坐标';

comment on column visual_target_original_info.trajectory_x is '轨迹x坐标';

comment on column visual_target_original_info.trajectory_y is '轨迹y坐标';

comment on column visual_target_original_info.azimuth_trajectory is '方位轨迹';

comment on column visual_target_original_info.pitch_trajectory is '俯仰轨迹';

comment on column visual_target_original_info.longitude is '经度';

comment on column visual_target_original_info.latitude is '纬度';

comment on column visual_target_original_info.azimuth is '方位值';

comment on column visual_target_original_info.pitch is '俯仰值';

comment on column visual_target_original_info.distance is '距离';

comment on column visual_target_original_info.height is '高度';

comment on column visual_target_original_info.split_bit is '当前分割位';

comment on column visual_target_original_info.speed is '速度';

comment on column visual_target_original_info.wing_span is '翼展';

comment on column visual_target_original_info.flight_direction is '飞行方向';

comment on column visual_target_original_info.target_size is '目标大小,0点目标,1面目标';

comment on column visual_target_original_info.detect_sub_type is '细分识别类型,子类';

comment on column visual_target_original_info.detect_type is '主要识别类型,一级分类';

comment on column visual_target_original_info.confidence_level is '置信度';

comment on column visual_target_original_info.area is '面积';

comment on column visual_target_original_info.img_width is '切片宽度';

comment on column visual_target_original_info.img_height is '切片高度';

comment on column visual_target_original_info.img_url is '切片地址';

comment on column visual_target_original_info.target_count is '目标数量';

comment on column visual_target_original_info.dev_run_model is '设备运行模式';

comment on column visual_target_original_info.runway_distance is '离跑道距离';

comment on column visual_target_original_info.runway_center_distance is '离跑道中心点距离';

comment on column visual_target_original_info.created_time is '创建时间';

comment on column visual_target_original_info.is_deleted is '是否删除, 0 否 1 是';

alter table visual_target_original_info
    owner to postgres;

create index if not exists idx_visual_target_original_info_backend_batch_number
    on visual_target_original_info (backend_batch_number);