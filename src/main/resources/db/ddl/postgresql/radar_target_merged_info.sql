create table if not exists radar_target_merged_info
(
    id                    varchar(64)                not null
        primary key,
    project_id            varchar(64)                not null,
    dev_num               varchar(64)                not null,
    dev_type              varchar(64)                not null,
    backend_batch_number  varchar(64)                not null,
    detect_type           varchar(64)                not null,
    mark_type varchar(64),
    danger_level          smallint,
    collision_possibility smallint,
    start_time            timestamp                  not null,
    end_time              timestamp                  not null,
    min_confidence_level  double precision default 0 not null,
    max_confidence_level  double precision default 0 not null,
    min_height            double precision default 0 not null,
    max_height            double precision default 0 not null,
    min_distance          double precision default 0 not null,
    max_distance          double precision default 0 not null,
    min_runway_distance   double precision default 0 not null,
    max_runway_distance   double precision default 0 not null,
    min_azimuth           double precision default 0 not null,
    max_azimuth           double precision default 0 not null,
    min_pitch             double precision default 0 not null,
    max_pitch             double precision default 0 not null,
    min_echo_amplitude    double precision default 0 not null,
    max_echo_amplitude    double precision default 0 not null,
    min_speed             double precision default 0 not null,
    max_speed             double precision default 0 not null,
    min_snr               double precision default 0 not null,
    max_snr               double precision default 0 not null,
    min_rcs               double precision default 0 not null,
    max_rcs               double precision default 0 not null,
    start_longitude       double precision           not null,
    start_latitude        double precision           not null,
    end_longitude         double precision           not null,
    end_latitude          double precision           not null,
    push_status           integer          default 0 not null,
    merged_count          integer          default 1 not null,
    merged_time           timestamp                  not null,
    merged_status         integer          default 0 not null,
    is_deleted            integer          default 0 not null
);

comment on table radar_target_merged_info is '雷达目标合并信息表';

comment on column radar_target_merged_info.id is '主键id';

comment on column radar_target_merged_info.project_id is '项目id';

comment on column radar_target_merged_info.dev_num is '雷达设备编号';

comment on column radar_target_merged_info.dev_type is '雷达设备类型';

comment on column radar_target_merged_info.backend_batch_number is '后端生成的目标批次号';

comment on column radar_target_merged_info.detect_type is '识别类型: 鸟群/鸟类/无人机/空飘物/违建/飞机/车辆/人/船';

comment on column radar_target_merged_info.danger_level is '危险等级: 1-5';

comment on column radar_target_merged_info.collision_possibility is '碰撞可能性';

comment on column radar_target_merged_info.start_time is '开始时间';

comment on column radar_target_merged_info.end_time is '结束时间';

comment on column radar_target_merged_info.min_confidence_level is '最小目标置信度';

comment on column radar_target_merged_info.max_confidence_level is '最大目标置信度';

comment on column radar_target_merged_info.mark_type is '标记类型: 鸟群/鸟类/无人机/空飘物/违建/飞机/车辆/人/船';

comment on column radar_target_merged_info.min_height is '最小目标高度';

comment on column radar_target_merged_info.max_height is '最大目标高度';

comment on column radar_target_merged_info.min_distance is '最小距离设备';

comment on column radar_target_merged_info.max_distance is '最大距离设备';

comment on column radar_target_merged_info.min_runway_distance is '最小距离跑道';

comment on column radar_target_merged_info.max_runway_distance is '最大距离跑道';

comment on column radar_target_merged_info.min_azimuth is '最小方位角';

comment on column radar_target_merged_info.max_azimuth is '最大方位角';

comment on column radar_target_merged_info.min_pitch is '最小俯仰角';

comment on column radar_target_merged_info.max_pitch is '最大俯仰角';

comment on column radar_target_merged_info.min_echo_amplitude is '最小目标回波幅度';

comment on column radar_target_merged_info.max_echo_amplitude is '最大目标回波幅度';

comment on column radar_target_merged_info.min_speed is '最小速度';

comment on column radar_target_merged_info.max_speed is '最大速度';

comment on column radar_target_merged_info.min_snr is '最小信噪比(dB)';

comment on column radar_target_merged_info.max_snr is '最大信噪比(dB)';

comment on column radar_target_merged_info.min_rcs is '最小雷达截面积';

comment on column radar_target_merged_info.max_rcs is '最大雷达截面积';

comment on column radar_target_merged_info.start_longitude is '开始经度';

comment on column radar_target_merged_info.start_latitude is '开始纬度';

comment on column radar_target_merged_info.end_longitude is '结束经度(认定一个目标的经纬度)';

comment on column radar_target_merged_info.end_latitude is '结束纬度(认定一个目标的经纬度)';

comment on column radar_target_merged_info.push_status is '推送状态: 0-未推送 1-已推送';

comment on column radar_target_merged_info.merged_count is '合并记录总数';

comment on column radar_target_merged_info.merged_time is '合并时间';

comment on column radar_target_merged_info.merged_status is '合并状态: 0-未合并 1-已合并';

comment on column radar_target_merged_info.is_deleted is '是否删除: 0-否 1-是';

alter table radar_target_merged_info
    owner to postgres;

create unique index if not exists idx_radar_target_merged_info_backend_batch_number
    on radar_target_merged_info (backend_batch_number);

create index if not exists idx_visual_target_merged_info_time_detect
    on radar_target_merged_info (start_time, detect_type, danger_level);

create index if not exists idx_visual_target_merged_info_time_danger
    on radar_target_merged_info (start_time, danger_level);

