create table if not exists radar_target_original_info
(
    id                    varchar(64)                not null
        primary key,
    project_id            varchar(64)                not null,
    dev_num               varchar(64)                not null,
    dev_type              varchar(64)                not null,
    backend_batch_number  varchar(64)                not null,
    frontend_batch_number varchar(64)                not null,
    detect_type           varchar(64)                not null,
    danger_level          smallint                   not null,
    dev_run_model         integer                    not null,
    confidence_level      double precision default 0 not null,
    height                double precision default 0 not null,
    azimuth               double precision default 0 not null,
    pitch                 double precision default 0 not null,
    echo_amplitude        double precision,
    speed                 double precision,
    snr                   double precision,
    rcs                   double precision,
    longitude             double precision           not null,
    latitude              double precision           not null,
    distance              double precision,
    runway_distance       double precision,
    created_time          timestamp                  not null,
    is_deleted            integer          default 0 not null
);

comment on table radar_target_original_info is '雷达目标原始信息表';

comment on column radar_target_original_info.id is '主键id';

comment on column radar_target_original_info.project_id is '项目id';

comment on column radar_target_original_info.dev_num is '雷达设备编号';

comment on column radar_target_original_info.dev_type is '雷达设备类型';

comment on column radar_target_original_info.backend_batch_number is '后端生成的目标批次号';

comment on column radar_target_original_info.frontend_batch_number is '前端设备发送的目标批次号';

comment on column radar_target_original_info.detect_type is '识别类型: 鸟群/鸟类/无人机/空飘物/违建/飞机/车辆/人/船';

comment on column radar_target_original_info.danger_level is '危险等级: 1-5';

comment on column radar_target_original_info.dev_run_model is '设备运行模式: 1-待机模式 2-周扫模式 3-扇扫模式';

comment on column radar_target_original_info.confidence_level is '目标置信度';

comment on column radar_target_original_info.height is '目标高度(米)';

comment on column radar_target_original_info.azimuth is '方位角';

comment on column radar_target_original_info.pitch is '俯仰角';

comment on column radar_target_original_info.echo_amplitude is '目标回波幅度';

comment on column radar_target_original_info.speed is '速度';

comment on column radar_target_original_info.snr is '信噪比(dB)';

comment on column radar_target_original_info.rcs is '雷达截面积';

comment on column radar_target_original_info.longitude is '经度';

comment on column radar_target_original_info.latitude is '纬度';

comment on column radar_target_original_info.distance is '距离设备(米)';

comment on column radar_target_original_info.runway_distance is '距离跑道(米)';

comment on column radar_target_original_info.created_time is '创建时间';

comment on column radar_target_original_info.is_deleted is '是否删除: 0-否 1-是';

alter table radar_target_original_info
    owner to postgres;

create index if not exists idx_radar_target_original_info_backend_batch_number
    on radar_target_original_info (backend_batch_number);

