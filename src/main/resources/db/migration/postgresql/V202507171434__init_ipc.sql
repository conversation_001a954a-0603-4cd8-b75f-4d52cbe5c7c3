CREATE TABLE dev_ipc
(
    id                              varchar(19)              NOT NULL,
    ipc_name                        varchar(100)             NOT NULL,
    ipc_num                         varchar(100)             NOT NULL,
    ipc_type                        int2        DEFAULT 1    NOT NULL,
    ipc_company                     varchar(20)              NOT NULL,
    ip                              varchar(20)              NOT NULL,
    channel                         int4        DEFAULT 1    NOT NULL,
    stream                          int4        DEFAULT 1    NOT NULL,
    user_name                       varchar(50) DEFAULT 'admin'::character varying NOT NULL,
    "password"                      varchar(50) DEFAULT 'Allin2018'::character varying NOT NULL,
    height                          numeric(5, 1)            NULL,
    longitude                       varchar(30)              NULL,
    latitude                        varchar(30)              NULL,
    is_ai_detection                 int2                     NOT NULL,
    back_ip                         varchar(20)              NULL,
    back_user_name                  varchar(50)              NULL,
    back_password                   varchar(50)              NULL,
    back_channel                    int4                     NULL,
    back_stream                     int4                     NULL,
    created_by                      varchar(19)              NULL,
    created_time                    timestamp                NULL,
    updated_by                      varchar(19)              NULL,
    updated_time                    timestamp                NULL,
    deleted                         int2                     NULL,
    project_id                      varchar(19)              NOT NULL,
    media_server_ip                 varchar(50)              NOT NULL,
    media_server_rest_api_port      int4        DEFAULT 9191 NOT NULL,
    media_server_web_port           int4        DEFAULT 9194 NOT NULL,
    ai_detection_server_id          varchar(19)              NULL,
    ivs_server_id                   varchar(19)              NULL,
    is_yolo_synchronized            int4                     NULL,
    ai_detection_dev_id             int4                     NULL,
    rtsp_url                        varchar(200)             NULL,
    media_dev_id                    int4                     NULL,
    dev_sort                        int8        DEFAULT 1    NOT NULL,
    live_stream_type                varchar(10) DEFAULT 'RTSP'::character varying NOT NULL,
    back_stream_type                varchar(10) DEFAULT 'RTSP'::character varying NOT NULL,
    onvif_url                       varchar(200)             NULL,
    sip_user_name                   varchar(100)             NULL,
    sip_domain                      varchar(100)             NULL,
    sip_channel_id                  varchar(100)             NULL,
    back_sip_user_name              varchar(100)             NULL,
    back_sip_domain                 varchar(100)             NULL,
    back_sip_channel_id             varchar(100)             NULL,
    number_segment                  int4                     NULL,
    ai_detect_type                  varchar(10) DEFAULT 'JPEG'::character varying NOT NULL,
    play_type                       varchar(20) DEFAULT 'MEDIA_SERVER'::character varying NOT NULL,
    back_media_server_ip            varchar(50)              NULL,
    back_media_server_rest_api_port int4                     NULL,
    CONSTRAINT dev_ipc_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.dev_ipc IS '网络相机';

-- Column comments
COMMENT ON COLUMN public.dev_ipc.id IS '主键';
COMMENT ON COLUMN public.dev_ipc.ipc_name IS '相机名称';
COMMENT ON COLUMN public.dev_ipc.ipc_num IS '相机编码';
COMMENT ON COLUMN public.dev_ipc.ipc_type IS '1:固定枪机 2:云台枪机 3:球机  4:180度全景  5:半球/鱼眼相机 6:其他 10: 360度全景';
COMMENT ON COLUMN public.dev_ipc.ipc_company IS '公司';
COMMENT ON COLUMN public.dev_ipc.ip IS '相机IP';
COMMENT ON COLUMN public.dev_ipc.channel IS '通道';
COMMENT ON COLUMN public.dev_ipc.stream IS '码流';
COMMENT ON COLUMN public.dev_ipc.user_name IS '用户名';
COMMENT ON COLUMN public.dev_ipc.password IS '密码';
COMMENT ON COLUMN public.dev_ipc.height IS '高度';
COMMENT ON COLUMN public.dev_ipc.longitude IS '经度';
COMMENT ON COLUMN public.dev_ipc.latitude IS '纬度';
COMMENT ON COLUMN public.dev_ipc.is_ai_detection IS '是否增强识别';
COMMENT ON COLUMN public.dev_ipc.back_ip IS '回放IP';
COMMENT ON COLUMN public.dev_ipc.back_user_name IS '回放用户名';
COMMENT ON COLUMN public.dev_ipc.back_password IS '回放密码';
COMMENT ON COLUMN public.dev_ipc.back_channel IS '回放通道';
COMMENT ON COLUMN public.dev_ipc.back_stream IS '回放码流';
COMMENT ON COLUMN public.dev_ipc.created_by IS '创建人';
COMMENT ON COLUMN public.dev_ipc.created_time IS '创建时间';
COMMENT ON COLUMN public.dev_ipc.updated_by IS '更新人';
COMMENT ON COLUMN public.dev_ipc.updated_time IS '更新时间';
COMMENT ON COLUMN public.dev_ipc.deleted IS '删除字段';
COMMENT ON COLUMN public.dev_ipc.project_id IS '项目主键';
COMMENT ON COLUMN public.dev_ipc.media_server_ip IS '直播流媒体IP';
COMMENT ON COLUMN public.dev_ipc.media_server_rest_api_port IS '直播流媒体REST端口';
COMMENT ON COLUMN public.dev_ipc.media_server_web_port IS '直播流媒体web端口';
COMMENT ON COLUMN public.dev_ipc.ai_detection_server_id IS '增强识别服务器主键';
COMMENT ON COLUMN public.dev_ipc.ivs_server_id IS 'IVS服务器主键';
COMMENT ON COLUMN public.dev_ipc.is_yolo_synchronized IS '是否已同步到增强识别服务';
COMMENT ON COLUMN public.dev_ipc.ai_detection_dev_id IS '增强识别通道号';
COMMENT ON COLUMN public.dev_ipc.rtsp_url IS '自定义的相机播放流';
COMMENT ON COLUMN public.dev_ipc.media_dev_id IS '网络相机播放通道号';
COMMENT ON COLUMN public.dev_ipc.dev_sort IS '排序';
COMMENT ON COLUMN public.dev_ipc.live_stream_type IS '直播协议';
COMMENT ON COLUMN public.dev_ipc.back_stream_type IS '回放协议';
COMMENT ON COLUMN public.dev_ipc.onvif_url IS '自定义抓图链接';
COMMENT ON COLUMN public.dev_ipc.sip_user_name IS '国标用户名';
COMMENT ON COLUMN public.dev_ipc.sip_domain IS '国标域名';
COMMENT ON COLUMN public.dev_ipc.sip_channel_id IS '国标通道';
COMMENT ON COLUMN public.dev_ipc.back_sip_user_name IS '回放国标用户名';
COMMENT ON COLUMN public.dev_ipc.back_sip_domain IS '回放国标域名';
COMMENT ON COLUMN public.dev_ipc.back_sip_channel_id IS '回放国标通道号';
COMMENT ON COLUMN public.dev_ipc.number_segment IS '号段';
COMMENT ON COLUMN public.dev_ipc.ai_detect_type IS '增强识别检测类型 图片/视频流';
COMMENT ON COLUMN public.dev_ipc.play_type IS '播放器类型 JANUS/MEDIA_SERVER';
COMMENT ON COLUMN public.dev_ipc.back_media_server_ip IS '回放流媒体服务器IP';
COMMENT ON COLUMN public.dev_ipc.back_media_server_rest_api_port IS '回放流媒体服务器端口';


CREATE TABLE dev_server
(
    id                 varchar(19)    NOT NULL, -- 主键
    server_name        varchar(100)   NOT NULL, -- 设备名称
    server_number      varchar(100)   NOT NULL, -- 设备编号
    server_type        int8           NOT NULL, -- 设备类型((0：应用服务器, 1：增强识别服务器, 2：流媒体服务器, 3：NCE设备, 4：IVS设备, 5：文件服务器, 6：NVR设备))
    ip_address         varchar(50)    NOT NULL, -- IP地址
    communication_port int4           NOT NULL, -- 通信端口
    user_name          varchar(255)   NULL,     -- 用户名称
    user_password      varchar(255)   NULL,     -- 用户密码
    status             int2 DEFAULT 1 NOT NULL, -- 状态(1启用, 0禁用)
    created_time       timestamp      NULL,     -- 创建时间
    updated_by         varchar(19)    NULL,     -- 更新人
    updated_time       timestamp      NULL,     -- 更新时间
    deleted            int2           NULL,     -- 删除标志(0表示存在，1表示删除)
    project_id         varchar(19)    NOT NULL, -- 项目主键
    CONSTRAINT dev_server_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.dev_server IS '系统设备';

-- Column comments

COMMENT ON COLUMN public.dev_server.id IS '主键';
COMMENT ON COLUMN public.dev_server.server_name IS '设备名称';
COMMENT ON COLUMN public.dev_server.server_number IS '设备编号';
COMMENT ON COLUMN public.dev_server.server_type IS '设备类型((0：应用服务器, 1：增强识别服务器, 2：流媒体服务器, 3：NCE设备, 4：IVS设备, 5：文件服务器, 6：NVR设备))';
COMMENT ON COLUMN public.dev_server.ip_address IS 'IP地址';
COMMENT ON COLUMN public.dev_server.communication_port IS '通信端口';
COMMENT ON COLUMN public.dev_server.user_name IS '用户名称';
COMMENT ON COLUMN public.dev_server.user_password IS '用户密码';
COMMENT ON COLUMN public.dev_server.status IS '状态(1启用, 0禁用)';
COMMENT ON COLUMN public.dev_server.created_time IS '创建时间';
COMMENT ON COLUMN public.dev_server.updated_by IS '更新人';
COMMENT ON COLUMN public.dev_server.updated_time IS '更新时间';
COMMENT ON COLUMN public.dev_server.deleted IS '删除标志(0表示存在，1表示删除)';
COMMENT ON COLUMN public.dev_server.project_id IS '项目主键';

INSERT INTO sys_dict_type (id, "name", "type", status, remark, created_by, created_time, updated_by, updated_time,
                           project_id)
VALUES ('20240413170500', '相机厂商', 'ipc_company', 1, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240506160400', '相机类型', 'ipc_type', 1, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO sys_dict_data (id, "type", "label", value, sort, status, is_disabled, parent_id, created_by,
                           created_time, updated_by, updated_time, project_id)
VALUES ('20240413170501', 'ipc_company', '傲英创视', 'ALLINTECH', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240413170502', 'ipc_company', '海康威视', 'HIK', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240413170503', 'ipc_company', '大华', 'DAHUA', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240413170504', 'ipc_company', '宇视', 'UNIVIEW', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240413170505', 'ipc_company', '华为', 'HUAWEI', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240413170506', 'ipc_company', '苏奈尔', 'SUNELL', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240413170507', 'ipc_company', '佳能', 'CANON', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240413170508', 'ipc_company', '长虹', 'CHANGHONG', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240413170509', 'ipc_company', '天地伟业', 'TIANDY', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('202404131705010', 'ipc_company', '松下', 'PANASONIC', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO sys_dict_data (id, "type", "label", value, sort, status, is_disabled, parent_id, created_by,
                           created_time, updated_by, updated_time, project_id)
VALUES ('202404131705011', 'ipc_company', '安讯士', 'AXIS', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('202404131705012', 'ipc_company', '和普威视', 'HPVS', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('202404131705013', 'ipc_company', '其他', 'OTHER', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240506160401', 'ipc_type', '固定枪机', '1', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240506160402', 'ipc_type', '云台枪机', '2', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240506160403', 'ipc_type', '球机', '3', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240506160404', 'ipc_type', '180度全景', '4', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240506160405', 'ipc_type', '高帧频全景', '5', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240506160406', 'ipc_type', '半球/鱼眼相机', '6', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL),
       ('20240506160407', 'ipc_type', '其他', '7', 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL);
