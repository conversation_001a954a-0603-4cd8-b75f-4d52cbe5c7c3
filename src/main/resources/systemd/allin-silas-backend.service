[Unit]
Description=allin-silas-backend Community Server
After=network.target

[Install]
WantedBy=multi-user.target

[Service]
User=root
Group=root
Type=simple
WorkingDirectory=/home/<USER>/project/allin-silas-backend/
ExecStart=/usr/lib/jvm/java-17-openjdk-amd64/bin/java -Xms10g -Xmx10g -jar -Dsun.jnu.encoding=UTF-8 -Dfile.encoding=UTF-8 /home/<USER>/project/allin-silas-backend/allin-silas-backend.jar
# 停止服务时的等待秒数，如果超过这个时间仍然没有停止，Systemd 会使用 SIGKILL 信号强行杀死服务的进程
TimeoutStopSec=60
LimitNOFILE=5000
Restart=on-failure
