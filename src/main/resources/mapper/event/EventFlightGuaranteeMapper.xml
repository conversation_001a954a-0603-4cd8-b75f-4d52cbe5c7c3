<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.event.infra.repository.EventFlightGuaranteeMapper">
    <!--$var tableName= visual_target_merged_info-->
    <select id="page" resultType="com.allin.silas.event.adapter.vo.EventFlightGuaranteePageVo">
        select tb1.id,
               tb1.dev_num,
               tb1.batch_number,
               tb1.flight_number,
               tb1.created_time,
               tb2.min_distance,
               tb2.max_distance,
               tb2.min_height,
               tb2.max_height,
               tb2.min_azimuth,
               tb2.max_azimuth
        from event_flight_guarantee tb1
                 left join ${tableName} tb2 on tb1.batch_number = tb2.backend_batch_number
        where tb1.project_id = #{query.projectId}
          and tb1.is_deleted = 0
          and tb2.is_deleted = 0
          AND tb1.created_time &lt;= #{query.endTime}
          AND tb1.created_time >= #{query.startTime}
        <if test="query.devNums != null and query.devNums.size() != 0">
            AND tb1.dev_num in
            <foreach item="item" collection="query.devNums" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.flightNumber != null and query.flightNumber != ''">
            and tb1.flight_number = #{query.flightNumber}
        </if>
        <if test="query.minDistance != null">
            AND tb2.max_distance >= #{query.minDistance}
        </if>
        <if test="query.maxDistance != null">
            AND tb2.min_distance &lt;= #{query.maxDistance}
        </if>
        <if test="query.minRunwayDistance != null">
            AND tb2.max_runway_distance >= #{query.minRunwayDistance}
        </if>
        <if test="query.maxRunwayDistance != null">
            AND tb2.min_runway_distance &lt;= #{query.maxRunwayDistance}
        </if>
        <if test="query.minHeight != null">
            AND tb2.max_height >= #{query.minHeight}
        </if>
        <if test="query.maxHeight != null">
            AND tb2.min_height &lt;= #{query.maxHeight}
        </if>
    </select>
</mapper>