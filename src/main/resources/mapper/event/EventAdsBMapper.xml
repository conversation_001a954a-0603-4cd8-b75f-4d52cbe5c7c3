<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.event.infra.repository.EventAdsBMapper">
    <!--$var tableName= event_ads_b-->
    <select id="page" resultType="com.allin.silas.event.adapter.vo.EventAdsBPageVo">
        SELECT tb1.id,
               tb1.icao_address,
               tb1.flight_number,
               tb1.callsign,
               tb1.lon,
               tb1.lat,
               tb1.altitude_mm,
               tb1.hor_velocity_cms,
               tb1.ver_velocity_cms,
               tb1.emitter_type,
               tb1.mag_heading,
               tb1.emergency_status,
               tb1.heading_de2,
               tb1.air_ground_state,
               tb1.ads_b_status,
               tb1.created_time
        from ${tableName} tb1
        <where>
            tb1.project_id = #{query.projectId}
              and tb1.is_deleted = 0
              and tb1.created_time = (SELECT MAX(tb2.created_time)
                                      FROM ${tableName} tb2
            WHERE tb2.callsign = tb1.callsign
              AND tb2.project_id = #{query.projectId}
              AND tb2.is_deleted = 0
            <if test="query.startTime != null and query.endTime != null">
                AND tb2.created_time &gt;= #{query.startTime}
                AND tb2.created_time &lt;= #{query.endTime}
            </if>
            <if test="query.icaoAddress != null and query.icaoAddress != ''">
                and tb2.icao_address = #{query.icaoAddress}
            </if>
            <if test="query.flightNumber != null and query.flightNumber != ''">
                and tb2.flight_number = #{query.flightNumber}
            </if>
            <if test="query.callsign != null and query.callsign != ''">
                and tb2.callsign = #{query.callsign}
            </if>
            <if test="query.asdBStatus != null">
                and tb2.ads_b_status = #{query.asdBStatus}
            </if>
            )
        </where>
        ORDER BY tb1.created_time DESC
    </select>

    <!--$var tableName= event_ads_b-->
    <select id="pageByCallsign" resultType="com.allin.silas.event.adapter.vo.EventAdsBPageVo">
        SELECT tb1.id,
               tb1.icao_address,
               tb1.flight_number,
               tb1.callsign,
               tb1.lon,
               tb1.lat,
               tb1.altitude_mm,
               tb1.hor_velocity_cms,
               tb1.ver_velocity_cms,
               tb1.emitter_type,
               tb1.mag_heading,
               tb1.emergency_status,
               tb1.heading_de2,
               tb1.air_ground_state,
               tb1.ads_b_status,
               tb1.created_time
        from ${tableName} tb1
        <where>
            tb1.callsign = #{callsign}
              and tb1.project_id = #{projectId}
              and tb1.is_deleted = 0
        </where>
        ORDER BY tb1.created_time DESC
    </select>
</mapper>