<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.event.infra.repository.EventInvasionHandleMapper">
    <select id="page" resultType="com.allin.silas.event.adapter.vo.EventInvasionPageVo">
        select tb1.id,
               tb1.source,
               tb1.source_id,
               tb1.event_type,
               tb1.discover_time,
               tb1.discover_region_id,
               tb1.altitude,
               tb1.danger_level,
               tb1.handle_status
        from event_invasion tb1
        <where>
            tb1.project_id = #{query.projectId}
              and tb1.is_deleted = 0
            <if test="query.source != null">
                and tb1.source = #{query.source}
            </if>
            <if test="query.eventType != null">
                and tb1.event_type = #{query.eventType}
            </if>
            <if test="query.startDiscoverTime != null and query.endDiscoverTime != null">
                and tb1.discover_time &gt;= #{query.startDiscoverTime}
                and tb1.discover_time &lt;= #{query.endDiscoverTime}
            </if>
            <if test="query.discoverRegionId != null">
                and tb1.discover_region_id = #{query.discoverRegionId}
            </if>
            <if test="query.startDangerLevel != null and query.endDangerLevel != null">
                and tb1.danger_level &gt;= #{query.startDangerLevel}
                and tb1.danger_level &lt;= #{query.endDangerLevel}
            </if>
            <if test="query.handleStatus != null">
                and tb1.handle_status = #{query.handleStatus}
            </if>
        </where>
    </select>

    <select id="pageByUserId" resultType="com.allin.silas.event.adapter.vo.EventInvasionPageVo">
        select distinct tb1.id,
                        tb1.source,
                        tb1.source_id,
                        tb1.event_type,
                        tb1.discover_time,
                        tb1.discover_region_id,
                        tb1.altitude,
                        tb1.danger_level,
                        tb1.handle_status
        from event_invasion tb1
                 left join event_invasion_handle_person tb2 on tb1.id = tb2.event_invasion_id
        <where>
            tb1.project_id = #{query.projectId}
              and tb1.is_deleted = 0
              and tb2.handle_user_id = #{createdBy}
            <if test="query.source != null">
                and tb1.source = #{query.source}
            </if>
            <if test="query.eventType != null">
                and tb1.event_type = #{query.eventType}
            </if>
            <if test="query.startDiscoverTime != null and query.endDiscoverTime != null">
                and tb1.discover_time &gt;= #{query.startDiscoverTime}
                and tb1.discover_time &lt;= #{query.endDiscoverTime}
            </if>
            <if test="query.discoverRegionId != null">
                and tb1.discover_region_id = #{query.discoverRegionId}
            </if>
            <if test="query.startDangerLevel != null and query.endDangerLevel != null">
                and tb1.danger_level &gt;= #{query.startDangerLevel}
                and tb1.danger_level &lt;= #{query.endDangerLevel}
            </if>
            <if test="query.handleStatus != null">
                and tb1.handle_status = #{query.handleStatus}
            </if>
        </where>
    </select>

    <select id="statisticsDangerLevel" resultType="java.util.Map">
        select tb1.danger_level as key, count(*) as value
        from event_invasion tb1
        <where>
            tb1.project_id = #{query.projectId}
              and tb1.is_deleted = 0
            <if test="query.source != null">
                and tb1.source = #{query.source}
            </if>
            <if test="query.eventType != null">
                and tb1.event_type = #{query.eventType}
            </if>
            <if test="query.startDiscoverTime != null and query.endDiscoverTime != null">
                and tb1.discover_time &gt;= #{query.startDiscoverTime}
                and tb1.discover_time &lt;= #{query.endDiscoverTime}
            </if>
            <if test="query.discoverRegionId != null">
                and tb1.discover_region_id = #{query.discoverRegionId}
            </if>
            <if test="query.startDangerLevel != null and query.endDangerLevel != null">
                and tb1.danger_level &gt;= #{query.startDangerLevel}
                and tb1.danger_level &lt;= #{query.endDangerLevel}
            </if>
            <if test="query.handleStatus != null">
                and tb1.handle_status = #{query.handleStatus}
            </if>
        </where>
        group by tb1.danger_level
    </select>

    <select id="statisticsHandleStatus" resultType="java.util.Map">
        select tb1.handle_status as key, count(*) as value
        from event_invasion tb1
        <where>
            tb1.project_id = #{query.projectId}
              and tb1.is_deleted = 0
            <if test="query.source != null">
                and tb1.source = #{query.source}
            </if>
            <if test="query.eventType != null">
                and tb1.event_type = #{query.eventType}
            </if>
            <if test="query.startDiscoverTime != null and query.endDiscoverTime != null">
                and tb1.discover_time &gt;= #{query.startDiscoverTime}
                and tb1.discover_time &lt;= #{query.endDiscoverTime}
            </if>
            <if test="query.discoverRegionId != null">
                and tb1.discover_region_id = #{query.discoverRegionId}
            </if>
            <if test="query.startDangerLevel != null and query.endDangerLevel != null">
                and tb1.danger_level &gt;= #{query.startDangerLevel}
                and tb1.danger_level &lt;= #{query.endDangerLevel}
            </if>
            <if test="query.handleStatus != null">
                and tb1.handle_status = #{query.handleStatus}
            </if>
        </where>
        group by tb1.handle_status
    </select>

    <select id="statisticsDangerLevelByUserId" resultType="java.util.Map">
        select tb1.danger_level as key, count(distinct tb1.id) as value
        from event_invasion tb1
                 left join event_invasion_handle_person tb2 on tb1.id = tb2.event_invasion_id
        <where>
            tb1.project_id = #{query.projectId}
              and tb1.is_deleted = 0
              and tb2.handle_user_id = #{createdBy}
            <if test="query.source != null">
                and tb1.source = #{query.source}
            </if>
            <if test="query.eventType != null">
                and tb1.event_type = #{query.eventType}
            </if>
            <if test="query.startDiscoverTime != null and query.endDiscoverTime != null">
                and tb1.discover_time &gt;= #{query.startDiscoverTime}
                and tb1.discover_time &lt;= #{query.endDiscoverTime}
            </if>
            <if test="query.discoverRegionId != null">
                and tb1.discover_region_id = #{query.discoverRegionId}
            </if>
            <if test="query.startDangerLevel != null and query.endDangerLevel != null">
                and tb1.danger_level &gt;= #{query.startDangerLevel}
                and tb1.danger_level &lt;= #{query.endDangerLevel}
            </if>
            <if test="query.handleStatus != null">
                and tb1.handle_status = #{query.handleStatus}
            </if>
        </where>
        group by tb1.danger_level
    </select>

    <select id="statisticsHandleStatusByUserId" resultType="java.util.Map">
        select tb1.handle_status as key, count(distinct tb1.id) as value
        from event_invasion tb1
                 left join event_invasion_handle_person tb2 on tb1.id = tb2.event_invasion_id
        <where>
            tb1.project_id = #{query.projectId}
              and tb1.is_deleted = 0
              and tb2.handle_user_id = #{createdBy}
            <if test="query.source != null">
                and tb1.source = #{query.source}
            </if>
            <if test="query.eventType != null">
                and tb1.event_type = #{query.eventType}
            </if>
            <if test="query.startDiscoverTime != null and query.endDiscoverTime != null">
                and tb1.discover_time &gt;= #{query.startDiscoverTime}
                and tb1.discover_time &lt;= #{query.endDiscoverTime}
            </if>
            <if test="query.discoverRegionId != null">
                and tb1.discover_region_id = #{query.discoverRegionId}
            </if>
            <if test="query.startDangerLevel != null and query.endDangerLevel != null">
                and tb1.danger_level &gt;= #{query.startDangerLevel}
                and tb1.danger_level &lt;= #{query.endDangerLevel}
            </if>
            <if test="query.handleStatus != null">
                and tb1.handle_status = #{query.handleStatus}
            </if>
        </where>
        group by tb1.handle_status
    </select>

    <select id="unhandled" resultType="java.lang.Integer">
        select count(*)
        from event_invasion tb1
                 left join event_invasion_handle_person tb2 on tb1.id = tb2.event_invasion_id
        <where>
            tb1.project_id = #{projectId}
              and tb1.is_deleted = 0
              and tb2.handle_user_id = #{userId}
              and tb1.handle_status = 0
        </where>
    </select>
</mapper>