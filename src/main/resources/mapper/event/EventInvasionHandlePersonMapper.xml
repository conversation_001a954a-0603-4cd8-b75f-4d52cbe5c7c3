<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.event.infra.repository.EventInvasionHandlePersonMapper">
    <select id="listByEventInvasionId" resultType="com.allin.silas.event.app.entity.EventInvasionHandlePerson">
        select *
        from event_invasion_handle_person
        where event_invasion_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>