<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.kn.infra.repository.KnBirdInfoActiveMonthMapper">

    <select id="listMonthByProjectId" resultType="java.lang.Integer">
        select month
        from kn_bird_info_active_month
        where chinese_name = #{chineseName}
          and project_id = #{projectId}
    </select>
</mapper>