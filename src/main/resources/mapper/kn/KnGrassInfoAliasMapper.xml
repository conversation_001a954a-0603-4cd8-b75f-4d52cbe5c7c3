<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.kn.infra.repository.KnGrassInfoAliasMapper">
    <select id="listBuiltInAlias" resultType="java.lang.String">
        select alias
        from kn_grass_info_alias
        where chinese_name = #{chineseName}
          and project_id is null
    </select>

    <select id="listBuiltInAndProjectAlias" resultType="java.lang.String">
        select alias
        from kn_grass_info_alias
        where chinese_name = #{chineseName}
          and project_id = #{projectId}
    </select>

    <select id="listBuiltIn" resultType="com.allin.silas.kn.app.entity.KnGrassInfoAlias">
        select *
        from kn_grass_info_alias
        where project_id is null
    </select>

    <select id="listBuiltInAndProject" resultType="com.allin.silas.kn.app.entity.KnGrassInfoAlias">
        select *
        from kn_grass_info_alias
        where (project_id is null and project_id = #{projectId})
    </select>
</mapper>