<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.kn.infra.repository.KnInsectInfoImageMapper">

    <select id="getById" resultType="java.lang.String">
        select image_base64
        from kn_insect_info_image
        where id = #{id}
    </select>

    <select id="listBuiltInAndProject" resultType="com.allin.silas.kn.app.entity.KnInsectInfoImage">
        select id, project_id
        from kn_insect_info_image
        where chinese_name = #{chineseName}
          and (project_id = #{projectId} or project_id is null)
        order by id
    </select>

    <select id="listBuiltIn" resultType="com.allin.silas.kn.adapter.vo.KnImageBaseInfoVo">
        select id, chinese_name, project_id
        from kn_insect_info_image
        where project_id is null
        order by id
    </select>
</mapper>