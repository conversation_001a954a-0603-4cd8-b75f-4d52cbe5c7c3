<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.kn.infra.repository.KnBirdInfoBaseMapper">
    <select id="listBaseInfo" resultType="com.allin.silas.kn.adapter.vo.KnBirdInfoBaseVo">
        select tb1.chinese_name, tb1.sci_name, tb1.order_name, tb1.family_name, tb1.genus_name, tb2.danger_level
        from kn_bird_info_base tb1
                 left join kn_bird_info_danger_level tb2 on tb1.chinese_name = tb2.chinese_name
    </select>
</mapper>