<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.dev.drive.infra.repository.DevDriveInfoMapper">

    <select id="findPage" resultType="com.allin.silas.dev.drive.adapter.vo.DevDriveInfoVo">
        SELECT
        tb1.*,
        tb4.dev_status,
        tb4.dev_run_status
        FROM dev_drive_info AS tb1
        LEFT JOIN (
        SELECT ds.*
        FROM dev_drive_status AS ds
        INNER JOIN (
        SELECT dev_num, MAX(id) AS id
        FROM dev_drive_status
        GROUP BY dev_num
        ) AS max_ds ON ds.dev_num = max_ds.dev_num AND ds.id = max_ds.id
        ) AS tb4 ON tb1.dev_num = tb4.dev_num
        <where>
            <if test="param.projectId != null">
                AND tb1.project_id = #{param.projectId}
            </if>
            <if test="param.devNum != null and param.devNum != ''">
                AND tb1.dev_num = #{param.devNum}
            </if>
            <if test="param.devName != null and param.devName != ''">
                AND tb1.dev_name = #{param.devName}
            </if>
            <if test="param.devStatus != null">
                AND tb4.dev_status = #{param.devStatus}
            </if>
            <if test="param.devType != null and param.devType != ''">
                AND tb1.dev_type = #{param.devType}
            </if>
        </where>
        ORDER BY tb1.id DESC
    </select>


</mapper>
