<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.dev.detect.infra.repository.DevDetectInfoMapper">
    <select id="findList" resultType="com.allin.silas.dev.detect.adapter.vo.DevDetectInfoVo">
        SELECT tb1.*, tb2.dev_status, tb2.dev_run_status
        FROM dev_detect_info AS tb1
                 LEFT JOIN (SELECT tb2.*
                            FROM dev_detect_status AS tb2
                                     INNER JOIN (SELECT dev_num, MAX(id) AS id
                                                 FROM dev_detect_status
                                                 GROUP BY dev_num) AS tb3
                                                ON tb2.dev_num = tb3.dev_num AND tb2.id = tb3.id) AS tb2
                           ON tb1.dev_num = tb2.dev_num
        <where>
            <if test="param.projectId != null">
                AND tb1.project_id = #{param.projectId}
            </if>
            <if test="param.devName != null and param.devName != ''">
                AND tb1.dev_name LIKE concat('%', #{param.devName}, '%')
            </if>
            <if test="param.devNum != null and param.devNum != ''">
                AND tb1.dev_num LIKE concat('%', #{param.devNum}, '%')
            </if>
            <if test="param.devType != null and param.devType != ''">
                AND tb1.dev_type = #{param.devType}
            </if>
            <if test="param.devStatus != null and param.devStatus != ''">
                AND tb2.dev_status = #{param.devStatus}
            </if>
        </where>
    </select>
</mapper>
