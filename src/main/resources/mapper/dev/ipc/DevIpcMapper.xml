<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.dev.ipc.infra.repository.DevIpcMapper">

    <!--拷贝此句代码-->
    <select id="getIpcPage" resultType="com.allin.silas.dev.ipc.adapter.vo.DevIpcVo">
        select tb1.*,
        tb2.ip_address as aiDetectionServerIp,
        tb2.server_name as aiDetectionServerName
        from dev_ipc tb1
        left join dev_server tb2 on tb1.ai_detection_server_id = tb2.id
        <where>
            tb1.deleted = 0
            <if test="query.ipcName != null and query.ipcName != ''">
                and tb1.ipc_name like concat('%',#{query.ipcName}, '%')
            </if>
            <if test="query.ipcNum != null and query.ipcNum != ''">
                and tb1.ipc_num like concat('%',#{query.ipcNum}, '%')
            </if>
            <if test="query.ipcType != null">
                and tb1.ipc_type = #{query.ipcType}
            </if>
            <if test="query.ipcCompany != null and query.ipcCompany != ''">
                and tb1.ipc_company = #{query.ipcCompany}
            </if>
            <if test="query.ip != null and query.ip != ''">
                and tb1.ip like concat('%',#{query.ip}, '%')
            </if>
            <if test="query.channel != null">
                and tb1.channel = #{query.channel}
            </if>
            <if test="query.stream != null">
                and tb1.stream = #{query.stream}
            </if>
            <if test="query.userName != null and query.userName != ''">
                and tb1.user_name like concat('%',#{query.userName}, '%')
            </if>
            <if test="query.password != null and query.password != ''">
                and tb1.password like concat('%',#{query.password}, '%')
            </if>
            <if test="query.height != null">
                and tb1.height = #{query.height}
            </if>
            <if test="query.longitude != null and query.longitude != ''">
                and tb1.longitude like concat('%',#{query.longitude}, '%')
            </if>
            <if test="query.latitude != null and query.latitude != ''">
                and tb1.latitude like concat('%',#{query.latitude}, '%')
            </if>
            <if test="query.isAiDetection != null">
                and tb1.is_ai_detection = #{query.isAiDetection}
            </if>
            <if test="query.backIp != null and query.backIp != ''">
                and tb1.back_ip like concat('%',#{query.backIp}, '%')
            </if>
            <if test="query.backUserName != null and query.backUserName != ''">
                and tb1.back_user_name like concat('%',#{query.backUserName}, '%')
            </if>
            <if test="query.backPassword != null and query.backPassword != ''">
                and tb1.back_password like concat('%',#{query.backPassword}, '%')
            </if>
            <if test="query.backChannel != null">
                and tb1.back_channel = #{query.backChannel}
            </if>
            <if test="query.backStream != null">
                and tb1.back_stream = #{query.backStream}
            </if>
            <if test="query.projectId != null and query.projectId != ''">
                and tb1.project_id = #{query.projectId}
            </if>
            <if test="query.devNums != null and !query.devNums.isEmpty() ">
                AND tb1.id in
                <foreach collection="query.devNums" item="devNum" index="index" open="(" close=")" separator=",">
                    #{devNum}
                </foreach>
            </if>
            <if test="query.ipcTypes != null and !query.ipcTypes.isEmpty() ">
                AND tb1.ipc_type in
                <foreach collection="query.ipcTypes" item="ipcType" index="index" open="(" close=")" separator=",">
                    #{ipcType}
                </foreach>
            </if>
            <if test="query.aiDetectionServerId != null and query.aiDetectionServerId != ''">
                and tb1.ai_detection_server_id = #{query.aiDetectionServerId}
            </if>
            <if test="query.mediaServerIp != null and query.mediaServerIp != ''">
                and tb1.media_server_ip = #{query.mediaServerIp}
            </if>
            <if test="query.backMediaServerIp != null and query.backMediaServerIp != ''">
                and tb1.back_media_server_ip like concat('%', #{query.backMediaServerIp}, '%') and (tb1.back_ip is not
                null or tb1.back_sip_user_name is not null)
            </if>
            <if test="query.numberSegment != null and query.numberSegment != ''">
                and tb1.number_segment = #{query.numberSegment}
            </if>
        </where>
        order by tb1.dev_sort asc, tb1.ipc_name asc
    </select>

</mapper>
