<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.dev.server.infra.repository.DevServerMapper">
    <!--拷贝此句代码-->
    <select id="getDevServerPage" resultType="com.allin.silas.dev.server.adapter.vo.DevServerVo">
        select tb1.* from dev_server tb1
        <where>
            tb1.deleted = 0
            <if test="query.serverName != null and query.serverName != ''">
                and tb1.server_name like concat('%',#{query.serverName}, '%')
            </if>
            <if test="query.serverNumber != null and query.serverNumber != ''">
                and tb1.server_number like concat('%',#{query.serverNumber}, '%')
            </if>
            <if test="query.serverType != null">
                and tb1.server_type = #{query.serverType}
            </if>
            <if test="query.ipAddress != null and query.ipAddress != ''">
                and tb1.ip_address like concat('%',#{query.ipAddress}, '%')
            </if>
            <if test="query.communicationPort != null">
                and tb1.communication_port = #{query.communicationPort}
            </if>
            <if test="query.userName != null and query.userName != ''">
                and tb1.user_name like concat('%',#{query.userName}, '%')
            </if>
            <if test="query.status != null">
                and tb1.status = #{query.status}
            </if>
            <if test="query.projectId != null and query.projectId != ''">
                and tb1.project_id = #{query.projectId}
            </if>
            <if test="query.serverTypes != null and !query.serverTypes.isEmpty() ">
                AND tb1.server_type in
                <foreach collection="query.serverTypes" item="serverType" index="index" open="(" close=")" separator=",">
                    #{serverType}
                </foreach>
            </if>
        </where>
        order by tb1.updated_time desc
    </select>
</mapper>
