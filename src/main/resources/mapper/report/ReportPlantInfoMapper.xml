<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.report.infra.repository.ReportPlantInfoMapper">
  <resultMap id="BaseResultMap" type="com.allin.silas.report.app.entity.ReportPlantInfo">
    <!--@mbg.generated-->
    <!--@Table report_plant_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="discover_time" jdbcType="TIMESTAMP" property="discoverTime" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="plant_name" jdbcType="VARCHAR" property="plantName" />
    <result column="plant_avg_height" jdbcType="INTEGER" property="plantAvgHeight" />
    <result column="plant_growth_speed" jdbcType="INTEGER" property="plantGrowthSpeed" />
    <result column="distribution_area" jdbcType="NUMERIC" property="distributionArea" />
    <result column="attachment" jdbcType="VARCHAR" property="attachment" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, discover_time, region_id, plant_name, plant_avg_height, plant_growth_speed, distribution_area, 
    attachment, remarks, project_id, created_by, created_time, updated_by, updated_time, 
    is_deleted
  </sql>
</mapper>