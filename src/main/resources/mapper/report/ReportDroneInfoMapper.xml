<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.report.infra.repository.ReportDroneInfoMapper">
    <resultMap id="BaseResultMap" type="com.allin.silas.report.app.entity.ReportDroneInfo">
        <!--@mbg.generated-->
        <!--@Table report_drone_info-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="discover_time" jdbcType="TIMESTAMP" property="discoverTime"/>
        <result column="region_id" jdbcType="VARCHAR" property="regionId"/>
        <result column="longitude" jdbcType="DOUBLE" property="longitude"/>
        <result column="latitude" jdbcType="DOUBLE" property="latitude"/>
        <result column="height" jdbcType="INTEGER" property="height"/>
        <result column="is_airspace_regulated" jdbcType="SMALLINT" property="isAirspaceRegulated"/>
        <result column="drone_type" jdbcType="SMALLINT" property="droneType"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="attachment" jdbcType="VARCHAR" property="attachment"/>
        <result column="handle_status" jdbcType="SMALLINT" property="handleStatus"/>
        <result column="project_id" jdbcType="VARCHAR" property="projectId"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
</mapper>