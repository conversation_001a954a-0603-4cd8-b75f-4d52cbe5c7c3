<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.report.infra.repository.ReportBirdNetInfoMapper">
  <resultMap id="BaseResultMap" type="com.allin.silas.report.app.entity.ReportBirdNetInfo">
    <!--@mbg.generated-->
    <!--@Table report_bird_net_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="discover_time" jdbcType="TIMESTAMP" property="discoverTime" />
    <result column="bird_net_id" jdbcType="VARCHAR" property="birdNetId" />
    <result column="bird_name" jdbcType="VARCHAR" property="birdName" />
    <result column="bird_risk_level" jdbcType="INTEGER" property="birdRiskLevel" />
    <result column="bird_count" jdbcType="INTEGER" property="birdCount" />
    <result column="attachment" jdbcType="VARCHAR" property="attachment" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, discover_time, bird_net_id, bird_name, bird_risk_level, bird_count, attachment, 
    remarks, project_id, created_by, created_time, updated_by, updated_time, is_deleted
  </sql>
</mapper>