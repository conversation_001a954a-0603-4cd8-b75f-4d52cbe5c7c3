<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.report.infra.repository.ReportInsectInfoMapper">
  <resultMap id="BaseResultMap" type="com.allin.silas.report.app.entity.ReportInsectInfo">
    <!--@mbg.generated-->
    <!--@Table report_insect_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="discover_time" jdbcType="TIMESTAMP" property="discoverTime" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="insect_name" jdbcType="VARCHAR" property="insectName" />
    <result column="insect_level" jdbcType="INTEGER" property="insectLevel" />
    <result column="insect_count" jdbcType="INTEGER" property="insectCount" />
    <result column="distribution_area" jdbcType="NUMERIC" property="distributionArea" />
    <result column="attachment" jdbcType="VARCHAR" property="attachment" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
  </resultMap>
</mapper>