<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.report.infra.repository.ReportDiscoverPersonMapper">
  <resultMap id="BaseResultMap" type="com.allin.silas.report.app.entity.ReportDiscoverPerson">
    <!--@mbg.generated-->
    <!--@Table report_discover_person-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="report_type" jdbcType="SMALLINT" property="reportType" />
    <result column="report_id" jdbcType="VARCHAR" property="reportId" />
    <result column="discover_user_id" jdbcType="VARCHAR" property="discoverUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, report_type, report_id, discover_user_id
  </sql>
</mapper>