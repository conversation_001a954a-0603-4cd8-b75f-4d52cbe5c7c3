<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.visual.infra.repository.VisualTargetTraceRegionMapper">
    <select id="listByBatchNumbers" resultType="com.allin.silas.visual.adapter.vo.VisualTargetTraceRegionVo">
        select tb1.backend_batch_number,
               tb1.map_region_id,
               tb1.created_time,
               tb2.region_name
        from visual_target_trace_region tb1
                 left join map_region tb2 on tb1.map_region_id = tb2.id
        where backend_batch_number in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getLastRegion" resultType="com.allin.silas.visual.adapter.vo.VisualTargetTraceRegionVo">
        select tb1.backend_batch_number,
               tb1.map_region_id,
               tb1.created_time,
               tb2.region_name
        from visual_target_trace_region tb1
                 left join map_region tb2 on tb1.map_region_id = tb2.id
        where backend_batch_number = #{backendBatchNumber}
        order by tb1.created_time desc
        limit 1
    </select>
</mapper>