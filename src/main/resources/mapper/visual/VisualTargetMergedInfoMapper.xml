<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.visual.infra.repository.VisualTargetMergedInfoMapper">
    <sql id="listColumns">
        <!--@sql select-->
        tb1.dev_num,
        tb1.dev_type,
        tb1.backend_batch_number,
        tb1.max_target_count,
        tb1.min_runway_distance,
        tb1.max_runway_distance,
        tb1.start_flight_direction,
        tb1.end_flight_direction,
        tb1.min_wing_span,
        tb1.max_wing_span,
        tb1.min_distance,
        tb1.max_distance,
        tb1.min_height,
        tb1.max_height,
        tb1.max_confidence_level,
        tb1.target_size,
        tb1.detect_sub_type,
        tb1.detect_type,
        tb1.mark_type,
        tb1.mark_sub_type,
        tb1.start_img_url,
        tb1.end_img_url,
        tb1.danger_level,
        tb1.push_status,
        tb1.start_time,
        tb1.end_time,
        tb1.dev_run_model
        <!--@sql from visual_target_merged_info tb1-->
    </sql>

    <!--$var tableName= visual_target_merged_info-->
    <select id="page" resultType="com.allin.silas.visual.adapter.vo.VisualTargetInfoListVo">
        select
        <include refid="listColumns"/>
        from ${tableName} tb1
        <where>
            tb1.project_id = #{query.projectId}
              AND tb1.start_time &lt;= #{query.endTime}
              AND tb1.end_time >= #{query.startTime}
              AND tb1.is_deleted = 0
            <if test="query.pushStatus != null">
                AND tb1.push_status = #{query.pushStatus}
            </if>
            <if test="query.targetSize != null">
                AND tb1.target_size = #{query.targetSize}
            </if>
            <if test="query.devNums != null and query.devNums.size() != 0">
                AND tb1.dev_num in
                <foreach item="item" collection="query.devNums" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.devTypes != null and query.devTypes.size() != 0">
                AND tb1.dev_type in
                <foreach item="item" collection="query.devTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.dangerLevels != null and query.dangerLevels.size() != 0">
                AND tb1.danger_level in
                <foreach item="item" index="index" collection="query.dangerLevels" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.minHeight != null and query.maxHeight != null">
                AND (tb1.min_height &lt;= #{query.maxHeight}
                    AND tb1.max_height >= #{query.minHeight})
            </if>
            <if test="query.minRunwayDistance != null and query.maxRunwayDistance != null">
                AND (tb1.min_runway_distance &lt;= #{query.maxRunwayDistance}
                    AND tb1.max_runway_distance >= #{query.minRunwayDistance})
            </if>
            <if test="query.detectTypes != null and query.detectTypes.size() != 0">
                AND (tb1.detect_type in
                <foreach item="item" collection="query.detectTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
                <if test="query.isQueryNullDetectType != null and query.isQueryNullDetectType == 1">
                    or tb1.detect_type is null
                </if>)
            </if>
            <if test="query.detectSubTypes != null and query.detectSubTypes.size() > 0">
                AND (tb1.detect_sub_type in
                <foreach item="item" collection="query.detectSubTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
                <if test="query.isQueryNullDetectType != null and query.isQueryNullDetectType == 1">
                    or tb1.detect_sub_type is null
                </if>)
            </if>
            <if test="query.markTypes != null and query.markTypes.size() > 0">
                AND tb1.mark_type in
                <foreach item="item" collection="query.markTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.markSubTypes != null and query.markSubTypes.size() > 0">
                AND tb1.mark_sub_type in
                <foreach item="item" collection="query.markSubTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.regionIds != null and query.regionIds.size() > 0">
                AND EXISTS (
                select 1
                from visual_target_trace_region tb2
                where tb2.backend_batch_number = tb1.backend_batch_number
                  and tb2.map_region_id in
                <foreach item="item" collection="query.regionIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
        </where>
        ORDER BY tb1.start_time DESC
    </select>

    <!--$var tableName= visual_target_merged_info-->
    <select id="list" resultType="com.allin.silas.visual.adapter.vo.VisualTargetInfoListVo">
        select
        <include refid="listColumns"/>
        from ${tableName} tb1
        <where>
            tb1.project_id = #{query.projectId}
              AND tb1.start_time &lt;= #{query.endTime}
              AND tb1.end_time >= #{query.startTime}
              AND tb1.is_deleted = 0
            <if test="query.pushStatus != null">
                AND tb1.push_status = #{query.pushStatus}
            </if>
            <if test="query.targetSize != null">
                AND tb1.target_size = #{query.targetSize}
            </if>
            <if test="query.devNums != null and query.devNums.size() != 0">
                AND tb1.dev_num in
                <foreach item="item" collection="query.devNums" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.devTypes != null and query.devTypes.size() != 0">
                AND tb1.dev_type in
                <foreach item="item" collection="query.devTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.batchNumbers != null and query.batchNumbers.size() != 0">
                AND tb1.backend_batch_number in
                <foreach item="item" collection="query.batchNumbers" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.dangerLevels != null and query.dangerLevels.size() != 0">
                AND tb1.danger_level in
                <foreach item="item" index="index" collection="query.dangerLevels" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.minHeight != null and query.maxHeight != null">
                AND (tb1.min_height &lt;= #{query.maxHeight}
                    AND tb1.max_height >= #{query.minHeight})
            </if>
            <if test="query.minRunwayDistance != null and query.maxRunwayDistance != null">
                AND (tb1.min_runway_distance &lt;= #{query.maxRunwayDistance}
                    AND tb1.max_runway_distance >= #{query.minRunwayDistance})
            </if>
            <if test="query.detectTypes != null and query.detectTypes.size() != 0">
                AND (tb1.detect_type in
                <foreach item="item" collection="query.detectTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
                <if test="query.isQueryNullDetectType != null and query.isQueryNullDetectType == 1">
                    or tb1.detect_type is null
                </if>)
            </if>
            <if test="query.detectSubTypes != null and query.detectSubTypes.size() > 0">
                AND (tb1.detect_sub_type in
                <foreach item="item" collection="query.detectSubTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
                <if test="query.isQueryNullDetectType != null and query.isQueryNullDetectType == 1">
                    or tb1.detect_sub_type is null
                </if>)
            </if>
            <if test="query.markTypes != null and query.markTypes.size() > 0">
                AND tb1.mark_type in
                <foreach item="item" collection="query.markTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.markSubTypes != null and query.markSubTypes.size() > 0">
                AND tb1.mark_type in
                <foreach item="item" collection="query.markSubTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="query.regionIds != null and query.regionIds.size() > 0">
                AND EXISTS (
                select 1
                from visual_target_trace_region tb2
                where tb2.backend_batch_number = tb1.backend_batch_number
                  and tb2.map_region_id in
                <foreach item="item" collection="query.regionIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
        </where>
        ORDER BY tb1.start_time DESC
    </select>

    <!--$var tableName= visual_target_merged_info-->
    <select id="info" resultType="com.allin.silas.visual.adapter.vo.VisualTargetMergedInfoVo">
        select tb1.dev_num,
               tb1.backend_batch_number,
               tb1.start_longitude,
               tb1.start_latitude,
               tb1.end_longitude,
               tb1.end_latitude,
               tb1.max_target_count,
               tb1.min_runway_distance,
               tb1.max_runway_distance,
               tb1.min_runway_center_distance,
               tb1.max_runway_center_distance,
               tb1.max_height,
               tb1.start_flight_direction,
               tb1.end_flight_direction,
               tb1.detect_type,
               tb1.detect_sub_type,
               tb1.mark_type,
               tb1.mark_sub_type,
               tb1.start_img_url,
               tb1.end_img_url,
               tb1.danger_level,
               tb1.push_status,
               tb1.start_time,
               tb1.end_time
        from ${tableName} tb1
        where tb1.backend_batch_number = #{backendBatchNumber}
    </select>

    <!--$var tableName= visual_target_merged_info-->
    <update id="updateMark">
        update ${tableName}
        set mark_type     = #{mark.markType},
            mark_sub_type = #{mark.markSubType},
            danger_level  = #{mark.dangerLevel}
        where backend_batch_number = #{mark.backendBatchNumber}
    </update>

    <!--$var tableName= visual_target_merged_info-->
    <update id="setPushed">
        update ${tableName}
        set push_status = 1
        where backend_batch_number = #{batchNumber}
    </update>

    <!--$var tableName= visual_target_merged_info-->
    <select id="listNotMerged" resultType="com.allin.silas.visual.app.entity.VisualTargetMergedInfo">
        select *
        from ${tableName}
        where merged_status = 0
    </select>
</mapper>