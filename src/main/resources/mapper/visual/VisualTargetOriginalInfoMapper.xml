<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.visual.infra.repository.VisualTargetOriginalInfoMapper">
    <!--$var tableName= visual_target_original_info-->
    <select id="trace" resultType="com.allin.silas.visual.adapter.vo.VisualTargetOriginalTraceVo">
        SELECT longitude,
               latitude,
               azimuth,
               pitch,
               runway_distance,
               runway_center_distance,
               height,
               speed,
               wing_span,
               flight_direction,
               area,
               img_url,
               created_time
        FROM ${tableName}
        WHERE backend_batch_number = #{batchNumber}
        ORDER BY created_time ASC
    </select>

    <!--$var tableName= visual_target_original_info-->
    <select id="listNotMergedAndSortCreatedTimeDesc"
            resultType="com.allin.silas.visual.adapter.vo.VisualTargetOriginalMergedVo">
        SELECT id,
               backend_batch_number,
               longitude,
               latitude,
               azimuth,
               pitch,
               distance,
               runway_distance,
               runway_center_distance,
               height,
               speed,
               wing_span,
               flight_direction,
               area,
               confidence_level,
               target_count,
               img_url,
               created_time,
               detect_sub_type,
               target_size
        FROM ${tableName}
        WHERE backend_batch_number in
        <foreach item="item" index="index" collection="batchNumbers" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--$var tableName= visual_target_original_info-->
    <select id="listImgPath" resultType="java.lang.String">
        SELECT DISTINCT img_url
        FROM ${tableName}
        WHERE backend_batch_number in
        <foreach item="item" index="index" collection="batchNumbers" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>