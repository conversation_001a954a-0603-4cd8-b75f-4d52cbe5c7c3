<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.radar.infra.repository.RadarTargetMergedInfoMapper">
    <sql id="listColumns">
        <!--@sql select-->
        tb1.id,
        tb1.dev_num,
        tb1.dev_type,
        tb1.backend_batch_number,
        tb1.detect_type,
        tb1.danger_level,
        tb1.collision_possibility,
        tb1.start_time,
        tb1.end_time,
        tb1.min_confidence_level,
        tb1.max_confidence_level,
        tb1.mark_type,
        tb1.min_height,
        tb1.max_height,
        tb1.min_distance,
        tb1.max_distance,
        tb1.min_runway_distance,
        tb1.max_runway_distance,
        tb1.min_azimuth,
        tb1.max_azimuth,
        tb1.min_pitch,
        tb1.max_pitch,
        tb1.min_echo_amplitude,
        tb1.max_echo_amplitude,
        tb1.min_speed,
        tb1.max_speed,
        tb1.min_snr,
        tb1.max_snr,
        tb1.min_rcs,
        tb1.max_rcs,
        tb1.push_status,
        tb2.visual_batch_number AS visualBatchNumber,
        tb2.visual_dev_num      AS visualDevNum
        <!--@sql from radar_target_merged_info tb1 LEFT JOIN radar_visual_target_relation tb2 ON tb1.backend_batch_number = tb2.radar_batch_number-->
    </sql>

    <!--$var tableName= radar_target_merged_info-->
    <select id="page" resultType="com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoPageVo">
        SELECT
        <include refid="listColumns"/>
        FROM ${tableName} tb1
        <choose>
            <when test="query.isRelatedVisualTarget != null and query.isRelatedVisualTarget == 1">
                INNER JOIN radar_visual_target_relation tb2 ON tb1.backend_batch_number = tb2.radar_batch_number
                    AND tb2.created_time >= #{query.startTime}
                    AND tb2.created_time &lt; #{query.relationEndTime}
            </when>
            <otherwise>
                LEFT JOIN radar_visual_target_relation tb2 ON tb1.backend_batch_number = tb2.radar_batch_number
                    AND tb2.created_time >= #{query.startTime}
                    AND tb2.created_time &lt; #{query.relationEndTime}
            </otherwise>
        </choose>
        <where>
            tb1.project_id = #{query.projectId}
              AND tb1.start_time &lt;= #{query.endTime}
              AND tb1.end_time >= #{query.startTime}
              AND tb1.is_deleted = 0
            <if test="query.isRelatedVisualTarget != null and query.isRelatedVisualTarget == 0">
                AND tb2.radar_batch_number IS NULL
            </if>
            <if test="query.detectTypes != null and query.detectTypes.size() > 0">
                AND tb1.detect_type IN
                <foreach item="item" collection="query.detectTypes" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.minHeight != null">
                AND tb1.max_height >= #{query.minHeight}
            </if>
            <if test="query.maxHeight != null">
                AND tb1.min_height &lt;= #{query.maxHeight}
            </if>
            <if test="query.minDistance != null">
                AND tb1.max_distance >= #{query.minDistance}
            </if>
            <if test="query.maxDistance != null">
                AND tb1.min_distance &lt;= #{query.maxDistance}
            </if>
            <if test="query.minRunwayDistance != null">
                AND tb1.max_runway_distance >= #{query.minRunwayDistance}
            </if>
            <if test="query.maxRunwayDistance != null">
                AND tb1.min_runway_distance &lt;= #{query.maxRunwayDistance}
            </if>
            <if test="query.minAzimuth != null">
                AND tb1.max_azimuth >= #{query.minAzimuth}
            </if>
            <if test="query.maxAzimuth != null">
                AND tb1.min_azimuth &lt;= #{query.maxAzimuth}
            </if>
            <if test="query.pushStatus != null">
                AND tb1.push_status = #{query.pushStatus}
            </if>
            <if test="query.dangerLevels != null and query.dangerLevels.size() > 0">
                AND tb1.danger_level IN
                <foreach item="item" collection="query.dangerLevels" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY tb1.start_time DESC
    </select>

    <select id="list" resultType="com.allin.silas.radar.adapter.vo.RadarTargetMergedInfoExportVo">
        SELECT
        <include refid="listColumns"/>
        FROM ${tableName} tb1
        <choose>
            <when test="query.isRelatedVisualTarget != null and query.isRelatedVisualTarget == 1">
                INNER JOIN radar_visual_target_relation tb2 ON tb1.backend_batch_number = tb2.radar_batch_number
                    AND tb2.created_time >= #{query.startTime}
                    AND tb2.created_time &lt; #{query.relationEndTime}
            </when>
            <otherwise>
                LEFT JOIN radar_visual_target_relation tb2 ON tb1.backend_batch_number = tb2.radar_batch_number
                    AND tb2.created_time >= #{query.startTime}
                    AND tb2.created_time &lt; #{query.relationEndTime}
            </otherwise>
        </choose>
        <where>
            tb1.project_id = #{query.projectId}
              AND tb1.start_time &lt;= #{query.endTime}
              AND tb1.end_time >= #{query.startTime}
              AND tb1.is_deleted = 0
            <if test="query.isRelatedVisualTarget != null and query.isRelatedVisualTarget == 0">
                AND tb2.radar_batch_number IS NULL
            </if>
            <if test="query.detectTypes != null and query.detectTypes.size() > 0">
                AND tb1.detect_type IN
                <foreach item="item" collection="query.detectTypes" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.minHeight != null">
                AND tb1.max_height >= #{query.minHeight}
            </if>
            <if test="query.maxHeight != null">
                AND tb1.min_height &lt;= #{query.maxHeight}
            </if>
            <if test="query.minDistance != null">
                AND tb1.max_distance >= #{query.minDistance}
            </if>
            <if test="query.maxDistance != null">
                AND tb1.min_distance &lt;= #{query.maxDistance}
            </if>
            <if test="query.minRunwayDistance != null">
                AND tb1.max_runway_distance >= #{query.minRunwayDistance}
            </if>
            <if test="query.maxRunwayDistance != null">
                AND tb1.min_runway_distance &lt;= #{query.maxRunwayDistance}
            </if>
            <if test="query.minAzimuth != null">
                AND tb1.max_azimuth >= #{query.minAzimuth}
            </if>
            <if test="query.maxAzimuth != null">
                AND tb1.min_azimuth &lt;= #{query.maxAzimuth}
            </if>
            <if test="query.pushStatus != null">
                AND tb1.push_status = #{query.pushStatus}
            </if>
            <if test="query.dangerLevels != null and query.dangerLevels.size() > 0">
                AND tb1.danger_level IN
                <foreach item="item" collection="query.dangerLevels" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY tb1.start_time DESC
    </select>

    <!--$var tableName= radar_target_merged_info-->
    <update id="updateMark">
        update ${tableName}
        set mark_type = #{mark.markType}
        where backend_batch_number = #{mark.backendBatchNumber}
    </update>
</mapper>