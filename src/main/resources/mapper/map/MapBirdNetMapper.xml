<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.map.infra.repository.MapBirdNetMapper">
  <resultMap id="BaseResultMap" type="com.allin.silas.map.app.entity.MapBirdNet">
    <!--@mbg.generated-->
    <!--@Table map_bird_net-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="bird_net_name" jdbcType="VARCHAR" property="birdNetName" />
    <result column="bird_net_coord" jdbcType="VARCHAR" property="birdNetCoord" />
    <result column="bird_net_position" jdbcType="INTEGER" property="birdNetPosition" />
    <result column="display_config" jdbcType="VARCHAR" property="displayConfig" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_id, bird_net_name, bird_net_coord, bird_net_position, display_config, 
    remarks, created_by, created_time, updated_by, updated_time, is_deleted
  </sql>
</mapper>