<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.silas.map.infra.repository.MapViewMapper">
  <resultMap id="BaseResultMap" type="com.allin.silas.map.app.entity.MapView">
    <!--@mbg.generated-->
    <!--@Table map_view-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="view_name" jdbcType="VARCHAR" property="viewName" />
    <result column="display_config" jdbcType="VARCHAR" property="displayConfig" />
    <result column="view_type" jdbcType="INTEGER" property="viewType" />
    <result column="is_default" jdbcType="SMALLINT" property="isDefault" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
</mapper>