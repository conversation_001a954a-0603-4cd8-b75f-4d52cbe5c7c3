spring:
  application:
    name: allin-silas-backend
  servlet:
    multipart:
      max-request-size: 1024MB
      max-file-size: 1024MB
  flyway:
    enabled: false
  datasource:
    url: *************************************************
    username: postgres
    password: Allin@019
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 10
      max-active: 20
      keep-alive: true
      min-evictable-idle-time-millis: 600000
      max-evictable-idle-time-millis: 900000
      time-between-eviction-runs-millis: 2000
      max-wait: 1200
      validation-query: SELECT 'x'
      validation-query-timeout: 60
      test-while-idle: true
      filters: stat
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: administrator
        login-password: Allin@022
        allow: ""
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 5000
  data:
    redis:
      host: **************
      port: 6379
      password: Allin@2022
      timeout: 3s
      database: 10
  messages:
    basename:
      - i18n.allin_silas
      - i18n.kn
      - i18n.device
      - i18n.map
  ai:
    mcp:
      server:
        name: allin-silas-backend
        version: 1.0.0

server:
  port: 8898
  tomcat:
    threads:
      max: 500

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: is_deleted # 全局逻辑删除字段名
      logic-delete-value: 1 # 逻辑已删除值
      logic-not-delete-value: 0 # 逻辑未删除值
management:
  server:
    port: -1

allin:
  view:
    auth:
      enable: true
      url: http://**************:8081
      allow-multiple-login: true