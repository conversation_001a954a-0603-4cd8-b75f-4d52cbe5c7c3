package com.allin.silas.radar.performance;

import com.allin.silas.radar.adapter.query.RadarTargetMergedInfoPageQuery;
import com.allin.view.auth.context.SecurityContextHolder;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.time.LocalDateTime;

/**
 * 雷达目标查询条件测试
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
public class RadarTargetQueryPerformanceTest {

    private static MockedStatic<SecurityContextHolder> mockedSecurityContextHolder;

    @BeforeAll
    public static void setUp() {
        // Mock SecurityContextHolder.getProjectId() 方法，固定返回 "1"
        mockedSecurityContextHolder = Mockito.mockStatic(SecurityContextHolder.class);
        mockedSecurityContextHolder.when(SecurityContextHolder::getProjectId).thenReturn("1");
    }

    @AfterAll
    public static void tearDown() {
        // 清理 Mock
        if (mockedSecurityContextHolder != null) {
            mockedSecurityContextHolder.close();
        }
    }


    @Test
    @DisplayName("测试时间范围扩展逻辑")
    public void testTimeRangeExpansion1() {
        // 模拟查询条件：2025-07-14 10:30:00 到 2025-07-14 15:45:00
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 14, 10, 30, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 14, 15, 45, 0);

        // 创建查询对象
        RadarTargetMergedInfoPageQuery query = new RadarTargetMergedInfoPageQuery();
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        query.validateAndInit();

        // 验证时间范围扩展
        System.out.println("原始查询时间范围:");
        System.out.println("开始时间: " + startTime);
        System.out.println("结束时间: " + endTime);

        System.out.println("\n扩展后的关联表查询时间范围:");
        System.out.println("关联结束时间: " + query.getRelationEndTime());
    }

    @Test
    @DisplayName("测试时间范围跨天逻辑")
    public void testTimeRangeExpansion2() {
        // 模拟查询条件：2025-07-14 10:30:00 到 2025-07-14 15:45:00
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 14, 10, 30, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 14, 23, 45, 0);

        // 创建查询对象
        RadarTargetMergedInfoPageQuery query = new RadarTargetMergedInfoPageQuery();
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        query.validateAndInit();

        // 验证时间范围扩展
        System.out.println("原始查询时间范围:");
        System.out.println("开始时间: " + startTime);
        System.out.println("结束时间: " + endTime);

        System.out.println("\n扩展后的关联表查询时间范围:");
        System.out.println("关联结束时间: " + query.getRelationEndTime());
    }
}
