package com.allin.silas.event.infra.repository;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson2.JSON;
import com.allin.silas.common.mybatis.util.MybatisResultUtils;
import com.allin.silas.event.adapter.query.EventInvasionPageQuery;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
class EventInvasionHandleMapperTest {

    @Autowired
    private EventInvasionHandleMapper eventInvasionHandleMapper;

    /**
     * 测试统计危险等级分布
     */
    @Test
    void testStatisticsDangerLevel() {
        // 准备查询条件
        EventInvasionPageQuery query = new EventInvasionPageQuery();
        query.setProjectId("1");

        // 执行统计
        List<Map<String, Object>> result = eventInvasionHandleMapper.statisticsDangerLevel(query);

        final Map<Integer, Integer> dangerLevelStatMap = MybatisResultUtils.convertListToMap(result,
                Convert::toInt, Convert::toInt);

        System.out.println(JSON.toJSONString(dangerLevelStatMap));

        // 验证结果
        assertThat(result).isNotNull();
    }
}