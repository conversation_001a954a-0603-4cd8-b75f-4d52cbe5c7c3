package com.allin.silas.map.app.manager.impl;

import com.allin.silas.map.app.entity.MapRegion;
import com.allin.silas.map.app.manager.MapRegionManager;
import com.allin.silas.map.infra.repository.MapRegionMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 验证 MapRegionManagerImpl 中的缓存行为是否生效
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
public class MapRegionManagerImplTest {

    @Autowired
    private MapRegionManager mapRegionManager;

    @MockitoBean
    private MapRegionMapper mapRegionMapperMock;

    /**
     * 测试：当使用 String id 查询时，缓存是否生效
     */
    @Test
    void testListNameFromCacheById_CacheWorks() {
        String id = "1";
        String expectedName = "Region A";

        // 模拟 mapper 返回数据
        MapRegion mockRegion = new MapRegion();
        mockRegion.setId(id);
        mockRegion.setRegionName(expectedName);
        when(mapRegionMapperMock.selectById(id)).thenReturn(mockRegion);

        // 第一次调用：应查询数据库
        String result1 = mapRegionManager.listNameFromCacheById(id);
        assertEquals(expectedName, result1);
        verify(mapRegionMapperMock, times(1)).selectById(id);

        // 第二次调用：应从缓存获取，不调用 mapper
        String result2 = mapRegionManager.listNameFromCacheById(id);
        assertEquals(expectedName, result2);
        verify(mapRegionMapperMock, times(1)).selectById(id); // 只调用一次
    }

    /**
     * 测试：当使用 List<String> ids 查询时，缓存是否生效
     */
    @Test
    void testListNameFromCacheByIds_CacheWorks() {
        List<String> ids = List.of("1", "2", "3");
        List<MapRegion> mockRegions = List.of(
                createMapRegion("1", "Region 1"),
                createMapRegion("2", "Region 2"),
                createMapRegion("3", "Region 3")
        );

        when(mapRegionMapperMock.selectByIds(ids)).thenReturn(mockRegions);

        // 第一次调用：应查询数据库
        List<String> result1 = mapRegionManager.listNameFromCacheByIds(ids);
        assertNotNull(result1);
        assertEquals(3, result1.size());
        assertTrue(result1.contains("Region 1"));
        verify(mapRegionMapperMock, times(1)).selectByIds(ids);

        // 第二次调用：应从缓存获取，不调用 mapper
        List<String> result2 = mapRegionManager.listNameFromCacheByIds(ids);
        assertNotNull(result2);
        assertEquals(3, result2.size());
        verify(mapRegionMapperMock, times(1)).selectByIds(ids); // 只调用一次
    }

    /**
     * 辅助方法：创建 MapRegion 实例
     */
    private MapRegion createMapRegion(String id, String name) {
        MapRegion region = new MapRegion();
        region.setId(id);
        region.setRegionName(name);
        return region;
    }
}