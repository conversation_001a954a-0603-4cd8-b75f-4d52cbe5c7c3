package com.allin.silas.map.app.manager.impl;

import com.allin.silas.map.adapter.dto.AddMapClearanceAreaDto;
import com.allin.silas.map.app.entity.MapClearanceArea;
import com.allin.silas.map.app.entity.MapRunway;
import com.allin.silas.map.infra.repository.MapClearanceAreaMapper;
import lombok.extern.slf4j.Slf4j;
import org.geotools.data.geojson.GeoJSONReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Polygon;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * MapClearanceAreaManagerImpl 测试类
 */
@Slf4j
class MapClearanceAreaManagerImplTest {

    private MapClearanceAreaManagerImpl mapClearanceAreaManager;

    private MapClearanceAreaMapper clearanceAreaMapperMock;

    @BeforeEach
    void setUp() {
        clearanceAreaMapperMock = Mockito.mock(MapClearanceAreaMapper.class);
        mapClearanceAreaManager = new MapClearanceAreaManagerImpl(clearanceAreaMapperMock);
    }

    /**
     * 测试正常生成净空区的情况
     */
    @Test
    void testGenerateAreas() {
        // 创建模拟对象
        MapRunway mapRunwayMock = mock(MapRunway.class);
        // 设置模拟行为
        when(mapRunwayMock.areaGeometry()).thenReturn((Polygon) GeoJSONReader.parseGeometry("""
                {"type":"Polygon","coordinates":[[[112.964981,28.190618],[112.965064,28.193325],[112.966186,28.193298],[112.966103,28.190592],[112.964981,28.190618]]]}
                """));
        when(mapRunwayMock.centerLineGeometry()).thenReturn((LineString) GeoJSONReader.parseGeometry("""
                {
                        "type": "LineString",
                        "coordinates": [
                          [112.96502233779762, 28.19197150971868],
                          [112.9661447544159, 28.191944639101695]
                        ]
                      }
                """));

        // 创建测试 DTO 列表
        List<AddMapClearanceAreaDto> areaDtos = new ArrayList<>();
        AddMapClearanceAreaDto dto1 = new AddMapClearanceAreaDto();
        dto1.setAreaCode(1);
        dto1.setAreaLength(3000);
        dto1.setDisplayConfig("test-config");
        AddMapClearanceAreaDto dto2 = new AddMapClearanceAreaDto();
        dto2.setAreaCode(2);
        dto2.setAreaLength(3600);
        dto2.setDisplayConfig("test-config");
        AddMapClearanceAreaDto dto3 = new AddMapClearanceAreaDto();
        dto3.setAreaCode(3);
        dto3.setAreaLength(8400);
        dto3.setDisplayConfig("test-config");
        areaDtos.add(dto1);
        areaDtos.add(dto2);
        areaDtos.add(dto3);

        // 执行测试
        final List<MapClearanceArea> clearanceAreas = mapClearanceAreaManager.generateAreas(mapRunwayMock, areaDtos);
        for (MapClearanceArea clearanceArea : clearanceAreas) {
            log.info("净空区段列表：{}", clearanceArea.getAreaCoord());
        }
    }
}