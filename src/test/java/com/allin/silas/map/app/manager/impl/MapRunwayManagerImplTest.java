package com.allin.silas.map.app.manager.impl;

import lombok.extern.slf4j.Slf4j;
import org.geotools.data.geojson.GeoJSONReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.LineString;

import java.lang.reflect.Method;

@Slf4j
class MapRunwayManagerImplTest {

    private MapRunwayManagerImpl manager;

    private Method calculateCenterCoordMethod;

    private Method calculateAreaCoordMethod;

    @BeforeEach
    void setUp() throws NoSuchMethodException {
        // 初始化被测类
        manager = new MapRunwayManagerImpl(null); // runwayMapper 不参与当前测试逻辑，设为 null

        // 获取私有方法
        calculateCenterCoordMethod = MapRunwayManagerImpl.class.getDeclaredMethod(
                "calculateCenterCoord", Geometry.class);
        calculateCenterCoordMethod.setAccessible(true); // 允许访问私有方法

        calculateAreaCoordMethod = MapRunwayManagerImpl.class.getDeclaredMethod(
                "calculateAreaCoord", LineString.class, Integer.class);
        calculateAreaCoordMethod.setAccessible(true);
    }

    /**
     * 测试计算中心点
     */
    @Test
    void testCalculateCenterCoord() throws Exception {
        // 创建 mock Geometry
        Geometry mockGeometry = GeoJSONReader.parseGeometry("""
                {
                        "type": "LineString",
                        "coordinates": [
                          [112.96502233779762, 28.19197150971868],
                          [112.9661447544159, 28.191944639101695]
                        ]
                      }
                """);

        // 调用私有方法
        String result = (String) calculateCenterCoordMethod.invoke(manager, mockGeometry);
        log.info("输出内容: {}", result);
    }

    /**
     * 测试计算跑道区域
     */
    @Test
    void testCalculateAreaCoord() throws Exception {
        // 创建 mock Geometry
        Geometry mockGeometry = GeoJSONReader.parseGeometry("""
                {
                        "type": "LineString",
                        "coordinates": [
                          [112.96502233779762, 28.19197150971868],
                          [112.9661447544159, 28.191944639101695]
                        ]
                      }
                """);

        // 调用私有方法
        String result = (String) calculateAreaCoordMethod.invoke(manager, mockGeometry, 300);
        log.info("输出内容: {}", result);
    }
}
