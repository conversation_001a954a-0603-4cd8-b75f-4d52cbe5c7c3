package com.allin.silas.visual.app.enums;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 碰撞可能性枚举测试类
 *
 * <AUTHOR>
 * @since 2025/7/12
 */
class CollisionPossibilityEnumsTest {

    @ParameterizedTest
    @CsvSource({
            "0, 5, 极有可能",
            "15, 5, 极有可能",
            "30, 5, 极有可能",
            "31, 4, 很可能",
            "65, 4, 很可能",
            "100, 4, 很可能",
            "101, 3, 可能的",
            "200, 3, 可能的",
            "300, 3, 可能的",
            "301, 2, 不太可能",
            "400, 2, 不太可能",
            "500, 2, 不太可能",
            "501, 1, 极不可能",
            "1000, 1, 极不可能",
            "10000, 1, 极不可能"
    })
    void testCalculateByDistance(double distance, int expectedCode, String expectedDesc) {
        CollisionPossibilityEnums result = CollisionPossibilityEnums.calculateByDistance(distance);

        assertNotNull(result, "结果不应该为null");
        assertEquals(expectedCode, result.getCode(), "等级代码不匹配");
        assertEquals(expectedDesc, result.getDesc(), "等级描述不匹配");
    }


}
