package com.allin.silas.visual.infra.task;

import com.allin.silas.visual.adapter.vo.VisualTargetOriginalMergedVo;
import com.allin.silas.visual.app.entity.VisualTargetMergedInfo;
import com.allin.silas.visual.infra.repository.VisualTargetMergedInfoMapper;
import com.allin.silas.visual.infra.repository.VisualTargetOriginalInfoMapper;
import com.allin.silas.visual.utils.VisualDetectUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VisualTimedMergedTask 测试类
 */
@ExtendWith(MockitoExtension.class)
class VisualTimedMergedTaskTest {

    @Mock
    private VisualTargetOriginalInfoMapper visualTargetOriginalInfoMapper;

    @Mock
    private VisualTargetMergedInfoMapper visualTargetMergedInfoMapper;

    @Mock
    private VisualDetectUtils visualDetectUtils;

    private VisualTimedMergedTask visualTimedMergedTask;

    @BeforeEach
    void setUp() {
        visualTimedMergedTask = new VisualTimedMergedTask(
                visualTargetOriginalInfoMapper,
                visualTargetMergedInfoMapper,
                visualDetectUtils
        );
    }

    @Test
    void testUpdateStatisticalValues() throws Exception {
        // 准备测试数据
        VisualTargetMergedInfo mergedInfo = new VisualTargetMergedInfo();
        List<VisualTargetOriginalMergedVo> originalInfos = createTestOriginalInfos();

        // 使用反射调用私有方法
        Method method = VisualTimedMergedTask.class.getDeclaredMethod(
                "updateStatisticalValues", 
                VisualTargetMergedInfo.class, 
                List.class
        );
        method.setAccessible(true);
        method.invoke(visualTimedMergedTask, mergedInfo, originalInfos);

        // 验证结果
        assertNotNull(mergedInfo.getMinAzimuth());
        assertNotNull(mergedInfo.getMaxAzimuth());
        assertEquals(10.0f, mergedInfo.getMinAzimuth());
        assertEquals(30.0f, mergedInfo.getMaxAzimuth());

        assertNotNull(mergedInfo.getMinHeight());
        assertNotNull(mergedInfo.getMaxHeight());
        assertEquals(100.0f, mergedInfo.getMinHeight());
        assertEquals(300.0f, mergedInfo.getMaxHeight());

        assertNotNull(mergedInfo.getMaxTargetCount());
        assertEquals(5, mergedInfo.getMaxTargetCount());

        assertNotNull(mergedInfo.getMaxConfidenceLevel());
        assertEquals(0.95f, mergedInfo.getMaxConfidenceLevel());
    }

    @Test
    void testUpdateLocationRange() throws Exception {
        // 准备测试数据
        VisualTargetMergedInfo mergedInfo = new VisualTargetMergedInfo();
        List<VisualTargetOriginalMergedVo> originalInfos = createTestOriginalInfos();

        // 使用反射调用私有方法
        Method method = VisualTimedMergedTask.class.getDeclaredMethod(
                "updateLocationRange", 
                VisualTargetMergedInfo.class, 
                List.class
        );
        method.setAccessible(true);
        method.invoke(visualTimedMergedTask, mergedInfo, originalInfos);

        // 验证结果
        assertNotNull(mergedInfo.getStartLongitude());
        assertNotNull(mergedInfo.getStartLatitude());
        assertNotNull(mergedInfo.getEndLongitude());
        assertNotNull(mergedInfo.getEndLatitude());

        // 开始位置应该是最早时间的位置
        assertEquals(116.1, mergedInfo.getStartLongitude());
        assertEquals(39.1, mergedInfo.getStartLatitude());

        // 结束位置应该是最新时间的位置
        assertEquals(116.3, mergedInfo.getEndLongitude());
        assertEquals(39.3, mergedInfo.getEndLatitude());
    }

    @Test
    void testUpdateImageUrls() throws Exception {
        // 准备测试数据
        VisualTargetMergedInfo mergedInfo = new VisualTargetMergedInfo();
        List<VisualTargetOriginalMergedVo> originalInfos = createTestOriginalInfos();

        // 使用反射调用私有方法
        Method method = VisualTimedMergedTask.class.getDeclaredMethod(
                "updateImageUrls", 
                VisualTargetMergedInfo.class, 
                List.class
        );
        method.setAccessible(true);
        method.invoke(visualTimedMergedTask, mergedInfo, originalInfos);

        // 验证结果
        assertNotNull(mergedInfo.getStartImgUrl());
        assertNotNull(mergedInfo.getEndImgUrl());
        assertEquals("http://example.com/img1.jpg", mergedInfo.getStartImgUrl());
        assertEquals("http://example.com/img3.jpg", mergedInfo.getEndImgUrl());
    }

    /**
     * 创建测试用的原始数据
     */
    private List<VisualTargetOriginalMergedVo> createTestOriginalInfos() {
        LocalDateTime baseTime = LocalDateTime.of(2025, 1, 1, 10, 0, 0);

        VisualTargetOriginalMergedVo info1 = new VisualTargetOriginalMergedVo();
        info1.setId("1");
        info1.setBackendBatchNumber("batch001");
        info1.setLongitude(116.1);
        info1.setLatitude(39.1);
        info1.setAzimuth(10.0f);
        info1.setPitch(5.0f);
        info1.setDistance(1000.0f);
        info1.setHeight(100.0f);
        info1.setSpeed(50.0f);
        info1.setWingSpan(2.0f);
        info1.setFlightDirection(90.0f);
        info1.setArea(10.0f);
        info1.setConfidenceLevel(0.8f);
        info1.setTargetCount(3);
        info1.setRunwayDistance(500.0f);
        info1.setRunwayCenterDistance(600.0f);
        info1.setImgUrl("http://example.com/img1.jpg");
        info1.setCreatedTime(baseTime);
        info1.setDetectSubType("鸟类");

        VisualTargetOriginalMergedVo info2 = new VisualTargetOriginalMergedVo();
        info2.setId("2");
        info2.setBackendBatchNumber("batch001");
        info2.setLongitude(116.2);
        info2.setLatitude(39.2);
        info2.setAzimuth(20.0f);
        info2.setPitch(10.0f);
        info2.setDistance(1200.0f);
        info2.setHeight(200.0f);
        info2.setSpeed(60.0f);
        info2.setWingSpan(2.5f);
        info2.setFlightDirection(95.0f);
        info2.setArea(15.0f);
        info2.setConfidenceLevel(0.9f);
        info2.setTargetCount(4);
        info2.setRunwayDistance(400.0f);
        info2.setRunwayCenterDistance(500.0f);
        info2.setImgUrl("http://example.com/img2.jpg");
        info2.setCreatedTime(baseTime.plusMinutes(1));
        info2.setDetectSubType("鸟类");

        VisualTargetOriginalMergedVo info3 = new VisualTargetOriginalMergedVo();
        info3.setId("3");
        info3.setBackendBatchNumber("batch001");
        info3.setLongitude(116.3);
        info3.setLatitude(39.3);
        info3.setAzimuth(30.0f);
        info3.setPitch(15.0f);
        info3.setDistance(1500.0f);
        info3.setHeight(300.0f);
        info3.setSpeed(70.0f);
        info3.setWingSpan(3.0f);
        info3.setFlightDirection(100.0f);
        info3.setArea(20.0f);
        info3.setConfidenceLevel(0.95f);
        info3.setTargetCount(5);
        info3.setRunwayDistance(300.0f);
        info3.setRunwayCenterDistance(400.0f);
        info3.setImgUrl("http://example.com/img3.jpg");
        info3.setCreatedTime(baseTime.plusMinutes(2));
        info3.setDetectSubType("鸟类");

        return Arrays.asList(info1, info2, info3);
    }
}
