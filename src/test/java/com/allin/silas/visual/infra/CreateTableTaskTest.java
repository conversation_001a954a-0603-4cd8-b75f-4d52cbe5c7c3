package com.allin.silas.visual.infra;

import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.visual.infra.task.VisualTimedCreateTableTask;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalDate;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
class CreateTableTaskTest {

    private static final String TEST_TABLE_NAME = TableNameUtils.getVisualTargetMergedInfo(LocalDate.now());

    @Autowired
    private VisualTimedCreateTableTask task;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 判断指定表名是否存在
     *
     * @param tableName 表名
     * @return true 表示存在
     */
    private boolean tableExists(String tableName) {
        try {
            jdbcTemplate.execute("SELECT 1 FROM " + tableName + " LIMIT 1");
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 测试 createTable 方法是否能成功创建表
     */
    @Test
    void testCreateTable_shouldCreateNewTableSuccessfully() {
        // Act: 执行建表
        task.createTable();

        // Assert: 验证表是否存在
        boolean exists = tableExists(TEST_TABLE_NAME);
        assertThat(exists).isTrue();
    }
}