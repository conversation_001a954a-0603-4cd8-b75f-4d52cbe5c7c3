package com.allin.silas.common.util;

import org.geotools.data.geojson.GeoJSONReader;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Geometry;

/**
 * geotools 谓词运算测试
 *
 * <AUTHOR>
 * @since 2025/5/28
 */
public class GeoToolsPredicateTest {

    @Test
    public void testGeoJsonPointInPolygon() throws Exception {
        // GeoJSON 多边形字符串
        String polygonGeoJson = """
                {
                  "type": "Polygon",
                  "coordinates": [
                    [
                      [
                        112.9322007767465,
                        28.211381344761126
                      ],
                      [
                        112.93220068431452,
                        28.212552362331714
                      ],
                      [
                        112.9334663145689,
                        28.212550613897818
                      ],
                      [
                        112.93346640702251,
                        28.211379596320803
                      ],
                      [
                        112.9322007767465,
                        28.211381344761126
                      ]
                    ]
                  ]
                }
                """;

        // GeoJSON 点字符串
        String pointGeoJson1 = """
                {
                  "type": "Point",
                  "coordinates": [
                    112.93283356479812,
                    28.21184548622267
                  ]
                }
                """;
        String pointGeoJson2 = """
                {
                  "type": "Point",
                  "coordinates": [
                    112.93353624704048,
                    28.211720503073174
                  ]
                }
                """;

        // 解析 GeoJSON
        final Geometry polygon = GeoJSONReader.parseGeometry(polygonGeoJson);
        final Geometry point1 = GeoJSONReader.parseGeometry(pointGeoJson1);
        final Geometry point2 = GeoJSONReader.parseGeometry(pointGeoJson2);

        // 判断点是否在多边形中
        boolean isInside1 = polygon.contains(point1);
        boolean isInside2 = polygon.contains(point2);

        System.out.println("点1是否在GeoJSON多边形内：" + isInside1);
        System.out.println("点2是否在GeoJSON多边形内：" + isInside2);
    }
}
