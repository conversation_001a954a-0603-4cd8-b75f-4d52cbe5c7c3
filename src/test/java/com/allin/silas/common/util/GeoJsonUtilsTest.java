package com.allin.silas.common.util;

import com.allin.silas.common.util.geo.GeoJsonUtils;
import com.allin.view.base.exception.service.ValidationFailureException;
import org.geotools.api.feature.simple.SimpleFeature;
import org.geotools.data.geojson.GeoJSONReader;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Geometry;

import static org.junit.jupiter.api.Assertions.*;

class GeoJsonUtilsTest {

    @Test
    void isGeoJsonGeometry_validPoint_shouldPass() {
        String geoJson = """
                {
                  "type": "Point",
                  "coordinates": [
                    112.93283356479812,
                    28.21184548622267
                  ]
                }
                """;
        assertDoesNotThrow(() -> GeoJsonUtils.checkGeoJsonGeometry(geoJson));
    }

    @Test
    void isGeoJsonGeometry_invalidType_shouldThrow() {
        String geoJson = """
                {
                  "type": "Point1",
                  "coordinates": [
                    112.93283356479812,
                    28.21184548622267
                  ]
                }
                """;
        assertThrows(ValidationFailureException.class, () -> GeoJsonUtils.checkGeoJsonGeometry(geoJson));
    }

    @Test
    void isGeoJsonGeometry_invalidCoordinates_shouldThrow() {
        String geoJson = """
                {
                  "type": "Point",
                  "coordinates": [
                    "invalid",
                    28.21184548622267
                  ]
                }
                """;
        assertThrows(ValidationFailureException.class, () -> GeoJsonUtils.checkGeoJsonGeometry(geoJson));
    }

    @Test
    void isGeoJsonGeometry_nullInput_shouldNotThrow() {
        assertDoesNotThrow(() -> GeoJsonUtils.checkGeoJsonGeometry(null));
    }

    @Test
    void isGeoJsonGeometry_emptyString_shouldThrow() {
        assertThrows(ValidationFailureException.class, () -> GeoJsonUtils.checkGeoJsonGeometry(""));
    }

    @Test
    void isGeoJsonGeometry_withFieldName_shouldContainFieldNameInMessage() {
        String geoJson = """
                {
                  "type": "Point1",
                  "coordinates": [
                    112.93283356479812,
                    28.21184548622267
                  ]
                }
                """;
        ValidationFailureException ex = assertThrows(ValidationFailureException.class, () ->
                GeoJsonUtils.checkGeoJsonGeometry(geoJson, "location")
        );
        assertTrue(ex.getMessage().contains("location"));
    }

    /**
     * 测试解析 geojson
     */
    @Test
    public void testGeoJson() {
        // GeoJSON 点字符串
        String geojson = """
                {
                  "type": "FeatureCollection",
                  "features": [
                    {
                      "type": "Feature",
                      "properties": {},
                      "geometry": {
                        "type": "Point",
                        "coordinates": [112.93283356479812, 28.21184548622267]
                      }
                    },
                    {
                      "type": "Feature",
                      "properties": {},
                      "geometry": {
                        "type": "Polygon",
                        "coordinates": [
                          [
                            [112.9322007767465, 28.211381344761126],
                            [112.93220068431452, 28.212552362331714],
                            [112.9334663145689, 28.212550613897818],
                            [112.93346640702251, 28.211379596320803],
                            [112.9322007767465, 28.211381344761126]
                          ]
                        ]
                      }
                    }
                  ]
                }
                
                """;
        final SimpleFeatureCollection collection = GeoJSONReader.parseFeatureCollection(geojson);
        try (SimpleFeatureIterator features = collection.features()) {
            while (features.hasNext()) {
                SimpleFeature feature = features.next();
                System.out.println(((Geometry) feature.getDefaultGeometry())
                        .getCoordinate());
            }
        }
    }
}
