package com.allin.silas.common.util;

import org.geotools.api.referencing.operation.TransformException;
import org.geotools.data.geojson.GeoJSONReader;
import org.geotools.data.geojson.GeoJSONWriter;
import org.geotools.geometry.Position2D;
import org.geotools.geometry.jts.JTS;
import org.geotools.referencing.GeodeticCalculator;
import org.geotools.referencing.crs.DefaultGeographicCRS;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.operation.distance.DistanceOp;

/**
 * class GeodeticCalculatorTest
 *
 * <AUTHOR>
 * @since 2025/5/28
 */
public class GeodeticCalculatorTest {

    /**
     * 两点之间的距离
     */
    @Test
    public void testGeoJsonCalculateDistance() throws TransformException {
        // GeoJSON 点字符串
        String pointGeoJson1 = """
                {
                  "type": "Point",
                  "coordinates": [
                    112.93283356479812,
                    28.21184548622267
                  ]
                }
                """;
        String pointGeoJson2 = """
                {
                  "type": "Point",
                  "coordinates": [
                    112.93353624704048,
                    28.211720503073174
                  ]
                }
                """;
        final Geometry point1 = GeoJSONReader.parseGeometry(pointGeoJson1);
        final Geometry point2 = GeoJSONReader.parseGeometry(pointGeoJson2);

        final double distance = JTS.orthodromicDistance(point1.getCoordinate(), point2.getCoordinate(), DefaultGeographicCRS.WGS84);


        System.out.printf("两点之间的大地距离为：%.2f 米%n", distance);
    }

    /**
     * 测试计算点到多边形的最短距离
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    @Test
    public void testPointToPolygonShortestDistance() throws Exception {
        // GeoJSON 多边形字符串
        String polygonGeoJson = """
                {
                  "type": "Polygon",
                  "coordinates": [
                    [
                      [
                        112.9322007767465,
                        28.211381344761126
                      ],
                      [
                        112.93220068431452,
                        28.212552362331714
                      ],
                      [
                        112.9334663145689,
                        28.212550613897818
                      ],
                      [
                        112.93346640702251,
                        28.211379596320803
                      ],
                      [
                        112.9322007767465,
                        28.211381344761126
                      ]
                    ]
                  ]
                }
                """;
        String pointGeoJson = """
                {
                  "type": "Point",
                  "coordinates": [
                    112.93353624704048,
                    28.211720503073174
                  ]
                }
                """;

        // 解析 GeoJSON
        final Geometry polygon = GeoJSONReader.parseGeometry(polygonGeoJson);
        final Geometry point = GeoJSONReader.parseGeometry(pointGeoJson);
        GeodeticCalculator gc = new GeodeticCalculator(DefaultGeographicCRS.WGS84);
        gc.setStartingPosition(JTS.toDirectPosition(DistanceOp.nearestPoints(polygon, point)[0], DefaultGeographicCRS.WGS84));
        gc.setDestinationPosition(JTS.toDirectPosition(point.getCoordinate(), DefaultGeographicCRS.WGS84));
        final double orthodromicDistance = gc.getOrthodromicDistance();
        System.out.printf("点到多边形之间的大地距离为：%.2f 米%n", orthodromicDistance);
    }

    /**
     * 测试计算点到线的最短距离
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    @Test
    public void testPointToLineStringShortestDistance() throws Exception {
        // GeoJSON 多边形字符串
        String lineStringGeoJson = """
                {
                        "type": "LineString",
                        "coordinates": [
                          [112.83102000418431, 28.412205159964145],
                          [112.83452139268579, 28.412211737640757]
                        ]
                      }
                """;
        String pointGeoJson = """
                {
                        "type": "Point",
                        "coordinates": [112.83259488261004, 28.412230118962604]
                      }
                """;

        // 解析 GeoJSON
        final Geometry lineString = GeoJSONReader.parseGeometry(lineStringGeoJson);
        final Geometry point = GeoJSONReader.parseGeometry(pointGeoJson);
        final Coordinate nearestPoint = DistanceOp.nearestPoints(lineString, point)[0];
        System.out.printf("最近的点: %s \n", GeoJSONWriter.toGeoJSON(JTS.toGeometry(new Position2D(nearestPoint.x, nearestPoint.y))));
        System.out.printf("点到线之间的大地距离为：%.2f 米%n", JTS.orthodromicDistance(point.getCoordinate(), nearestPoint, DefaultGeographicCRS.WGS84));
    }
}
